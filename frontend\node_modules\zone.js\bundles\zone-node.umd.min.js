"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},__assign.apply(this,arguments)};
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){var e=globalThis;function t(t){return(e.__Zone_symbol_prefix||"__zone_symbol__")+t}function n(){var n=e.performance;function r(e){n&&n.mark&&n.mark(e)}function o(e,t){n&&n.measure&&n.measure(e,t)}r("Zone");var i,a=function(){function n(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new c(this,this._parent&&this._parent._zoneDelegate,t)}return n.assertZonePatched=function(){if(e.Promise!==P.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")},Object.defineProperty(n,"root",{get:function(){for(var e=n.current;e.parent;)e=e.parent;return e},enumerable:!1,configurable:!0}),Object.defineProperty(n,"current",{get:function(){return z.zone},enumerable:!1,configurable:!0}),Object.defineProperty(n,"currentTask",{get:function(){return O},enumerable:!1,configurable:!0}),n.__load_patch=function(i,a,s){if(void 0===s&&(s=!1),P.hasOwnProperty(i)){var c=!0===e[t("forceDuplicateZoneCheck")];if(!s&&c)throw Error("Already loaded patch: "+i)}else if(!e["__Zone_disable_"+i]){var u="Zone:"+i;r(u),P[i]=a(e,n,j),o(u,u)}},Object.defineProperty(n.prototype,"parent",{get:function(){return this._parent},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),n.prototype.get=function(e){var t=this.getZoneWith(e);if(t)return t._properties[e]},n.prototype.getZoneWith=function(e){for(var t=this;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null},n.prototype.fork=function(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)},n.prototype.wrap=function(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);var n=this._zoneDelegate.intercept(this,e,t),r=this;return function(){return r.runGuarded(n,this,arguments,t)}},n.prototype.run=function(e,t,n,r){z={parent:z,zone:this};try{return this._zoneDelegate.invoke(this,e,t,n,r)}finally{z=z.parent}},n.prototype.runGuarded=function(e,t,n,r){void 0===t&&(t=null),z={parent:z,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,n,r)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{z=z.parent}},n.prototype.runTask=function(e,t,n){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||g).name+"; Execution: "+this.name+")");var r=e,o=e.type,i=e.data,a=void 0===i?{}:i,s=a.isPeriodic,c=void 0!==s&&s,u=a.isRefreshable,l=void 0!==u&&u;if(e.state!==y||o!==D&&o!==S){var f=e.state!=b;f&&r._transitionTo(b,T);var h=O;O=r,z={parent:z,zone:this};try{o!=S||!e.data||c||l||(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,r,t,n)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{var p=e.state;if(p!==y&&p!==E)if(o==D||c||l&&p===m)f&&r._transitionTo(T,b,m);else{var v=r._zoneDelegates;this._updateTaskCount(r,-1),f&&r._transitionTo(y,b,y),l&&(r._zoneDelegates=v)}z=z.parent,O=h}}},n.prototype.scheduleTask=function(e){if(e.zone&&e.zone!==this)for(var t=this;t;){if(t===e.zone)throw Error("can not reschedule task to ".concat(this.name," which is descendants of the original zone ").concat(e.zone.name));t=t.parent}e._transitionTo(m,y);var n=[];e._zoneDelegates=n,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(t){throw e._transitionTo(E,m,y),this._zoneDelegate.handleError(this,t),t}return e._zoneDelegates===n&&this._updateTaskCount(e,1),e.state==m&&e._transitionTo(T,m),e},n.prototype.scheduleMicroTask=function(e,t,n,r){return this.scheduleTask(new u(Z,e,t,n,r,void 0))},n.prototype.scheduleMacroTask=function(e,t,n,r,o){return this.scheduleTask(new u(S,e,t,n,r,o))},n.prototype.scheduleEventTask=function(e,t,n,r,o){return this.scheduleTask(new u(D,e,t,n,r,o))},n.prototype.cancelTask=function(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||g).name+"; Execution: "+this.name+")");if(e.state===T||e.state===b){e._transitionTo(w,T,b);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(E,w),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(y,w),e.runCount=-1,e}},n.prototype._updateTaskCount=function(e,t){var n=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(var r=0;r<n.length;r++)n[r]._updateTaskCount(e.type,t)},n.__symbol__=t,n}(),s={name:"",onHasTask:function(e,t,n,r){return e.hasTask(n,r)},onScheduleTask:function(e,t,n,r){return e.scheduleTask(n,r)},onInvokeTask:function(e,t,n,r,o,i){return e.invokeTask(n,r,o,i)},onCancelTask:function(e,t,n,r){return e.cancelTask(n,r)}},c=function(){function e(e,t,n){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this._zone=e,this._parentDelegate=t,this._forkZS=n&&(n&&n.onFork?n:t._forkZS),this._forkDlgt=n&&(n.onFork?t:t._forkDlgt),this._forkCurrZone=n&&(n.onFork?this._zone:t._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:t._interceptZS),this._interceptDlgt=n&&(n.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this._zone:t._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:t._invokeZS),this._invokeDlgt=n&&(n.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this._zone:t._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:t._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this._zone:t._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:t._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this._zone:t._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:t._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this._zone:t._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:t._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this._zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;var r=n&&n.onHasTask;(r||t&&t._hasTaskZS)&&(this._hasTaskZS=r?n:s,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,n.onScheduleTask||(this._scheduleTaskZS=s,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this._zone),n.onInvokeTask||(this._invokeTaskZS=s,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this._zone),n.onCancelTask||(this._cancelTaskZS=s,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this._zone))}return Object.defineProperty(e.prototype,"zone",{get:function(){return this._zone},enumerable:!1,configurable:!0}),e.prototype.fork=function(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new a(e,t)},e.prototype.intercept=function(e,t,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,n):t},e.prototype.invoke=function(e,t,n,r,o){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,n,r,o):t.apply(n,r)},e.prototype.handleError=function(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)},e.prototype.scheduleTask=function(e,t){var n=t;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),(n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t))||(n=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=Z)throw new Error("Task is missing scheduleFn.");_(t)}return n},e.prototype.invokeTask=function(e,t,n,r){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,n,r):t.callback.apply(n,r)},e.prototype.cancelTask=function(e,t){var n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");n=t.cancelFn(t)}return n},e.prototype.hasTask=function(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(t){this.handleError(e,t)}},e.prototype._updateTaskCount=function(e,t){var n=this._taskCounts,r=n[e],o=n[e]=r+t;if(o<0)throw new Error("More tasks executed then were scheduled.");0!=r&&0!=o||this.hasTask(this._zone,{microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:e})},e}(),u=function(){function t(n,r,o,i,a,s){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=n,this.source=r,this.data=i,this.scheduleFn=a,this.cancelFn=s,!o)throw new Error("callback is not defined");this.callback=o;var c=this;this.invoke=n===D&&i&&i.useG?t.invokeTask:function(){return t.invokeTask.call(e,c,this,arguments)}}return t.invokeTask=function(e,t,n){e||(e=this),C++;try{return e.runCount++,e.zone.runTask(e,t,n)}finally{1==C&&k(),C--}},Object.defineProperty(t.prototype,"zone",{get:function(){return this._zone},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),t.prototype.cancelScheduleRequest=function(){this._transitionTo(y,m)},t.prototype._transitionTo=function(e,t,n){if(this._state!==t&&this._state!==n)throw new Error("".concat(this.type," '").concat(this.source,"': can not transition to '").concat(e,"', expecting state '").concat(t,"'").concat(n?" or '"+n+"'":"",", was '").concat(this._state,"'."));this._state=e,e==y&&(this._zoneDelegates=null)},t.prototype.toString=function(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)},t.prototype.toJSON=function(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}},t}(),l=t("setTimeout"),f=t("Promise"),h=t("then"),p=[],v=!1;function d(t){if(i||e[f]&&(i=e[f].resolve(0)),i){var n=i[h];n||(n=i.then),n.call(i,t)}else e[l](t,0)}function _(e){0===C&&0===p.length&&d(k),e&&p.push(e)}function k(){if(!v){for(v=!0;p.length;){var e=p;p=[];for(var t=0;t<e.length;t++){var n=e[t];try{n.zone.runTask(n,null,null)}catch(e){j.onUnhandledError(e)}}}j.microtaskDrainDone(),v=!1}}var g={name:"NO ZONE"},y="notScheduled",m="scheduling",T="scheduled",b="running",w="canceling",E="unknown",Z="microTask",S="macroTask",D="eventTask",P={},j={symbol:t,currentZoneFrame:function(){return z},onUnhandledError:I,microtaskDrainDone:I,scheduleMicroTask:_,showUncaughtError:function(){return!a[t("ignoreConsoleErrorUncaughtError")]},patchEventTarget:function(){return[]},patchOnProperties:I,patchMethod:function(){return I},bindArguments:function(){return[]},patchThen:function(){return I},patchMacroTask:function(){return I},patchEventPrototype:function(){return I},isIEOrEdge:function(){return!1},getGlobalObjects:function(){},ObjectDefineProperty:function(){return I},ObjectGetOwnPropertyDescriptor:function(){},ObjectCreate:function(){},ArraySlice:function(){return[]},patchClass:function(){return I},wrapWithCurrentZone:function(){return I},filterProperties:function(){return[]},attachOriginToPatched:function(){return I},_redefineProperty:function(){return I},patchCallbacks:function(){return I},nativeScheduleMicroTask:d},z={parent:null,zone:new a(null,null)},O=null,C=0;function I(){}return o("Zone","Zone"),a}var r=Object.getOwnPropertyDescriptor,o=Object.defineProperty,i=Object.getPrototypeOf,a=Array.prototype.slice,s="true",c="false",u=t("");function l(e,t){return Zone.current.wrap(e,t)}function f(e,t,n,r,o){return Zone.current.scheduleMacroTask(e,t,n,r,o)}var h=t,p="undefined"!=typeof window,v=p?window:void 0,d=p&&v||globalThis;function _(e,t){for(var n=e.length-1;n>=0;n--)"function"==typeof e[n]&&(e[n]=l(e[n],t+"_"+n));return e}var k="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,g=!("nw"in d)&&void 0!==d.process&&"[object process]"===d.process.toString(),y=!g&&!k&&!(!p||!v.HTMLElement),m=void 0!==d.process&&"[object process]"===d.process.toString()&&!k&&!(!p||!v.HTMLElement),T={},b=h("enable_beforeunload"),w=function(e){if(e=e||d.event){var t=T[e.type];t||(t=T[e.type]=h("ON_PROPERTY"+e.type));var n,r=this||e.target||d,o=r[t];return y&&r===v&&"error"===e.type?!0===(n=o&&o.call(this,e.message,e.filename,e.lineno,e.colno,e.error))&&e.preventDefault():(n=o&&o.apply(this,arguments),"beforeunload"===e.type&&d[b]&&"string"==typeof n?e.returnValue=n:null==n||n||e.preventDefault()),n}};function E(e,t,n){var i=r(e,t);if(!i&&n&&r(n,t)&&(i={enumerable:!0,configurable:!0}),i&&i.configurable){var a=h("on"+t+"patched");if(!e.hasOwnProperty(a)||!e[a]){delete i.writable,delete i.value;var s=i.get,c=i.set,u=t.slice(2),l=T[u];l||(l=T[u]=h("ON_PROPERTY"+u)),i.set=function(t){var n=this;n||e!==d||(n=d),n&&("function"==typeof n[l]&&n.removeEventListener(u,w),null==c||c.call(n,null),n[l]=t,"function"==typeof t&&n.addEventListener(u,w,!1))},i.get=function(){var n=this;if(n||e!==d||(n=d),!n)return null;var r=n[l];if(r)return r;if(s){var o=s.call(this);if(o)return i.set.call(this,o),"function"==typeof n.removeAttribute&&n.removeAttribute(t),o}return null},o(e,t,i),e[a]=!0}}}function Z(e,t,n){if(t)for(var r=0;r<t.length;r++)E(e,"on"+t[r],n);else{var o=[];for(var i in e)"on"==i.slice(0,2)&&o.push(i);for(var a=0;a<o.length;a++)E(e,o[a],n)}}var S=!1;function D(e,t,n){for(var o=e;o&&!o.hasOwnProperty(t);)o=i(o);!o&&e[t]&&(o=e);var a=h(t),s=null;if(o&&(!(s=o[a])||!o.hasOwnProperty(a))&&(s=o[a]=o[t],function c(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}(o&&r(o,t)))){var u=n(s,a,t);o[t]=function(){return u(this,arguments)},z(o[t],s),S&&function e(t,n){"function"==typeof Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(t).forEach((function(e){var r=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(n,e,{get:function(){return t[e]},set:function(n){(!r||r.writable&&"function"==typeof r.set)&&(t[e]=n)},enumerable:!r||r.enumerable,configurable:!r||r.configurable})}))}(s,o[t])}return s}function P(e,t,n){var r=null;function o(e){var t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},r.apply(t.target,t.args),e}r=D(e,t,(function(e){return function(t,r){var i=n(t,r);return i.cbIdx>=0&&"function"==typeof r[i.cbIdx]?f(i.name,r[i.cbIdx],i,o):e.apply(t,r)}}))}function j(e,t,n){var r=null;function o(e){var t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},r.apply(t.target,t.args),e}r=D(e,t,(function(e){return function(t,r){var i=n(t,r);return i.cbIdx>=0&&"function"==typeof r[i.cbIdx]?Zone.current.scheduleMicroTask(i.name,r[i.cbIdx],i,o):e.apply(t,r)}}))}function z(e,t){e[h("OriginalDelegate")]=t}function O(e){return"function"==typeof e}function C(e){return"number"==typeof e}var I={useG:!0},A={},N={},x=new RegExp("^"+u+"(\\w+)(true|false)$"),R=h("propagationStopped");function M(e,t){var n=(t?t(e):e)+c,r=(t?t(e):e)+s,o=u+n,i=u+r;A[e]={},A[e][c]=o,A[e][s]=i}function L(e,t,n,r){var o=r&&r.add||"addEventListener",a=r&&r.rm||"removeEventListener",l=r&&r.listeners||"eventListeners",f=r&&r.rmAll||"removeAllListeners",p=h(o),v="."+o+":",d="prependListener",_="."+d+":",k=function(e,t,n){if(!e.isRemoved){var r,o=e.callback;"object"==typeof o&&o.handleEvent&&(e.callback=function(e){return o.handleEvent(e)},e.originalDelegate=o);try{e.invoke(e,t,[n])}catch(e){r=e}var i=e.options;return i&&"object"==typeof i&&i.once&&t[a].call(t,n.type,e.originalDelegate?e.originalDelegate:e.callback,i),r}};function y(n,r,o){if(r=r||e.event){var i=n||r.target||e,a=i[A[r.type][o?s:c]];if(a){var u=[];if(1===a.length)(h=k(a[0],i,r))&&u.push(h);else for(var l=a.slice(),f=0;f<l.length&&(!r||!0!==r[R]);f++){var h;(h=k(l[f],i,r))&&u.push(h)}if(1===u.length)throw u[0];var p=function(e){var n=u[e];t.nativeScheduleMicroTask((function(){throw n}))};for(f=0;f<u.length;f++)p(f)}}}var m=function(e){return y(this,e,!1)},T=function(e){return y(this,e,!0)};function b(t,n){if(!t)return!1;var r=!0;n&&void 0!==n.useG&&(r=n.useG);var k=n&&n.vh,y=!0;n&&void 0!==n.chkDup&&(y=n.chkDup);var b=!1;n&&void 0!==n.rt&&(b=n.rt);for(var w=t;w&&!w.hasOwnProperty(o);)w=i(w);if(!w&&t[o]&&(w=t),!w)return!1;if(w[p])return!1;var E,Z=n&&n.eventNameToString,S={},D=w[p]=w[o],P=w[h(a)]=w[a],j=w[h(l)]=w[l],O=w[h(f)]=w[f];n&&n.prepend&&(E=w[h(n.prepend)]=w[n.prepend]);var C=r?function(e){if(!S.isExisting)return D.call(S.target,S.eventName,S.capture?T:m,S.options)}:function(e){return D.call(S.target,S.eventName,e.invoke,S.options)},R=r?function(e){if(!e.isRemoved){var t=A[e.eventName],n=void 0;t&&(n=t[e.capture?s:c]);var r=n&&e.target[n];if(r)for(var o=0;o<r.length;o++)if(r[o]===e){r.splice(o,1),e.isRemoved=!0,e.removeAbortListener&&(e.removeAbortListener(),e.removeAbortListener=null),0===r.length&&(e.allRemoved=!0,e.target[n]=null);break}}if(e.allRemoved)return P.call(e.target,e.eventName,e.capture?T:m,e.options)}:function(e){return P.call(e.target,e.eventName,e.invoke,e.options)},L=(null==n?void 0:n.diff)||function(e,t){var n=typeof t;return"function"===n&&e.callback===t||"object"===n&&e.originalDelegate===t},H=Zone[h("UNPATCHED_EVENTS")],G=e[h("PASSIVE_EVENTS")],U=function(t,o,i,a,u,l){return void 0===u&&(u=!1),void 0===l&&(l=!1),function(){var f=this||e,h=arguments[0];n&&n.transferEventName&&(h=n.transferEventName(h));var p=arguments[1];if(!p)return t.apply(this,arguments);if(g&&"uncaughtException"===h)return t.apply(this,arguments);var v=!1;if("function"!=typeof p){if(!p.handleEvent)return t.apply(this,arguments);v=!0}if(!k||k(t,p,f,arguments)){var d=!!G&&-1!==G.indexOf(h),_=function n(e){if("object"==typeof e&&null!==e){var t=__assign({},e);return e.signal&&(t.signal=e.signal),t}return e}(function e(t,n){return n?"boolean"==typeof t?{capture:t,passive:!0}:t?"object"==typeof t&&!1!==t.passive?__assign(__assign({},t),{passive:!0}):t:{passive:!0}:t}(arguments[2],d)),m=null==_?void 0:_.signal;if(!(null==m?void 0:m.aborted)){if(H)for(var T=0;T<H.length;T++)if(h===H[T])return d?t.call(f,h,p,_):t.apply(this,arguments);var b=!!_&&("boolean"==typeof _||_.capture),w=!(!_||"object"!=typeof _)&&_.once,E=Zone.current,D=A[h];D||(M(h,Z),D=A[h]);var P,j=D[b?s:c],z=f[j],O=!1;if(z){if(O=!0,y)for(T=0;T<z.length;T++)if(L(z[T],p))return}else z=f[j]=[];var C=f.constructor.name,x=N[C];x&&(P=x[h]),P||(P=C+o+(Z?Z(h):h)),S.options=_,w&&(S.options.once=!1),S.target=f,S.capture=b,S.eventName=h,S.isExisting=O;var R=r?I:void 0;R&&(R.taskData=S),m&&(S.options.signal=void 0);var F=E.scheduleEventTask(P,p,R,i,a);if(m){S.options.signal=m;var U=function(){return F.zone.cancelTask(F)};t.call(m,"abort",U,{once:!0}),F.removeAbortListener=function(){return m.removeEventListener("abort",U)}}return S.target=null,R&&(R.taskData=null),w&&(S.options.once=!0),"boolean"!=typeof F.options&&(F.options=_),F.target=f,F.capture=b,F.eventName=h,v&&(F.originalDelegate=p),l?z.unshift(F):z.push(F),u?f:void 0}}}};return w[o]=U(D,v,C,R,b),E&&(w[d]=U(E,_,(function(e){return E.call(S.target,S.eventName,e.invoke,S.options)}),R,b,!0)),w[a]=function(){var t=this||e,r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));var o=arguments[2],i=!!o&&("boolean"==typeof o||o.capture),a=arguments[1];if(!a)return P.apply(this,arguments);if(!k||k(P,a,t,arguments)){var l,f=A[r];f&&(l=f[i?s:c]);var h=l&&t[l];if(h)for(var p=0;p<h.length;p++){var v=h[p];if(L(v,a))return h.splice(p,1),v.isRemoved=!0,0===h.length&&(v.allRemoved=!0,t[l]=null,i||"string"!=typeof r||(t[u+"ON_PROPERTY"+r]=null)),v.zone.cancelTask(v),b?t:void 0}return P.apply(this,arguments)}},w[l]=function(){var t=this||e,r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));for(var o=[],i=F(t,Z?Z(r):r),a=0;a<i.length;a++){var s=i[a];o.push(s.originalDelegate?s.originalDelegate:s.callback)}return o},w[f]=function(){var t=this||e,r=arguments[0];if(r){n&&n.transferEventName&&(r=n.transferEventName(r));var o=A[r];if(o){var i=t[o[c]],u=t[o[s]];if(i){var l=i.slice();for(v=0;v<l.length;v++)this[a].call(this,r,(h=l[v]).originalDelegate?h.originalDelegate:h.callback,h.options)}if(u)for(l=u.slice(),v=0;v<l.length;v++){var h;this[a].call(this,r,(h=l[v]).originalDelegate?h.originalDelegate:h.callback,h.options)}}}else{for(var p=Object.keys(t),v=0;v<p.length;v++){var d=x.exec(p[v]),_=d&&d[1];_&&"removeListener"!==_&&this[f].call(this,_)}this[f].call(this,"removeListener")}if(b)return this},z(w[o],D),z(w[a],P),O&&z(w[f],O),j&&z(w[l],j),!0}for(var w=[],E=0;E<n.length;E++)w[E]=b(n[E],r);return w}function F(e,t){if(!t){var n=[];for(var r in e){var o=x.exec(r),i=o&&o[1];if(i&&(!t||i===t)){var a=e[r];if(a)for(var u=0;u<a.length;u++)n.push(a[u])}}return n}var l=A[t];l||(M(t),l=A[t]);var f=e[l[c]],h=e[l[s]];return f?h?f.concat(h):f.slice():h?h.slice():[]}function H(e,t){t.patchMethod(e,"queueMicrotask",(function(e){return function(e,t){Zone.current.scheduleMicroTask("queueMicrotask",t[0])}}))}var G=h("zoneTask");function U(e,t,n,r){var o=null,i=null;n+=r;var a={};function s(t){var n=t.data;n.args[0]=function(){return t.invoke.apply(this,arguments)};var r=o.apply(e,n.args);return C(r)?n.handleId=r:(n.handle=r,n.isRefreshable=O(r.refresh)),t}function c(t){var n=t.data,r=n.handle;return i.call(e,null!=r?r:n.handleId)}o=D(e,t+=r,(function(n){return function(o,i){var u;if(O(i[0])){var l={isRefreshable:!1,isPeriodic:"Interval"===r,delay:"Timeout"===r||"Interval"===r?i[1]||0:void 0,args:i},h=i[0];i[0]=function e(){try{return h.apply(this,arguments)}finally{var t=l.handle,n=l.handleId;l.isPeriodic||l.isRefreshable||(n?delete a[n]:t&&(t[G]=null))}};var p=f(t,i[0],l,s,c);if(!p)return p;var v=p.data,d=v.handleId,_=v.handle,k=v.isRefreshable,g=v.isPeriodic;if(d)a[d]=p;else if(_&&(_[G]=p,k&&!g)){var y=_.refresh;_.refresh=function(){var e=p.zone,t=p.state;return"notScheduled"===t?(p._state="scheduled",e._updateTaskCount(p,1)):"running"===t&&(p._state="scheduling"),y.call(this)}}return null!==(u=null!=_?_:d)&&void 0!==u?u:p}return n.apply(e,i)}})),i=D(e,n,(function(t){return function(n,r){var o,i=r[0];C(i)?(o=a[i],delete a[i]):(o=null==i?void 0:i[G])?i[G]=null:o=i,(null==o?void 0:o.type)?o.cancelFn&&o.zone.cancelTask(o):t.apply(e,r)}}))}var q="set",W="clear";!function V(){var e=function r(){var e,r=globalThis,o=!0===r[t("forceDuplicateZoneCheck")];if(r.Zone&&(o||"function"!=typeof r.Zone.__symbol__))throw new Error("Zone already loaded.");return null!==(e=r.Zone)&&void 0!==e||(r.Zone=n()),r.Zone}();(function o(e){(function t(e){e.__load_patch("node_util",(function(e,t,n){n.patchOnProperties=Z,n.patchMethod=D,n.bindArguments=_,n.patchMacroTask=P,function r(e){S=e}(!0)}))})(e),function n(e){e.__load_patch("EventEmitter",(function(e,t,n){var r,o="addListener",i="removeListener",a=function(e,t){return e.callback===t||e.callback.listener===t},s=function(e){return"string"==typeof e?e:e?e.toString().replace("(","_").replace(")","_"):""};try{r=require("events")}catch(e){}r&&r.EventEmitter&&function c(t){var r=L(e,n,[t],{useG:!1,add:o,rm:i,prepend:"prependListener",rmAll:"removeAllListeners",listeners:"listeners",chkDup:!1,rt:!0,diff:a,eventNameToString:s});r&&r[0]&&(t.on=t[o],t.off=t[i])}(r.EventEmitter.prototype)}))}(e),function r(e){e.__load_patch("fs",(function(e,t,n){var r,o;try{o=require("fs")}catch(e){}if(o){["access","appendFile","chmod","chown","close","exists","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","lutimes","link","lstat","mkdir","mkdtemp","open","opendir","read","readdir","readFile","readlink","realpath","rename","rmdir","stat","symlink","truncate","unlink","utimes","write","writeFile","writev"].filter((function(e){return!!o[e]&&"function"==typeof o[e]})).forEach((function(e){P(o,e,(function(t,n){return{name:"fs."+e,args:n,cbIdx:n.length>0?n.length-1:-1,target:t}}))}));var i=null===(r=o.realpath)||void 0===r?void 0:r[n.symbol("OriginalDelegate")];(null==i?void 0:i.native)&&(o.realpath.native=i.native,P(o.realpath,"native",(function(e,t){return{args:t,target:e,cbIdx:t.length>0?t.length-1:-1,name:"fs.realpath.native"}})))}}))}(e),e.__load_patch("node_timers",(function(e,t){var n=!1;try{var r=require("timers");if(e.setTimeout!==r.setTimeout&&!m){var o=r.setTimeout;r.setTimeout=function(){return n=!0,o.apply(this,arguments)};var i=e.setTimeout((function(){}),100);clearTimeout(i),r.setTimeout=o}U(r,q,W,"Timeout"),U(r,q,W,"Interval"),U(r,q,W,"Immediate")}catch(e){}m||(n?(e[t.__symbol__("setTimeout")]=e.setTimeout,e[t.__symbol__("setInterval")]=e.setInterval,e[t.__symbol__("setImmediate")]=e.setImmediate):(U(e,q,W,"Timeout"),U(e,q,W,"Interval"),U(e,q,W,"Immediate")))})),e.__load_patch("nextTick",(function(){j(process,"nextTick",(function(e,t){return{name:"process.nextTick",args:t,cbIdx:t.length>0&&"function"==typeof t[0]?0:-1,target:process}}))})),e.__load_patch("handleUnhandledPromiseRejection",(function(e,t,n){function r(e){return function(t){F(process,e).forEach((function(n){"unhandledRejection"===e?n.invoke(t.rejection,t.promise):"rejectionHandled"===e&&n.invoke(t.promise)}))}}t[n.symbol("unhandledPromiseRejectionHandler")]=r("unhandledRejection"),t[n.symbol("rejectionHandledHandler")]=r("rejectionHandled")})),e.__load_patch("crypto",(function(){var e;try{e=require("crypto")}catch(e){}e&&["randomBytes","pbkdf2"].forEach((function(t){P(e,t,(function(n,r){return{name:"crypto."+t,args:r,cbIdx:r.length>0&&"function"==typeof r[r.length-1]?r.length-1:-1,target:e}}))}))})),e.__load_patch("console",(function(e,t){["dir","log","info","error","warn","assert","debug","timeEnd","trace"].forEach((function(e){var n=console[t.__symbol__(e)]=console[e];n&&(console[e]=function(){var e=a.call(arguments);return t.current===t.root?n.apply(this,e):t.root.run(n,this,e)})}))})),e.__load_patch("queueMicrotask",(function(e,t,n){H(e,n)}))})(e),function i(e){e.__load_patch("ZoneAwarePromise",(function(e,t,n){var r=Object.getOwnPropertyDescriptor,o=Object.defineProperty,i=n.symbol,a=[],s=!1!==e[i("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],c=i("Promise"),u=i("then");n.onUnhandledError=function(e){if(n.showUncaughtError()){var t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},n.microtaskDrainDone=function(){for(var e=function(){var e=a.shift();try{e.zone.runGuarded((function(){if(e.throwOriginal)throw e.rejection;throw e}))}catch(e){!function r(e){n.onUnhandledError(e);try{var r=t[l];"function"==typeof r&&r.call(this,e)}catch(e){}}(e)}};a.length;)e()};var l=i("unhandledPromiseRejectionHandler");function f(e){return e&&"function"==typeof e.then}function h(e){return e}function p(e){return I.reject(e)}var v=i("state"),d=i("value"),_=i("finally"),k=i("parentPromiseValue"),g=i("parentPromiseState"),y=null,m=!0,T=!1;function b(e,t){return function(n){try{S(e,t,n)}catch(t){S(e,!1,t)}}}var w=function(){var e=!1;return function t(n){return function(){e||(e=!0,n.apply(null,arguments))}}},E="Promise resolved with itself",Z=i("currentTaskTrace");function S(e,r,i){var c=w();if(e===i)throw new TypeError(E);if(e[v]===y){var u=null;try{"object"!=typeof i&&"function"!=typeof i||(u=i&&i.then)}catch(t){return c((function(){S(e,!1,t)}))(),e}if(r!==T&&i instanceof I&&i.hasOwnProperty(v)&&i.hasOwnProperty(d)&&i[v]!==y)j(i),S(e,i[v],i[d]);else if(r!==T&&"function"==typeof u)try{u.call(i,c(b(e,r)),c(b(e,!1)))}catch(t){c((function(){S(e,!1,t)}))()}else{e[v]=r;var l=e[d];if(e[d]=i,e[_]===_&&r===m&&(e[v]=e[g],e[d]=e[k]),r===T&&i instanceof Error){var f=t.currentTask&&t.currentTask.data&&t.currentTask.data.__creationTrace__;f&&o(i,Z,{configurable:!0,enumerable:!1,writable:!0,value:f})}for(var h=0;h<l.length;)z(e,l[h++],l[h++],l[h++],l[h++]);if(0==l.length&&r==T){e[v]=0;var p=i;try{throw new Error("Uncaught (in promise): "+function e(t){return t&&t.toString===Object.prototype.toString?(t.constructor&&t.constructor.name||"")+": "+JSON.stringify(t):t?t.toString():Object.prototype.toString.call(t)}(i)+(i&&i.stack?"\n"+i.stack:""))}catch(e){p=e}s&&(p.throwOriginal=!0),p.rejection=i,p.promise=e,p.zone=t.current,p.task=t.currentTask,a.push(p),n.scheduleMicroTask()}}}return e}var P=i("rejectionHandledHandler");function j(e){if(0===e[v]){try{var n=t[P];n&&"function"==typeof n&&n.call(this,{rejection:e[d],promise:e})}catch(e){}e[v]=T;for(var r=0;r<a.length;r++)e===a[r].promise&&a.splice(r,1)}}function z(e,t,n,r,o){j(e);var i=e[v],a=i?"function"==typeof r?r:h:"function"==typeof o?o:p;t.scheduleMicroTask("Promise.then",(function(){try{var r=e[d],o=!!n&&_===n[_];o&&(n[k]=r,n[g]=i);var s=t.run(a,void 0,o&&a!==p&&a!==h?[]:[r]);S(n,!0,s)}catch(e){S(n,!1,e)}}),n)}var O=function(){},C=e.AggregateError,I=function(){function e(t){var n=this;if(!(n instanceof e))throw new Error("Must be an instanceof Promise.");n[v]=y,n[d]=[];try{var r=w();t&&t(r(b(n,m)),r(b(n,T)))}catch(e){S(n,!1,e)}}return e.toString=function(){return"function ZoneAwarePromise() { [native code] }"},e.resolve=function(t){return t instanceof e?t:S(new this(null),m,t)},e.reject=function(e){return S(new this(null),T,e)},e.withResolvers=function(){var t={};return t.promise=new e((function(e,n){t.resolve=e,t.reject=n})),t},e.any=function(t){if(!t||"function"!=typeof t[Symbol.iterator])return Promise.reject(new C([],"All promises were rejected"));var n=[],r=0;try{for(var o=0,i=t;o<i.length;o++)r++,n.push(e.resolve(i[o]))}catch(e){return Promise.reject(new C([],"All promises were rejected"))}if(0===r)return Promise.reject(new C([],"All promises were rejected"));var a=!1,s=[];return new e((function(e,t){for(var o=0;o<n.length;o++)n[o].then((function(t){a||(a=!0,e(t))}),(function(e){s.push(e),0===--r&&(a=!0,t(new C(s,"All promises were rejected")))}))}))},e.race=function(e){var t,n,r=new this((function(e,r){t=e,n=r}));function o(e){t(e)}function i(e){n(e)}for(var a=0,s=e;a<s.length;a++){var c=s[a];f(c)||(c=this.resolve(c)),c.then(o,i)}return r},e.all=function(t){return e.allWithCallback(t)},e.allSettled=function(t){return(this&&this.prototype instanceof e?this:e).allWithCallback(t,{thenCallback:function(e){return{status:"fulfilled",value:e}},errorCallback:function(e){return{status:"rejected",reason:e}}})},e.allWithCallback=function(e,t){for(var n,r,o=new this((function(e,t){n=e,r=t})),i=2,a=0,s=[],c=function(e){f(e)||(e=u.resolve(e));var o=a;try{e.then((function(e){s[o]=t?t.thenCallback(e):e,0===--i&&n(s)}),(function(e){t?(s[o]=t.errorCallback(e),0===--i&&n(s)):r(e)}))}catch(e){r(e)}i++,a++},u=this,l=0,h=e;l<h.length;l++)c(h[l]);return 0==(i-=2)&&n(s),o},Object.defineProperty(e.prototype,Symbol.toStringTag,{get:function(){return"Promise"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Symbol.species,{get:function(){return e},enumerable:!1,configurable:!0}),e.prototype.then=function(n,r){var o,i=null===(o=this.constructor)||void 0===o?void 0:o[Symbol.species];i&&"function"==typeof i||(i=this.constructor||e);var a=new i(O),s=t.current;return this[v]==y?this[d].push(s,a,n,r):z(this,s,a,n,r),a},e.prototype.catch=function(e){return this.then(null,e)},e.prototype.finally=function(n){var r,o=null===(r=this.constructor)||void 0===r?void 0:r[Symbol.species];o&&"function"==typeof o||(o=e);var i=new o(O);i[_]=_;var a=t.current;return this[v]==y?this[d].push(a,i,n,n):z(this,a,i,n,n),i},e}();I.resolve=I.resolve,I.reject=I.reject,I.race=I.race,I.all=I.all;var A=e[c]=e.Promise;e.Promise=I;var N=i("thenPatched");function x(e){var t=e.prototype,n=r(t,"then");if(!n||!1!==n.writable&&n.configurable){var o=t.then;t[u]=o,e.prototype.then=function(e,t){var n=this;return new I((function(e,t){o.call(n,e,t)})).then(e,t)},e[N]=!0}}return n.patchThen=x,A&&(x(A),D(e,"fetch",(function(e){return function t(e){return function(t,n){var r=e.apply(t,n);if(r instanceof I)return r;var o=r.constructor;return o[N]||x(o),r}}(e)}))),Promise[t.__symbol__("uncaughtPromiseErrors")]=a,I}))}(e),function s(e){e.__load_patch("toString",(function(e){var t=Function.prototype.toString,n=h("OriginalDelegate"),r=h("Promise"),o=h("Error"),i=function i(){if("function"==typeof this){var a=this[n];if(a)return"function"==typeof a?t.call(a):Object.prototype.toString.call(a);if(this===Promise){var s=e[r];if(s)return t.call(s)}if(this===Error){var c=e[o];if(c)return t.call(c)}}return t.call(this)};i[n]=t,Function.prototype.toString=i;var a=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":a.call(this)}}))}(e)}()}));