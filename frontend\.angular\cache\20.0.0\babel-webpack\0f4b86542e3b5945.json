{"ast": null, "code": "import { QueryList, InjectionToken } from '@angular/core';\nimport { Subscription, isObservable, Subject, of } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { T as Typeahead } from './typeahead-9ZW4Dtsf.mjs';\nimport { coerceObservable } from './coercion/private.mjs';\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nclass TreeKeyManager {\n  /** The index of the currently active (focused) item. */\n  _activeItemIndex = -1;\n  /** The currently active (focused) item. */\n  _activeItem = null;\n  /** Whether or not we activate the item when it's focused. */\n  _shouldActivationFollowFocus = false;\n  /**\n   * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n   * Right arrow are switched.\n   */\n  _horizontalOrientation = 'ltr';\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager.\n   *\n   * The default value for this doesn't skip any elements in order to keep tree items focusable\n   * when disabled. This aligns with ARIA guidelines:\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n   */\n  _skipPredicateFn = _item => false;\n  /** Function to determine equivalent items. */\n  _trackByFn = item => item;\n  /** Synchronous cache of the items to manage. */\n  _items = [];\n  _typeahead;\n  _typeaheadSubscription = Subscription.EMPTY;\n  _hasInitialFocused = false;\n  _initializeFocus() {\n    if (this._hasInitialFocused || this._items.length === 0) {\n      return;\n    }\n    let activeIndex = 0;\n    for (let i = 0; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n        activeIndex = i;\n        break;\n      }\n    }\n    const activeItem = this._items[activeIndex];\n    // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n    // capture the focus since the user isn't interacting with it. See #29628.\n    if (activeItem.makeFocusable) {\n      this._activeItem?.unfocus();\n      this._activeItemIndex = activeIndex;\n      this._activeItem = activeItem;\n      this._typeahead?.setCurrentSelectedItemIndex(activeIndex);\n      activeItem.makeFocusable();\n    } else {\n      // Backwards compatibility for items that don't implement `makeFocusable`.\n      this.focusItem(activeIndex);\n    }\n    this._hasInitialFocused = true;\n  }\n  /**\n   *\n   * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n   * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n   * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n   * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n   * default interval of 200ms.\n   */\n  constructor(items, config) {\n    // We allow for the items to be an array or Observable because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (items instanceof QueryList) {\n      this._items = items.toArray();\n      items.changes.subscribe(newItems => {\n        this._items = newItems.toArray();\n        this._typeahead?.setItems(this._items);\n        this._updateActiveItemIndex(this._items);\n        this._initializeFocus();\n      });\n    } else if (isObservable(items)) {\n      items.subscribe(newItems => {\n        this._items = newItems;\n        this._typeahead?.setItems(newItems);\n        this._updateActiveItemIndex(newItems);\n        this._initializeFocus();\n      });\n    } else {\n      this._items = items;\n      this._initializeFocus();\n    }\n    if (typeof config.shouldActivationFollowFocus === 'boolean') {\n      this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n    }\n    if (config.horizontalOrientation) {\n      this._horizontalOrientation = config.horizontalOrientation;\n    }\n    if (config.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if (config.trackBy) {\n      this._trackByFn = config.trackBy;\n    }\n    if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n      this._setTypeAhead(config.typeAheadDebounceInterval);\n    }\n  }\n  /** Stream that emits any time the focused item changes. */\n  change = new Subject();\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._typeahead?.destroy();\n    this.change.complete();\n  }\n  /**\n   * Handles a keyboard event on the tree.\n   * @param event Keyboard event that represents the user interaction with the tree.\n   */\n  onKeydown(event) {\n    const key = event.key;\n    switch (key) {\n      case 'Tab':\n        // Return early here, in order to allow Tab to actually tab out of the tree\n        return;\n      case 'ArrowDown':\n        this._focusNextItem();\n        break;\n      case 'ArrowUp':\n        this._focusPreviousItem();\n        break;\n      case 'ArrowRight':\n        this._horizontalOrientation === 'rtl' ? this._collapseCurrentItem() : this._expandCurrentItem();\n        break;\n      case 'ArrowLeft':\n        this._horizontalOrientation === 'rtl' ? this._expandCurrentItem() : this._collapseCurrentItem();\n        break;\n      case 'Home':\n        this._focusFirstItem();\n        break;\n      case 'End':\n        this._focusLastItem();\n        break;\n      case 'Enter':\n      case ' ':\n        this._activateCurrentItem();\n        break;\n      default:\n        if (event.key === '*') {\n          this._expandAllItemsAtCurrentItemLevel();\n          break;\n        }\n        this._typeahead?.handleKey(event);\n        // Return here, in order to avoid preventing the default action of non-navigational\n        // keys or resetting the buffer of pressed letters.\n        return;\n    }\n    // Reset the typeahead since the user has used a navigational key.\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  getActiveItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The currently active item. */\n  getActiveItem() {\n    return this._activeItem;\n  }\n  /** Focus the first available item. */\n  _focusFirstItem() {\n    this.focusItem(this._findNextAvailableItemIndex(-1));\n  }\n  /** Focus the last available item. */\n  _focusLastItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n  }\n  /** Focus the next available item. */\n  _focusNextItem() {\n    this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n  }\n  /** Focus the previous available item. */\n  _focusPreviousItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n  }\n  focusItem(itemOrIndex, options = {}) {\n    // Set default options\n    options.emitChangeEvent ??= true;\n    let index = typeof itemOrIndex === 'number' ? itemOrIndex : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n    if (index < 0 || index >= this._items.length) {\n      return;\n    }\n    const activeItem = this._items[index];\n    // If we're just setting the same item, don't re-call activate or focus\n    if (this._activeItem !== null && this._trackByFn(activeItem) === this._trackByFn(this._activeItem)) {\n      return;\n    }\n    const previousActiveItem = this._activeItem;\n    this._activeItem = activeItem ?? null;\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n    this._activeItem?.focus();\n    previousActiveItem?.unfocus();\n    if (options.emitChangeEvent) {\n      this.change.next(this._activeItem);\n    }\n    if (this._shouldActivationFollowFocus) {\n      this._activateCurrentItem();\n    }\n  }\n  _updateActiveItemIndex(newItems) {\n    const activeItem = this._activeItem;\n    if (!activeItem) {\n      return;\n    }\n    const newIndex = newItems.findIndex(item => this._trackByFn(item) === this._trackByFn(activeItem));\n    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n      this._activeItemIndex = newIndex;\n      this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n    }\n  }\n  _setTypeAhead(debounceInterval) {\n    this._typeahead = new Typeahead(this._items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.focusItem(item);\n    });\n  }\n  _findNextAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex + 1; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  _findPreviousAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex - 1; i >= 0; i--) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  /**\n   * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n   */\n  _collapseCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (this._isCurrentItemExpanded()) {\n      this._activeItem.collapse();\n    } else {\n      const parent = this._activeItem.getParent();\n      if (!parent || this._skipPredicateFn(parent)) {\n        return;\n      }\n      this.focusItem(parent);\n    }\n  }\n  /**\n   * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n   */\n  _expandCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (!this._isCurrentItemExpanded()) {\n      this._activeItem.expand();\n    } else {\n      coerceObservable(this._activeItem.getChildren()).pipe(take(1)).subscribe(children => {\n        const firstChild = children.find(child => !this._skipPredicateFn(child));\n        if (!firstChild) {\n          return;\n        }\n        this.focusItem(firstChild);\n      });\n    }\n  }\n  _isCurrentItemExpanded() {\n    if (!this._activeItem) {\n      return false;\n    }\n    return typeof this._activeItem.isExpanded === 'boolean' ? this._activeItem.isExpanded : this._activeItem.isExpanded();\n  }\n  _isItemDisabled(item) {\n    return typeof item.isDisabled === 'boolean' ? item.isDisabled : item.isDisabled?.();\n  }\n  /** For all items that are the same level as the current item, we expand those items. */\n  _expandAllItemsAtCurrentItemLevel() {\n    if (!this._activeItem) {\n      return;\n    }\n    const parent = this._activeItem.getParent();\n    let itemsToExpand;\n    if (!parent) {\n      itemsToExpand = of(this._items.filter(item => item.getParent() === null));\n    } else {\n      itemsToExpand = coerceObservable(parent.getChildren());\n    }\n    itemsToExpand.pipe(take(1)).subscribe(items => {\n      for (const item of items) {\n        item.expand();\n      }\n    });\n  }\n  _activateCurrentItem() {\n    this._activeItem?.activate();\n  }\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction TREE_KEY_MANAGER_FACTORY() {\n  return (items, options) => new TreeKeyManager(items, options);\n}\n/** Injection token that determines the key manager to use. */\nconst TREE_KEY_MANAGER = new InjectionToken('tree-key-manager', {\n  providedIn: 'root',\n  factory: TREE_KEY_MANAGER_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: TREE_KEY_MANAGER_FACTORY\n};\nexport { TREE_KEY_MANAGER as T, TreeKeyManager as a, TREE_KEY_MANAGER_FACTORY as b, TREE_KEY_MANAGER_FACTORY_PROVIDER as c };", "map": {"version": 3, "names": ["QueryList", "InjectionToken", "Subscription", "isObservable", "Subject", "of", "take", "T", "Typeahead", "coerceObservable", "TreeKeyManager", "_activeItemIndex", "_activeItem", "_shouldActivationFollowFocus", "_horizontalOrientation", "_skipPredicateFn", "_item", "_trackByFn", "item", "_items", "_typeahead", "_typeaheadSubscription", "EMPTY", "_hasInitialFocused", "_initializeFocus", "length", "activeIndex", "i", "_isItemDisabled", "activeItem", "makeFocusable", "unfocus", "setCurrentSelectedItemIndex", "focusItem", "constructor", "items", "config", "toArray", "changes", "subscribe", "newItems", "setItems", "_updateActiveItemIndex", "shouldActivationFollowFocus", "horizontalOrientation", "skipPredicate", "trackBy", "typeAheadDebounceInterval", "_setTypeAhead", "change", "destroy", "unsubscribe", "complete", "onKeydown", "event", "key", "_focusNextItem", "_focusPreviousItem", "_collapseCurrentItem", "_expandCurrentItem", "_focusFirstItem", "_focusLastItem", "_activateCurrentItem", "_expandAllItemsAtCurrentItemLevel", "handle<PERSON>ey", "reset", "preventDefault", "getActiveItemIndex", "getActiveItem", "_findNextAvailableItemIndex", "_findPreviousAvailableItemIndex", "itemOrIndex", "options", "emitChangeEvent", "index", "findIndex", "previousActiveItem", "focus", "next", "newIndex", "debounceInterval", "undefined", "selectedItem", "startingIndex", "_isCurrentItemExpanded", "collapse", "parent", "getParent", "expand", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pipe", "children", "<PERSON><PERSON><PERSON><PERSON>", "find", "child", "isExpanded", "isDisabled", "itemsToExpand", "filter", "activate", "TREE_KEY_MANAGER_FACTORY", "TREE_KEY_MANAGER", "providedIn", "factory", "TREE_KEY_MANAGER_FACTORY_PROVIDER", "provide", "useFactory", "a", "b", "c"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/cdk/fesm2022/tree-key-manager-KnCoIkIC.mjs"], "sourcesContent": ["import { QueryList, InjectionToken } from '@angular/core';\nimport { Subscription, isObservable, Subject, of } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { T as Typeahead } from './typeahead-9ZW4Dtsf.mjs';\nimport { coerceObservable } from './coercion/private.mjs';\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nclass TreeKeyManager {\n    /** The index of the currently active (focused) item. */\n    _activeItemIndex = -1;\n    /** The currently active (focused) item. */\n    _activeItem = null;\n    /** Whether or not we activate the item when it's focused. */\n    _shouldActivationFollowFocus = false;\n    /**\n     * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n     * Right arrow are switched.\n     */\n    _horizontalOrientation = 'ltr';\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager.\n     *\n     * The default value for this doesn't skip any elements in order to keep tree items focusable\n     * when disabled. This aligns with ARIA guidelines:\n     * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n     */\n    _skipPredicateFn = (_item) => false;\n    /** Function to determine equivalent items. */\n    _trackByFn = (item) => item;\n    /** Synchronous cache of the items to manage. */\n    _items = [];\n    _typeahead;\n    _typeaheadSubscription = Subscription.EMPTY;\n    _hasInitialFocused = false;\n    _initializeFocus() {\n        if (this._hasInitialFocused || this._items.length === 0) {\n            return;\n        }\n        let activeIndex = 0;\n        for (let i = 0; i < this._items.length; i++) {\n            if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n                activeIndex = i;\n                break;\n            }\n        }\n        const activeItem = this._items[activeIndex];\n        // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n        // capture the focus since the user isn't interacting with it. See #29628.\n        if (activeItem.makeFocusable) {\n            this._activeItem?.unfocus();\n            this._activeItemIndex = activeIndex;\n            this._activeItem = activeItem;\n            this._typeahead?.setCurrentSelectedItemIndex(activeIndex);\n            activeItem.makeFocusable();\n        }\n        else {\n            // Backwards compatibility for items that don't implement `makeFocusable`.\n            this.focusItem(activeIndex);\n        }\n        this._hasInitialFocused = true;\n    }\n    /**\n     *\n     * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n     * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n     * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n     * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n     * default interval of 200ms.\n     */\n    constructor(items, config) {\n        // We allow for the items to be an array or Observable because, in some cases, the consumer may\n        // not have access to a QueryList of the items they want to manage (e.g. when the\n        // items aren't being collected via `ViewChildren` or `ContentChildren`).\n        if (items instanceof QueryList) {\n            this._items = items.toArray();\n            items.changes.subscribe((newItems) => {\n                this._items = newItems.toArray();\n                this._typeahead?.setItems(this._items);\n                this._updateActiveItemIndex(this._items);\n                this._initializeFocus();\n            });\n        }\n        else if (isObservable(items)) {\n            items.subscribe(newItems => {\n                this._items = newItems;\n                this._typeahead?.setItems(newItems);\n                this._updateActiveItemIndex(newItems);\n                this._initializeFocus();\n            });\n        }\n        else {\n            this._items = items;\n            this._initializeFocus();\n        }\n        if (typeof config.shouldActivationFollowFocus === 'boolean') {\n            this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n        }\n        if (config.horizontalOrientation) {\n            this._horizontalOrientation = config.horizontalOrientation;\n        }\n        if (config.skipPredicate) {\n            this._skipPredicateFn = config.skipPredicate;\n        }\n        if (config.trackBy) {\n            this._trackByFn = config.trackBy;\n        }\n        if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n            this._setTypeAhead(config.typeAheadDebounceInterval);\n        }\n    }\n    /** Stream that emits any time the focused item changes. */\n    change = new Subject();\n    /** Cleans up the key manager. */\n    destroy() {\n        this._typeaheadSubscription.unsubscribe();\n        this._typeahead?.destroy();\n        this.change.complete();\n    }\n    /**\n     * Handles a keyboard event on the tree.\n     * @param event Keyboard event that represents the user interaction with the tree.\n     */\n    onKeydown(event) {\n        const key = event.key;\n        switch (key) {\n            case 'Tab':\n                // Return early here, in order to allow Tab to actually tab out of the tree\n                return;\n            case 'ArrowDown':\n                this._focusNextItem();\n                break;\n            case 'ArrowUp':\n                this._focusPreviousItem();\n                break;\n            case 'ArrowRight':\n                this._horizontalOrientation === 'rtl'\n                    ? this._collapseCurrentItem()\n                    : this._expandCurrentItem();\n                break;\n            case 'ArrowLeft':\n                this._horizontalOrientation === 'rtl'\n                    ? this._expandCurrentItem()\n                    : this._collapseCurrentItem();\n                break;\n            case 'Home':\n                this._focusFirstItem();\n                break;\n            case 'End':\n                this._focusLastItem();\n                break;\n            case 'Enter':\n            case ' ':\n                this._activateCurrentItem();\n                break;\n            default:\n                if (event.key === '*') {\n                    this._expandAllItemsAtCurrentItemLevel();\n                    break;\n                }\n                this._typeahead?.handleKey(event);\n                // Return here, in order to avoid preventing the default action of non-navigational\n                // keys or resetting the buffer of pressed letters.\n                return;\n        }\n        // Reset the typeahead since the user has used a navigational key.\n        this._typeahead?.reset();\n        event.preventDefault();\n    }\n    /** Index of the currently active item. */\n    getActiveItemIndex() {\n        return this._activeItemIndex;\n    }\n    /** The currently active item. */\n    getActiveItem() {\n        return this._activeItem;\n    }\n    /** Focus the first available item. */\n    _focusFirstItem() {\n        this.focusItem(this._findNextAvailableItemIndex(-1));\n    }\n    /** Focus the last available item. */\n    _focusLastItem() {\n        this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n    }\n    /** Focus the next available item. */\n    _focusNextItem() {\n        this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n    }\n    /** Focus the previous available item. */\n    _focusPreviousItem() {\n        this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n    }\n    focusItem(itemOrIndex, options = {}) {\n        // Set default options\n        options.emitChangeEvent ??= true;\n        let index = typeof itemOrIndex === 'number'\n            ? itemOrIndex\n            : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n        if (index < 0 || index >= this._items.length) {\n            return;\n        }\n        const activeItem = this._items[index];\n        // If we're just setting the same item, don't re-call activate or focus\n        if (this._activeItem !== null &&\n            this._trackByFn(activeItem) === this._trackByFn(this._activeItem)) {\n            return;\n        }\n        const previousActiveItem = this._activeItem;\n        this._activeItem = activeItem ?? null;\n        this._activeItemIndex = index;\n        this._typeahead?.setCurrentSelectedItemIndex(index);\n        this._activeItem?.focus();\n        previousActiveItem?.unfocus();\n        if (options.emitChangeEvent) {\n            this.change.next(this._activeItem);\n        }\n        if (this._shouldActivationFollowFocus) {\n            this._activateCurrentItem();\n        }\n    }\n    _updateActiveItemIndex(newItems) {\n        const activeItem = this._activeItem;\n        if (!activeItem) {\n            return;\n        }\n        const newIndex = newItems.findIndex(item => this._trackByFn(item) === this._trackByFn(activeItem));\n        if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n            this._activeItemIndex = newIndex;\n            this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n        }\n    }\n    _setTypeAhead(debounceInterval) {\n        this._typeahead = new Typeahead(this._items, {\n            debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n            skipPredicate: item => this._skipPredicateFn(item),\n        });\n        this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n            this.focusItem(item);\n        });\n    }\n    _findNextAvailableItemIndex(startingIndex) {\n        for (let i = startingIndex + 1; i < this._items.length; i++) {\n            if (!this._skipPredicateFn(this._items[i])) {\n                return i;\n            }\n        }\n        return startingIndex;\n    }\n    _findPreviousAvailableItemIndex(startingIndex) {\n        for (let i = startingIndex - 1; i >= 0; i--) {\n            if (!this._skipPredicateFn(this._items[i])) {\n                return i;\n            }\n        }\n        return startingIndex;\n    }\n    /**\n     * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n     */\n    _collapseCurrentItem() {\n        if (!this._activeItem) {\n            return;\n        }\n        if (this._isCurrentItemExpanded()) {\n            this._activeItem.collapse();\n        }\n        else {\n            const parent = this._activeItem.getParent();\n            if (!parent || this._skipPredicateFn(parent)) {\n                return;\n            }\n            this.focusItem(parent);\n        }\n    }\n    /**\n     * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n     */\n    _expandCurrentItem() {\n        if (!this._activeItem) {\n            return;\n        }\n        if (!this._isCurrentItemExpanded()) {\n            this._activeItem.expand();\n        }\n        else {\n            coerceObservable(this._activeItem.getChildren())\n                .pipe(take(1))\n                .subscribe(children => {\n                const firstChild = children.find(child => !this._skipPredicateFn(child));\n                if (!firstChild) {\n                    return;\n                }\n                this.focusItem(firstChild);\n            });\n        }\n    }\n    _isCurrentItemExpanded() {\n        if (!this._activeItem) {\n            return false;\n        }\n        return typeof this._activeItem.isExpanded === 'boolean'\n            ? this._activeItem.isExpanded\n            : this._activeItem.isExpanded();\n    }\n    _isItemDisabled(item) {\n        return typeof item.isDisabled === 'boolean' ? item.isDisabled : item.isDisabled?.();\n    }\n    /** For all items that are the same level as the current item, we expand those items. */\n    _expandAllItemsAtCurrentItemLevel() {\n        if (!this._activeItem) {\n            return;\n        }\n        const parent = this._activeItem.getParent();\n        let itemsToExpand;\n        if (!parent) {\n            itemsToExpand = of(this._items.filter(item => item.getParent() === null));\n        }\n        else {\n            itemsToExpand = coerceObservable(parent.getChildren());\n        }\n        itemsToExpand.pipe(take(1)).subscribe(items => {\n            for (const item of items) {\n                item.expand();\n            }\n        });\n    }\n    _activateCurrentItem() {\n        this._activeItem?.activate();\n    }\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction TREE_KEY_MANAGER_FACTORY() {\n    return (items, options) => new TreeKeyManager(items, options);\n}\n/** Injection token that determines the key manager to use. */\nconst TREE_KEY_MANAGER = new InjectionToken('tree-key-manager', {\n    providedIn: 'root',\n    factory: TREE_KEY_MANAGER_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n    provide: TREE_KEY_MANAGER,\n    useFactory: TREE_KEY_MANAGER_FACTORY,\n};\n\nexport { TREE_KEY_MANAGER as T, TreeKeyManager as a, TREE_KEY_MANAGER_FACTORY as b, TREE_KEY_MANAGER_FACTORY_PROVIDER as c };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,cAAc,QAAQ,eAAe;AACzD,SAASC,YAAY,EAAEC,YAAY,EAAEC,OAAO,EAAEC,EAAE,QAAQ,MAAM;AAC9D,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,CAAC,IAAIC,SAAS,QAAQ,0BAA0B;AACzD,SAASC,gBAAgB,QAAQ,wBAAwB;;AAEzD;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjB;EACAC,gBAAgB,GAAG,CAAC,CAAC;EACrB;EACAC,WAAW,GAAG,IAAI;EAClB;EACAC,4BAA4B,GAAG,KAAK;EACpC;AACJ;AACA;AACA;EACIC,sBAAsB,GAAG,KAAK;EAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,gBAAgB,GAAIC,KAAK,IAAK,KAAK;EACnC;EACAC,UAAU,GAAIC,IAAI,IAAKA,IAAI;EAC3B;EACAC,MAAM,GAAG,EAAE;EACXC,UAAU;EACVC,sBAAsB,GAAGnB,YAAY,CAACoB,KAAK;EAC3CC,kBAAkB,GAAG,KAAK;EAC1BC,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACD,kBAAkB,IAAI,IAAI,CAACJ,MAAM,CAACM,MAAM,KAAK,CAAC,EAAE;MACrD;IACJ;IACA,IAAIC,WAAW,GAAG,CAAC;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACR,MAAM,CAACM,MAAM,EAAEE,CAAC,EAAE,EAAE;MACzC,IAAI,CAAC,IAAI,CAACZ,gBAAgB,CAAC,IAAI,CAACI,MAAM,CAACQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACC,eAAe,CAAC,IAAI,CAACT,MAAM,CAACQ,CAAC,CAAC,CAAC,EAAE;QACjFD,WAAW,GAAGC,CAAC;QACf;MACJ;IACJ;IACA,MAAME,UAAU,GAAG,IAAI,CAACV,MAAM,CAACO,WAAW,CAAC;IAC3C;IACA;IACA,IAAIG,UAAU,CAACC,aAAa,EAAE;MAC1B,IAAI,CAAClB,WAAW,EAAEmB,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACpB,gBAAgB,GAAGe,WAAW;MACnC,IAAI,CAACd,WAAW,GAAGiB,UAAU;MAC7B,IAAI,CAACT,UAAU,EAAEY,2BAA2B,CAACN,WAAW,CAAC;MACzDG,UAAU,CAACC,aAAa,CAAC,CAAC;IAC9B,CAAC,MACI;MACD;MACA,IAAI,CAACG,SAAS,CAACP,WAAW,CAAC;IAC/B;IACA,IAAI,CAACH,kBAAkB,GAAG,IAAI;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIW,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACvB;IACA;IACA;IACA,IAAID,KAAK,YAAYnC,SAAS,EAAE;MAC5B,IAAI,CAACmB,MAAM,GAAGgB,KAAK,CAACE,OAAO,CAAC,CAAC;MAC7BF,KAAK,CAACG,OAAO,CAACC,SAAS,CAAEC,QAAQ,IAAK;QAClC,IAAI,CAACrB,MAAM,GAAGqB,QAAQ,CAACH,OAAO,CAAC,CAAC;QAChC,IAAI,CAACjB,UAAU,EAAEqB,QAAQ,CAAC,IAAI,CAACtB,MAAM,CAAC;QACtC,IAAI,CAACuB,sBAAsB,CAAC,IAAI,CAACvB,MAAM,CAAC;QACxC,IAAI,CAACK,gBAAgB,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,MACI,IAAIrB,YAAY,CAACgC,KAAK,CAAC,EAAE;MAC1BA,KAAK,CAACI,SAAS,CAACC,QAAQ,IAAI;QACxB,IAAI,CAACrB,MAAM,GAAGqB,QAAQ;QACtB,IAAI,CAACpB,UAAU,EAAEqB,QAAQ,CAACD,QAAQ,CAAC;QACnC,IAAI,CAACE,sBAAsB,CAACF,QAAQ,CAAC;QACrC,IAAI,CAAChB,gBAAgB,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACL,MAAM,GAAGgB,KAAK;MACnB,IAAI,CAACX,gBAAgB,CAAC,CAAC;IAC3B;IACA,IAAI,OAAOY,MAAM,CAACO,2BAA2B,KAAK,SAAS,EAAE;MACzD,IAAI,CAAC9B,4BAA4B,GAAGuB,MAAM,CAACO,2BAA2B;IAC1E;IACA,IAAIP,MAAM,CAACQ,qBAAqB,EAAE;MAC9B,IAAI,CAAC9B,sBAAsB,GAAGsB,MAAM,CAACQ,qBAAqB;IAC9D;IACA,IAAIR,MAAM,CAACS,aAAa,EAAE;MACtB,IAAI,CAAC9B,gBAAgB,GAAGqB,MAAM,CAACS,aAAa;IAChD;IACA,IAAIT,MAAM,CAACU,OAAO,EAAE;MAChB,IAAI,CAAC7B,UAAU,GAAGmB,MAAM,CAACU,OAAO;IACpC;IACA,IAAI,OAAOV,MAAM,CAACW,yBAAyB,KAAK,WAAW,EAAE;MACzD,IAAI,CAACC,aAAa,CAACZ,MAAM,CAACW,yBAAyB,CAAC;IACxD;EACJ;EACA;EACAE,MAAM,GAAG,IAAI7C,OAAO,CAAC,CAAC;EACtB;EACA8C,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC7B,sBAAsB,CAAC8B,WAAW,CAAC,CAAC;IACzC,IAAI,CAAC/B,UAAU,EAAE8B,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACD,MAAM,CAACG,QAAQ,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIC,SAASA,CAACC,KAAK,EAAE;IACb,MAAMC,GAAG,GAAGD,KAAK,CAACC,GAAG;IACrB,QAAQA,GAAG;MACP,KAAK,KAAK;QACN;QACA;MACJ,KAAK,WAAW;QACZ,IAAI,CAACC,cAAc,CAAC,CAAC;QACrB;MACJ,KAAK,SAAS;QACV,IAAI,CAACC,kBAAkB,CAAC,CAAC;QACzB;MACJ,KAAK,YAAY;QACb,IAAI,CAAC3C,sBAAsB,KAAK,KAAK,GAC/B,IAAI,CAAC4C,oBAAoB,CAAC,CAAC,GAC3B,IAAI,CAACC,kBAAkB,CAAC,CAAC;QAC/B;MACJ,KAAK,WAAW;QACZ,IAAI,CAAC7C,sBAAsB,KAAK,KAAK,GAC/B,IAAI,CAAC6C,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACD,oBAAoB,CAAC,CAAC;QACjC;MACJ,KAAK,MAAM;QACP,IAAI,CAACE,eAAe,CAAC,CAAC;QACtB;MACJ,KAAK,KAAK;QACN,IAAI,CAACC,cAAc,CAAC,CAAC;QACrB;MACJ,KAAK,OAAO;MACZ,KAAK,GAAG;QACJ,IAAI,CAACC,oBAAoB,CAAC,CAAC;QAC3B;MACJ;QACI,IAAIR,KAAK,CAACC,GAAG,KAAK,GAAG,EAAE;UACnB,IAAI,CAACQ,iCAAiC,CAAC,CAAC;UACxC;QACJ;QACA,IAAI,CAAC3C,UAAU,EAAE4C,SAAS,CAACV,KAAK,CAAC;QACjC;QACA;QACA;IACR;IACA;IACA,IAAI,CAAClC,UAAU,EAAE6C,KAAK,CAAC,CAAC;IACxBX,KAAK,CAACY,cAAc,CAAC,CAAC;EAC1B;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACxD,gBAAgB;EAChC;EACA;EACAyD,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACxD,WAAW;EAC3B;EACA;EACAgD,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAACoC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD;EACA;EACAR,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACqC,+BAA+B,CAAC,IAAI,CAACnD,MAAM,CAACM,MAAM,CAAC,CAAC;EAC5E;EACA;EACA+B,cAAcA,CAAA,EAAG;IACb,IAAI,CAACvB,SAAS,CAAC,IAAI,CAACoC,2BAA2B,CAAC,IAAI,CAAC1D,gBAAgB,CAAC,CAAC;EAC3E;EACA;EACA8C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACxB,SAAS,CAAC,IAAI,CAACqC,+BAA+B,CAAC,IAAI,CAAC3D,gBAAgB,CAAC,CAAC;EAC/E;EACAsB,SAASA,CAACsC,WAAW,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC;IACAA,OAAO,CAACC,eAAe,KAAK,IAAI;IAChC,IAAIC,KAAK,GAAG,OAAOH,WAAW,KAAK,QAAQ,GACrCA,WAAW,GACX,IAAI,CAACpD,MAAM,CAACwD,SAAS,CAACzD,IAAI,IAAI,IAAI,CAACD,UAAU,CAACC,IAAI,CAAC,KAAK,IAAI,CAACD,UAAU,CAACsD,WAAW,CAAC,CAAC;IAC3F,IAAIG,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACvD,MAAM,CAACM,MAAM,EAAE;MAC1C;IACJ;IACA,MAAMI,UAAU,GAAG,IAAI,CAACV,MAAM,CAACuD,KAAK,CAAC;IACrC;IACA,IAAI,IAAI,CAAC9D,WAAW,KAAK,IAAI,IACzB,IAAI,CAACK,UAAU,CAACY,UAAU,CAAC,KAAK,IAAI,CAACZ,UAAU,CAAC,IAAI,CAACL,WAAW,CAAC,EAAE;MACnE;IACJ;IACA,MAAMgE,kBAAkB,GAAG,IAAI,CAAChE,WAAW;IAC3C,IAAI,CAACA,WAAW,GAAGiB,UAAU,IAAI,IAAI;IACrC,IAAI,CAAClB,gBAAgB,GAAG+D,KAAK;IAC7B,IAAI,CAACtD,UAAU,EAAEY,2BAA2B,CAAC0C,KAAK,CAAC;IACnD,IAAI,CAAC9D,WAAW,EAAEiE,KAAK,CAAC,CAAC;IACzBD,kBAAkB,EAAE7C,OAAO,CAAC,CAAC;IAC7B,IAAIyC,OAAO,CAACC,eAAe,EAAE;MACzB,IAAI,CAACxB,MAAM,CAAC6B,IAAI,CAAC,IAAI,CAAClE,WAAW,CAAC;IACtC;IACA,IAAI,IAAI,CAACC,4BAA4B,EAAE;MACnC,IAAI,CAACiD,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACApB,sBAAsBA,CAACF,QAAQ,EAAE;IAC7B,MAAMX,UAAU,GAAG,IAAI,CAACjB,WAAW;IACnC,IAAI,CAACiB,UAAU,EAAE;MACb;IACJ;IACA,MAAMkD,QAAQ,GAAGvC,QAAQ,CAACmC,SAAS,CAACzD,IAAI,IAAI,IAAI,CAACD,UAAU,CAACC,IAAI,CAAC,KAAK,IAAI,CAACD,UAAU,CAACY,UAAU,CAAC,CAAC;IAClG,IAAIkD,QAAQ,GAAG,CAAC,CAAC,IAAIA,QAAQ,KAAK,IAAI,CAACpE,gBAAgB,EAAE;MACrD,IAAI,CAACA,gBAAgB,GAAGoE,QAAQ;MAChC,IAAI,CAAC3D,UAAU,EAAEY,2BAA2B,CAAC+C,QAAQ,CAAC;IAC1D;EACJ;EACA/B,aAAaA,CAACgC,gBAAgB,EAAE;IAC5B,IAAI,CAAC5D,UAAU,GAAG,IAAIZ,SAAS,CAAC,IAAI,CAACW,MAAM,EAAE;MACzC6D,gBAAgB,EAAE,OAAOA,gBAAgB,KAAK,QAAQ,GAAGA,gBAAgB,GAAGC,SAAS;MACrFpC,aAAa,EAAE3B,IAAI,IAAI,IAAI,CAACH,gBAAgB,CAACG,IAAI;IACrD,CAAC,CAAC;IACF,IAAI,CAACG,sBAAsB,GAAG,IAAI,CAACD,UAAU,CAAC8D,YAAY,CAAC3C,SAAS,CAACrB,IAAI,IAAI;MACzE,IAAI,CAACe,SAAS,CAACf,IAAI,CAAC;IACxB,CAAC,CAAC;EACN;EACAmD,2BAA2BA,CAACc,aAAa,EAAE;IACvC,KAAK,IAAIxD,CAAC,GAAGwD,aAAa,GAAG,CAAC,EAAExD,CAAC,GAAG,IAAI,CAACR,MAAM,CAACM,MAAM,EAAEE,CAAC,EAAE,EAAE;MACzD,IAAI,CAAC,IAAI,CAACZ,gBAAgB,CAAC,IAAI,CAACI,MAAM,CAACQ,CAAC,CAAC,CAAC,EAAE;QACxC,OAAOA,CAAC;MACZ;IACJ;IACA,OAAOwD,aAAa;EACxB;EACAb,+BAA+BA,CAACa,aAAa,EAAE;IAC3C,KAAK,IAAIxD,CAAC,GAAGwD,aAAa,GAAG,CAAC,EAAExD,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzC,IAAI,CAAC,IAAI,CAACZ,gBAAgB,CAAC,IAAI,CAACI,MAAM,CAACQ,CAAC,CAAC,CAAC,EAAE;QACxC,OAAOA,CAAC;MACZ;IACJ;IACA,OAAOwD,aAAa;EACxB;EACA;AACJ;AACA;EACIzB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAAC9C,WAAW,EAAE;MACnB;IACJ;IACA,IAAI,IAAI,CAACwE,sBAAsB,CAAC,CAAC,EAAE;MAC/B,IAAI,CAACxE,WAAW,CAACyE,QAAQ,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,MAAMC,MAAM,GAAG,IAAI,CAAC1E,WAAW,CAAC2E,SAAS,CAAC,CAAC;MAC3C,IAAI,CAACD,MAAM,IAAI,IAAI,CAACvE,gBAAgB,CAACuE,MAAM,CAAC,EAAE;QAC1C;MACJ;MACA,IAAI,CAACrD,SAAS,CAACqD,MAAM,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;EACI3B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC/C,WAAW,EAAE;MACnB;IACJ;IACA,IAAI,CAAC,IAAI,CAACwE,sBAAsB,CAAC,CAAC,EAAE;MAChC,IAAI,CAACxE,WAAW,CAAC4E,MAAM,CAAC,CAAC;IAC7B,CAAC,MACI;MACD/E,gBAAgB,CAAC,IAAI,CAACG,WAAW,CAAC6E,WAAW,CAAC,CAAC,CAAC,CAC3CC,IAAI,CAACpF,IAAI,CAAC,CAAC,CAAC,CAAC,CACbiC,SAAS,CAACoD,QAAQ,IAAI;QACvB,MAAMC,UAAU,GAAGD,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,CAAC,IAAI,CAAC/E,gBAAgB,CAAC+E,KAAK,CAAC,CAAC;QACxE,IAAI,CAACF,UAAU,EAAE;UACb;QACJ;QACA,IAAI,CAAC3D,SAAS,CAAC2D,UAAU,CAAC;MAC9B,CAAC,CAAC;IACN;EACJ;EACAR,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAACxE,WAAW,EAAE;MACnB,OAAO,KAAK;IAChB;IACA,OAAO,OAAO,IAAI,CAACA,WAAW,CAACmF,UAAU,KAAK,SAAS,GACjD,IAAI,CAACnF,WAAW,CAACmF,UAAU,GAC3B,IAAI,CAACnF,WAAW,CAACmF,UAAU,CAAC,CAAC;EACvC;EACAnE,eAAeA,CAACV,IAAI,EAAE;IAClB,OAAO,OAAOA,IAAI,CAAC8E,UAAU,KAAK,SAAS,GAAG9E,IAAI,CAAC8E,UAAU,GAAG9E,IAAI,CAAC8E,UAAU,GAAG,CAAC;EACvF;EACA;EACAjC,iCAAiCA,CAAA,EAAG;IAChC,IAAI,CAAC,IAAI,CAACnD,WAAW,EAAE;MACnB;IACJ;IACA,MAAM0E,MAAM,GAAG,IAAI,CAAC1E,WAAW,CAAC2E,SAAS,CAAC,CAAC;IAC3C,IAAIU,aAAa;IACjB,IAAI,CAACX,MAAM,EAAE;MACTW,aAAa,GAAG5F,EAAE,CAAC,IAAI,CAACc,MAAM,CAAC+E,MAAM,CAAChF,IAAI,IAAIA,IAAI,CAACqE,SAAS,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;IAC7E,CAAC,MACI;MACDU,aAAa,GAAGxF,gBAAgB,CAAC6E,MAAM,CAACG,WAAW,CAAC,CAAC,CAAC;IAC1D;IACAQ,aAAa,CAACP,IAAI,CAACpF,IAAI,CAAC,CAAC,CAAC,CAAC,CAACiC,SAAS,CAACJ,KAAK,IAAI;MAC3C,KAAK,MAAMjB,IAAI,IAAIiB,KAAK,EAAE;QACtBjB,IAAI,CAACsE,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACA1B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAClD,WAAW,EAAEuF,QAAQ,CAAC,CAAC;EAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAAA,EAAG;EAChC,OAAO,CAACjE,KAAK,EAAEqC,OAAO,KAAK,IAAI9D,cAAc,CAACyB,KAAK,EAAEqC,OAAO,CAAC;AACjE;AACA;AACA,MAAM6B,gBAAgB,GAAG,IAAIpG,cAAc,CAAC,kBAAkB,EAAE;EAC5DqG,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMI,iCAAiC,GAAG;EACtCC,OAAO,EAAEJ,gBAAgB;EACzBK,UAAU,EAAEN;AAChB,CAAC;AAED,SAASC,gBAAgB,IAAI9F,CAAC,EAAEG,cAAc,IAAIiG,CAAC,EAAEP,wBAAwB,IAAIQ,CAAC,EAAEJ,iCAAiC,IAAIK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}