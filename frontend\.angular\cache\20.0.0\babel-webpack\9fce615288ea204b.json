{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nfunction LoginComponent_form_12_mat_spinner_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 22);\n  }\n}\nfunction LoginComponent_form_12_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 7);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_form_12_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"mat-form-field\", 8)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 9);\n    i0.ɵɵelementStart(5, \"mat-icon\", 10);\n    i0.ɵɵtext(6, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-form-field\", 8)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 11);\n    i0.ɵɵelementStart(13, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_12_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hidePassword = !ctx_r1.hidePassword);\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"mat-error\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 13);\n    i0.ɵɵtemplate(19, LoginComponent_form_12_mat_spinner_19_Template, 1, 0, \"mat-spinner\", 14)(20, LoginComponent_form_12_span_20_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 16)(22, \"span\");\n    i0.ɵɵtext(23, \"or\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_12_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27, \" Login with OTP \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 18)(29, \"a\", 19);\n    i0.ɵɵtext(30, \"Forgot Password?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 20)(32, \"span\");\n    i0.ɵɵtext(33, \"Don't have an account? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"a\", 21);\n    i0.ɵɵtext(35, \"Sign Up\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.loginForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.loginForm, \"email\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r1.hidePassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.hidePassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.loginForm, \"password\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_13_mat_spinner_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 22);\n  }\n}\nfunction LoginComponent_form_13_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verify & Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 7);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_form_13_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTwoFactorSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 23)(2, \"mat-icon\", 24);\n    i0.ɵɵtext(3, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Two-Factor Authentication\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Enter the 6-digit code from your authenticator app\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-form-field\", 8)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Authentication Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 25);\n    i0.ɵɵelementStart(12, \"mat-icon\", 10);\n    i0.ɵɵtext(13, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-error\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"button\", 13);\n    i0.ɵɵtemplate(17, LoginComponent_form_13_mat_spinner_17_Template, 1, 0, \"mat-spinner\", 14)(18, LoginComponent_form_13_span_18_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_13_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.backToLogin());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Back to Login \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.twoFactorForm);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.twoFactorForm, \"twoFactorToken\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_14_button_16_mat_spinner_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 22);\n  }\n}\nfunction LoginComponent_form_14_button_16_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Send OTP\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_14_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendOTP());\n    });\n    i0.ɵɵtemplate(1, LoginComponent_form_14_button_16_mat_spinner_1_Template, 1, 0, \"mat-spinner\", 14)(2, LoginComponent_form_14_button_16_span_2_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_14_div_17_mat_spinner_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 22);\n  }\n}\nfunction LoginComponent_form_14_div_17_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verify & Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_14_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 8)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Enter OTP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 31);\n    i0.ɵɵelementStart(5, \"mat-icon\", 10);\n    i0.ɵɵtext(6, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_div_17_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loginWithOTP());\n    });\n    i0.ɵɵtemplate(10, LoginComponent_form_14_div_17_mat_spinner_10_Template, 1, 0, \"mat-spinner\", 14)(11, LoginComponent_form_14_div_17_span_11_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_div_17_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendOTP());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Resend OTP \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.otpForm, \"code\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 27)(1, \"div\", 23)(2, \"mat-icon\", 24);\n    i0.ɵɵtext(3, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Login with OTP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Enter your email or phone number to receive a one-time password\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-form-field\", 8)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Email or Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 28);\n    i0.ɵɵelementStart(12, \"mat-icon\", 10);\n    i0.ɵɵtext(13, \"contact_mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-error\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, LoginComponent_form_14_button_16_Template, 3, 3, \"button\", 29)(17, LoginComponent_form_14_div_17_Template, 16, 4, \"div\", 15);\n    i0.ɵɵelementStart(18, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Back to Login \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.otpForm);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.otpForm, \"identifier\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.otpSent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.otpSent);\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.loading = false;\n    this.hidePassword = true;\n    this.showOTPLogin = false;\n    this.showTwoFactor = false;\n    this.otpSent = false;\n    this.returnUrl = '';\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]]\n    });\n    this.otpForm = this.formBuilder.group({\n      identifier: ['', [Validators.required]],\n      code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n    this.twoFactorForm = this.formBuilder.group({\n      twoFactorToken: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n  }\n  ngOnInit() {\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    if (this.authService.isAuthenticated) {\n      this.router.navigate([this.returnUrl]);\n    }\n    const message = this.route.snapshot.queryParams['message'];\n    if (message) {\n      this.snackBar.open(message, 'Close', {\n        duration: 5000\n      });\n    }\n  }\n  onSubmit() {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched(this.loginForm);\n      return;\n    }\n    this.loading = true;\n    const credentials = this.loginForm.value;\n    this.authService.login(credentials).subscribe({\n      next: response => {\n        if (response.requiresTwoFactor) {\n          this.showTwoFactor = true;\n          this.snackBar.open('Please enter your two-factor authentication code', 'Close', {\n            duration: 5000\n          });\n        } else {\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n        }\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Login failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  onTwoFactorSubmit() {\n    if (this.twoFactorForm.invalid) {\n      this.markFormGroupTouched(this.twoFactorForm);\n      return;\n    }\n    this.loading = true;\n    const credentials = {\n      ...this.loginForm.value,\n      twoFactorToken: this.twoFactorForm.value.twoFactorToken\n    };\n    this.authService.login(credentials).subscribe({\n      next: response => {\n        this.snackBar.open('Login successful!', 'Close', {\n          duration: 3000\n        });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Two-factor authentication failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  sendOTP() {\n    const identifier = this.otpForm.get('identifier')?.value;\n    if (!identifier) {\n      this.snackBar.open('Please enter email or phone number', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    this.loading = true;\n    const request = {\n      identifier,\n      type: 'login'\n    };\n    this.authService.sendOTP(request).subscribe({\n      next: () => {\n        this.otpSent = true;\n        this.snackBar.open('OTP sent successfully!', 'Close', {\n          duration: 3000\n        });\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Failed to send OTP', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  loginWithOTP() {\n    if (this.otpForm.invalid) {\n      this.markFormGroupTouched(this.otpForm);\n      return;\n    }\n    this.loading = true;\n    const {\n      identifier,\n      code\n    } = this.otpForm.value;\n    this.authService.loginWithOTP(identifier, code).subscribe({\n      next: () => {\n        this.snackBar.open('Login successful!', 'Close', {\n          duration: 3000\n        });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'OTP login failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  toggleOTPLogin() {\n    this.showOTPLogin = !this.showOTPLogin;\n    this.showTwoFactor = false;\n    this.otpSent = false;\n    this.otpForm.reset();\n  }\n  backToLogin() {\n    this.showTwoFactor = false;\n    this.showOTPLogin = false;\n    this.otpSent = false;\n  }\n  getFieldError(form, fieldName) {\n    const field = form.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['email']) return 'Please enter a valid email';\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['pattern']) return 'Please enter a valid format';\n    }\n    return '';\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    standalone: false,\n    decls: 15,\n    vars: 3,\n    consts: [[1, \"auth-container\"], [1, \"auth-card\", \"fade-in\"], [1, \"auth-header\"], [1, \"security-badge\"], [1, \"auth-content\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"divider\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"w-100\", 3, \"click\"], [1, \"text-center\", \"mt-3\"], [\"routerLink\", \"/auth/forgot-password\", 1, \"text-primary\"], [1, \"text-center\", \"mt-2\"], [\"routerLink\", \"/auth/register\", 1, \"text-primary\"], [\"diameter\", \"20\"], [1, \"text-center\", \"mb-3\"], [\"color\", \"primary\", 2, \"font-size\", \"48px\", \"width\", \"48px\", \"height\", \"48px\"], [\"matInput\", \"\", \"formControlName\", \"twoFactorToken\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"w-100\", \"mt-2\", 3, \"click\"], [3, \"formGroup\"], [\"matInput\", \"\", \"formControlName\", \"identifier\", \"placeholder\", \"<EMAIL> or +1234567890\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"type\", \"button\", \"class\", \"submit-button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"type\", \"button\", 1, \"submit-button\", 3, \"click\", \"disabled\"], [\"matInput\", \"\", \"formControlName\", \"code\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"submit-button\", 3, \"click\", \"disabled\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n        i0.ɵɵtext(4, \"Welcome Back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\");\n        i0.ɵɵtext(6, \"Sign in to your secure account\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-icon\");\n        i0.ɵɵtext(9, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(10, \" Secure Login \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 4);\n        i0.ɵɵtemplate(12, LoginComponent_form_12_Template, 36, 8, \"form\", 5)(13, LoginComponent_form_13_Template, 23, 5, \"form\", 5)(14, LoginComponent_form_14_Template, 22, 4, \"form\", 6);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"ngIf\", !ctx.showOTPLogin && !ctx.showTwoFactor);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showTwoFactor);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showOTPLogin);\n      }\n    },\n    dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i6.MatFormField, i6.MatLabel, i6.MatError, i6.MatSuffix, i7.MatInput, i8.MatButton, i8.MatIconButton, i9.MatIcon, i10.MatProgressSpinner],\n    styles: [\".w-100[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.mt-2[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n}\\n\\n.mt-3[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.text-center[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  text-decoration: none;\\n}\\n.text-primary[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9hdXRoL2xvZ2luL2xvZ2luLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vTW9kdWxhciUyMGJhY2tlbmQlMjBzZWN1cmUlMjB1c2VyJTIwc3lzdGVtJTIwYW5kJTIwcGF5bWVudC9mcm9udGVuZC9zcmMvYXBwL2NvbXBvbmVudHMvYXV0aC9sb2dpbi9sb2dpbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFQTtFQUFTLFdBQUE7QUNBVDs7QURDQTtFQUFRLGtCQUFBO0FDR1I7O0FERkE7RUFBUSxnQkFBQTtBQ01SOztBRExBO0VBQVEsbUJBQUE7QUNTUjs7QURSQTtFQUFlLGtCQUFBO0FDWWY7O0FEWEE7RUFDRSxjQUFBO0VBQ0EscUJBQUE7QUNjRjtBRGJFO0VBQVUsMEJBQUE7QUNnQloiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBVc2UgZ2xvYmFsIHN0eWxlc1xuXG4udy0xMDAgeyB3aWR0aDogMTAwJTsgfVxuLm10LTIgeyBtYXJnaW4tdG9wOiAwLjVyZW07IH1cbi5tdC0zIHsgbWFyZ2luLXRvcDogMXJlbTsgfVxuLm1iLTMgeyBtYXJnaW4tYm90dG9tOiAxcmVtOyB9XG4udGV4dC1jZW50ZXIgeyB0ZXh0LWFsaWduOiBjZW50ZXI7IH1cbi50ZXh0LXByaW1hcnkgeyBcbiAgY29sb3I6ICMzZjUxYjU7IFxuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICY6aG92ZXIgeyB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTsgfVxufVxuIiwiLnctMTAwIHtcbiAgd2lkdGg6IDEwMCU7XG59XG5cbi5tdC0yIHtcbiAgbWFyZ2luLXRvcDogMC41cmVtO1xufVxuXG4ubXQtMyB7XG4gIG1hcmdpbi10b3A6IDFyZW07XG59XG5cbi5tYi0zIHtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbn1cblxuLnRleHQtY2VudGVyIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuXG4udGV4dC1wcmltYXJ5IHtcbiAgY29sb3I6ICMzZjUxYjU7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbn1cbi50ZXh0LXByaW1hcnk6aG92ZXIge1xuICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "LoginComponent_form_12_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "LoginComponent_form_12_Template_button_click_13_listener", "hidePassword", "ɵɵtemplate", "LoginComponent_form_12_mat_spinner_19_Template", "LoginComponent_form_12_span_20_Template", "LoginComponent_form_12_Template_button_click_24_listener", "toggleOTPLogin", "ɵɵproperty", "loginForm", "ɵɵadvance", "ɵɵtextInterpolate", "getFieldError", "loading", "LoginComponent_form_13_Template_form_ngSubmit_0_listener", "_r3", "onTwoFactorSubmit", "LoginComponent_form_13_mat_spinner_17_Template", "LoginComponent_form_13_span_18_Template", "LoginComponent_form_13_Template_button_click_19_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twoFactorForm", "LoginComponent_form_14_button_16_Template_button_click_0_listener", "_r5", "sendOTP", "LoginComponent_form_14_button_16_mat_spinner_1_Template", "LoginComponent_form_14_button_16_span_2_Template", "LoginComponent_form_14_div_17_Template_button_click_9_listener", "_r6", "loginWithOTP", "LoginComponent_form_14_div_17_mat_spinner_10_Template", "LoginComponent_form_14_div_17_span_11_Template", "LoginComponent_form_14_div_17_Template_button_click_12_listener", "otpForm", "LoginComponent_form_14_button_16_Template", "LoginComponent_form_14_div_17_Template", "LoginComponent_form_14_Template_button_click_18_listener", "_r4", "otpSent", "LoginComponent", "constructor", "formBuilder", "authService", "router", "route", "snackBar", "showOTPLogin", "showTwoFactor", "returnUrl", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "identifier", "code", "pattern", "twoFactorToken", "ngOnInit", "snapshot", "queryParams", "isAuthenticated", "navigate", "message", "open", "duration", "invalid", "markFormGroupTouched", "credentials", "value", "login", "subscribe", "next", "response", "requiresTwoFactor", "error", "get", "request", "type", "reset", "form", "fieldName", "field", "errors", "touched", "<PERSON><PERSON><PERSON><PERSON>", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "LoginComponent_form_12_Template", "LoginComponent_form_13_Template", "LoginComponent_form_14_Template"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\login\\login.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../../services/auth.service';\nimport { UserLogin, OTPRequest } from '../../../models/user.model';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss'],\n  standalone: false\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  otpForm: FormGroup;\n  twoFactorForm: FormGroup;\n  \n  loading = false;\n  hidePassword = true;\n  showOTPLogin = false;\n  showTwoFactor = false;\n  otpSent = false;\n  returnUrl = '';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]]\n    });\n\n    this.otpForm = this.formBuilder.group({\n      identifier: ['', [Validators.required]],\n      code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n\n    this.twoFactorForm = this.formBuilder.group({\n      twoFactorToken: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n  }\n\n  ngOnInit(): void {\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    \n    if (this.authService.isAuthenticated) {\n      this.router.navigate([this.returnUrl]);\n    }\n\n    const message = this.route.snapshot.queryParams['message'];\n    if (message) {\n      this.snackBar.open(message, 'Close', { duration: 5000 });\n    }\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched(this.loginForm);\n      return;\n    }\n\n    this.loading = true;\n    const credentials: UserLogin = this.loginForm.value;\n\n    this.authService.login(credentials).subscribe({\n      next: (response) => {\n        if (response.requiresTwoFactor) {\n          this.showTwoFactor = true;\n          this.snackBar.open('Please enter your two-factor authentication code', 'Close', {\n            duration: 5000\n          });\n        } else {\n          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n          this.router.navigate([this.returnUrl]);\n        }\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  onTwoFactorSubmit(): void {\n    if (this.twoFactorForm.invalid) {\n      this.markFormGroupTouched(this.twoFactorForm);\n      return;\n    }\n\n    this.loading = true;\n    const credentials: UserLogin = {\n      ...this.loginForm.value,\n      twoFactorToken: this.twoFactorForm.value.twoFactorToken\n    };\n\n    this.authService.login(credentials).subscribe({\n      next: (response) => {\n        this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Two-factor authentication failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n\n  sendOTP(): void {\n    const identifier = this.otpForm.get('identifier')?.value;\n    if (!identifier) {\n      this.snackBar.open('Please enter email or phone number', 'Close', { duration: 3000 });\n      return;\n    }\n\n    this.loading = true;\n    const request: OTPRequest = {\n      identifier,\n      type: 'login'\n    };\n\n    this.authService.sendOTP(request).subscribe({\n      next: () => {\n        this.otpSent = true;\n        this.snackBar.open('OTP sent successfully!', 'Close', { duration: 3000 });\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Failed to send OTP', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  loginWithOTP(): void {\n    if (this.otpForm.invalid) {\n      this.markFormGroupTouched(this.otpForm);\n      return;\n    }\n\n    this.loading = true;\n    const { identifier, code } = this.otpForm.value;\n\n    this.authService.loginWithOTP(identifier, code).subscribe({\n      next: () => {\n        this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'OTP login failed', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  toggleOTPLogin(): void {\n    this.showOTPLogin = !this.showOTPLogin;\n    this.showTwoFactor = false;\n    this.otpSent = false;\n    this.otpForm.reset();\n  }\n\n  backToLogin(): void {\n    this.showTwoFactor = false;\n    this.showOTPLogin = false;\n    this.otpSent = false;\n  }\n\n  getFieldError(form: FormGroup, fieldName: string): string {\n    const field = form.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['email']) return 'Please enter a valid email';\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['pattern']) return 'Please enter a valid format';\n    }\n    return '';\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n}\n", "<div class=\"auth-container\">\n  <div class=\"auth-card fade-in\">\n    <!-- Header -->\n    <div class=\"auth-header\">\n      <h1>Welcome Back</h1>\n      <p>Sign in to your secure account</p>\n      <div class=\"security-badge\">\n        <mat-icon>security</mat-icon>\n        Secure Login\n      </div>\n    </div>\n\n    <div class=\"auth-content\">\n      <!-- Regular Login Form -->\n      <form *ngIf=\"!showOTPLogin && !showTwoFactor\" [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Email Address</mat-label>\n          <input matInput type=\"email\" formControlName=\"email\" autocomplete=\"email\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error>{{ getFieldError(loginForm, 'email') }}</mat-error>\n        </mat-form-field>\n\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Password</mat-label>\n          <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\" autocomplete=\"current-password\">\n          <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError(loginForm, 'password') }}</mat-error>\n        </mat-form-field>\n\n        <button mat-raised-button color=\"primary\" type=\"submit\" class=\"submit-button\" [disabled]=\"loading\">\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!loading\">Sign In</span>\n        </button>\n\n        <div class=\"divider\">\n          <span>or</span>\n        </div>\n\n        <button mat-stroked-button type=\"button\" class=\"w-100\" (click)=\"toggleOTPLogin()\">\n          <mat-icon>sms</mat-icon>\n          Login with OTP\n        </button>\n\n        <div class=\"text-center mt-3\">\n          <a routerLink=\"/auth/forgot-password\" class=\"text-primary\">Forgot Password?</a>\n        </div>\n\n        <div class=\"text-center mt-2\">\n          <span>Don't have an account? </span>\n          <a routerLink=\"/auth/register\" class=\"text-primary\">Sign Up</a>\n        </div>\n      </form>\n\n      <!-- Two-Factor Authentication Form -->\n      <form *ngIf=\"showTwoFactor\" [formGroup]=\"twoFactorForm\" (ngSubmit)=\"onTwoFactorSubmit()\">\n        <div class=\"text-center mb-3\">\n          <mat-icon color=\"primary\" style=\"font-size: 48px; width: 48px; height: 48px;\">security</mat-icon>\n          <h3>Two-Factor Authentication</h3>\n          <p>Enter the 6-digit code from your authenticator app</p>\n        </div>\n\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Authentication Code</mat-label>\n          <input matInput formControlName=\"twoFactorToken\" placeholder=\"000000\" maxlength=\"6\" autocomplete=\"one-time-code\">\n          <mat-icon matSuffix>verified_user</mat-icon>\n          <mat-error>{{ getFieldError(twoFactorForm, 'twoFactorToken') }}</mat-error>\n        </mat-form-field>\n\n        <button mat-raised-button color=\"primary\" type=\"submit\" class=\"submit-button\" [disabled]=\"loading\">\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!loading\">Verify & Sign In</span>\n        </button>\n\n        <button mat-button type=\"button\" class=\"w-100 mt-2\" (click)=\"backToLogin()\">\n          <mat-icon>arrow_back</mat-icon>\n          Back to Login\n        </button>\n      </form>\n\n      <!-- OTP Login Form -->\n      <form *ngIf=\"showOTPLogin\" [formGroup]=\"otpForm\">\n        <div class=\"text-center mb-3\">\n          <mat-icon color=\"primary\" style=\"font-size: 48px; width: 48px; height: 48px;\">sms</mat-icon>\n          <h3>Login with OTP</h3>\n          <p>Enter your email or phone number to receive a one-time password</p>\n        </div>\n\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Email or Phone Number</mat-label>\n          <input matInput formControlName=\"identifier\" placeholder=\"<EMAIL> or +1234567890\">\n          <mat-icon matSuffix>contact_mail</mat-icon>\n          <mat-error>{{ getFieldError(otpForm, 'identifier') }}</mat-error>\n        </mat-form-field>\n\n        <button *ngIf=\"!otpSent\" mat-raised-button color=\"accent\" type=\"button\" class=\"submit-button\" \n                (click)=\"sendOTP()\" [disabled]=\"loading\">\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!loading\">Send OTP</span>\n        </button>\n\n        <div *ngIf=\"otpSent\">\n          <mat-form-field class=\"form-field\" appearance=\"outline\">\n            <mat-label>Enter OTP</mat-label>\n            <input matInput formControlName=\"code\" placeholder=\"000000\" maxlength=\"6\" autocomplete=\"one-time-code\">\n            <mat-icon matSuffix>lock</mat-icon>\n            <mat-error>{{ getFieldError(otpForm, 'code') }}</mat-error>\n          </mat-form-field>\n\n          <button mat-raised-button color=\"primary\" type=\"button\" class=\"submit-button\" \n                  (click)=\"loginWithOTP()\" [disabled]=\"loading\">\n            <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n            <span *ngIf=\"!loading\">Verify & Sign In</span>\n          </button>\n\n          <button mat-button type=\"button\" class=\"w-100 mt-2\" (click)=\"sendOTP()\">\n            <mat-icon>refresh</mat-icon>\n            Resend OTP\n          </button>\n        </div>\n\n        <button mat-button type=\"button\" class=\"w-100 mt-2\" (click)=\"toggleOTPLogin()\">\n          <mat-icon>arrow_back</mat-icon>\n          Back to Login\n        </button>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;IC+BzDC,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAE,cAAA,WAAuB;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAnBzCJ,EAAA,CAAAE,cAAA,cAA8F;IAAxBF,EAAA,CAAAK,UAAA,sBAAAC,yDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAEzFZ,EADF,CAAAE,cAAA,wBAAwD,gBAC3C;IAAAF,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACpCJ,EAAA,CAAAC,SAAA,eAA0E;IAC1ED,EAAA,CAAAE,cAAA,mBAAoB;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACpCJ,EAAA,CAAAE,cAAA,gBAAW;IAAAF,EAAA,CAAAG,MAAA,GAAuC;IACpDH,EADoD,CAAAI,YAAA,EAAY,EAC/C;IAGfJ,EADF,CAAAE,cAAA,wBAAwD,iBAC3C;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC/BJ,EAAA,CAAAC,SAAA,iBAAuH;IACvHD,EAAA,CAAAE,cAAA,kBAAuF;IAArDF,EAAA,CAAAK,UAAA,mBAAAQ,yDAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAK,YAAA,IAAAL,MAAA,CAAAK,YAAA;IAAA,EAAsC;IACtEd,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAG,MAAA,IAAoD;IAChEH,EADgE,CAAAI,YAAA,EAAW,EAClE;IACTJ,EAAA,CAAAE,cAAA,iBAAW;IAAAF,EAAA,CAAAG,MAAA,IAA0C;IACvDH,EADuD,CAAAI,YAAA,EAAY,EAClD;IAEjBJ,EAAA,CAAAE,cAAA,kBAAmG;IAEjGF,EADA,CAAAe,UAAA,KAAAC,8CAAA,0BAA2C,KAAAC,uCAAA,mBACpB;IACzBjB,EAAA,CAAAI,YAAA,EAAS;IAGPJ,EADF,CAAAE,cAAA,eAAqB,YACb;IAAAF,EAAA,CAAAG,MAAA,UAAE;IACVH,EADU,CAAAI,YAAA,EAAO,EACX;IAENJ,EAAA,CAAAE,cAAA,kBAAkF;IAA3BF,EAAA,CAAAK,UAAA,mBAAAa,yDAAA;MAAAlB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAU,cAAA,EAAgB;IAAA,EAAC;IAC/EnB,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACxBJ,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAGPJ,EADF,CAAAE,cAAA,eAA8B,aAC+B;IAAAF,EAAA,CAAAG,MAAA,wBAAgB;IAC7EH,EAD6E,CAAAI,YAAA,EAAI,EAC3E;IAGJJ,EADF,CAAAE,cAAA,eAA8B,YACtB;IAAAF,EAAA,CAAAG,MAAA,+BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpCJ,EAAA,CAAAE,cAAA,aAAoD;IAAAF,EAAA,CAAAG,MAAA,eAAO;IAE/DH,EAF+D,CAAAI,YAAA,EAAI,EAC3D,EACD;;;;IAvCuCJ,EAAA,CAAAoB,UAAA,cAAAX,MAAA,CAAAY,SAAA,CAAuB;IAKtDrB,EAAA,CAAAsB,SAAA,GAAuC;IAAvCtB,EAAA,CAAAuB,iBAAA,CAAAd,MAAA,CAAAe,aAAA,CAAAf,MAAA,CAAAY,SAAA,WAAuC;IAKlCrB,EAAA,CAAAsB,SAAA,GAA2C;IAA3CtB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAK,YAAA,uBAA2C;IAE/Cd,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAuB,iBAAA,CAAAd,MAAA,CAAAK,YAAA,mCAAoD;IAErDd,EAAA,CAAAsB,SAAA,GAA0C;IAA1CtB,EAAA,CAAAuB,iBAAA,CAAAd,MAAA,CAAAe,aAAA,CAAAf,MAAA,CAAAY,SAAA,cAA0C;IAGuBrB,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAAoB,UAAA,aAAAX,MAAA,CAAAgB,OAAA,CAAoB;IAClFzB,EAAA,CAAAsB,SAAA,EAAa;IAAbtB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAgB,OAAA,CAAa;IACpBzB,EAAA,CAAAsB,SAAA,EAAc;IAAdtB,EAAA,CAAAoB,UAAA,UAAAX,MAAA,CAAAgB,OAAA,CAAc;;;;;IAsCrBzB,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAE,cAAA,WAAuB;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAhBlDJ,EAAA,CAAAE,cAAA,cAAyF;IAAjCF,EAAA,CAAAK,UAAA,sBAAAqB,yDAAA;MAAA1B,EAAA,CAAAO,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,iBAAA,EAAmB;IAAA,EAAC;IAEpF5B,EADF,CAAAE,cAAA,cAA8B,mBACkD;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACjGJ,EAAA,CAAAE,cAAA,SAAI;IAAAF,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAG,MAAA,yDAAkD;IACvDH,EADuD,CAAAI,YAAA,EAAI,EACrD;IAGJJ,EADF,CAAAE,cAAA,wBAAwD,gBAC3C;IAAAF,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC1CJ,EAAA,CAAAC,SAAA,iBAAiH;IACjHD,EAAA,CAAAE,cAAA,oBAAoB;IAAAF,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5CJ,EAAA,CAAAE,cAAA,iBAAW;IAAAF,EAAA,CAAAG,MAAA,IAAoD;IACjEH,EADiE,CAAAI,YAAA,EAAY,EAC5D;IAEjBJ,EAAA,CAAAE,cAAA,kBAAmG;IAEjGF,EADA,CAAAe,UAAA,KAAAc,8CAAA,0BAA2C,KAAAC,uCAAA,mBACpB;IACzB9B,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAE,cAAA,kBAA4E;IAAxBF,EAAA,CAAAK,UAAA,mBAAA0B,yDAAA;MAAA/B,EAAA,CAAAO,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,WAAA,EAAa;IAAA,EAAC;IACzEhC,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,uBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACJ;;;;IAvBqBJ,EAAA,CAAAoB,UAAA,cAAAX,MAAA,CAAAwB,aAAA,CAA2B;IAWxCjC,EAAA,CAAAsB,SAAA,IAAoD;IAApDtB,EAAA,CAAAuB,iBAAA,CAAAd,MAAA,CAAAe,aAAA,CAAAf,MAAA,CAAAwB,aAAA,oBAAoD;IAGajC,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAAoB,UAAA,aAAAX,MAAA,CAAAgB,OAAA,CAAoB;IAClFzB,EAAA,CAAAsB,SAAA,EAAa;IAAbtB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAgB,OAAA,CAAa;IACpBzB,EAAA,CAAAsB,SAAA,EAAc;IAAdtB,EAAA,CAAAoB,UAAA,UAAAX,MAAA,CAAAgB,OAAA,CAAc;;;;;IA0BrBzB,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAE,cAAA,WAAuB;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAHxCJ,EAAA,CAAAE,cAAA,iBACiD;IAAzCF,EAAA,CAAAK,UAAA,mBAAA6B,kEAAA;MAAAlC,EAAA,CAAAO,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2B,OAAA,EAAS;IAAA,EAAC;IAEzBpC,EADA,CAAAe,UAAA,IAAAsB,uDAAA,0BAA2C,IAAAC,gDAAA,mBACpB;IACzBtC,EAAA,CAAAI,YAAA,EAAS;;;;IAHmBJ,EAAA,CAAAoB,UAAA,aAAAX,MAAA,CAAAgB,OAAA,CAAoB;IAChCzB,EAAA,CAAAsB,SAAA,EAAa;IAAbtB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAgB,OAAA,CAAa;IACpBzB,EAAA,CAAAsB,SAAA,EAAc;IAAdtB,EAAA,CAAAoB,UAAA,UAAAX,MAAA,CAAAgB,OAAA,CAAc;;;;;IAanBzB,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAE,cAAA,WAAuB;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAT9CJ,EAFJ,CAAAE,cAAA,UAAqB,wBACqC,gBAC3C;IAAAF,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAChCJ,EAAA,CAAAC,SAAA,gBAAuG;IACvGD,EAAA,CAAAE,cAAA,mBAAoB;IAAAF,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACnCJ,EAAA,CAAAE,cAAA,gBAAW;IAAAF,EAAA,CAAAG,MAAA,GAAoC;IACjDH,EADiD,CAAAI,YAAA,EAAY,EAC5C;IAEjBJ,EAAA,CAAAE,cAAA,iBACsD;IAA9CF,EAAA,CAAAK,UAAA,mBAAAkC,+DAAA;MAAAvC,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgC,YAAA,EAAc;IAAA,EAAC;IAE9BzC,EADA,CAAAe,UAAA,KAAA2B,qDAAA,0BAA2C,KAAAC,8CAAA,mBACpB;IACzB3C,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAE,cAAA,kBAAwE;IAApBF,EAAA,CAAAK,UAAA,mBAAAuC,gEAAA;MAAA5C,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2B,OAAA,EAAS;IAAA,EAAC;IACrEpC,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,oBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;IAbSJ,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAuB,iBAAA,CAAAd,MAAA,CAAAe,aAAA,CAAAf,MAAA,CAAAoC,OAAA,UAAoC;IAIhB7C,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAAoB,UAAA,aAAAX,MAAA,CAAAgB,OAAA,CAAoB;IACrCzB,EAAA,CAAAsB,SAAA,EAAa;IAAbtB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAgB,OAAA,CAAa;IACpBzB,EAAA,CAAAsB,SAAA,EAAc;IAAdtB,EAAA,CAAAoB,UAAA,UAAAX,MAAA,CAAAgB,OAAA,CAAc;;;;;;IA7BvBzB,EAFJ,CAAAE,cAAA,eAAiD,cACjB,mBACkD;IAAAF,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5FJ,EAAA,CAAAE,cAAA,SAAI;IAAAF,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvBJ,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAG,MAAA,sEAA+D;IACpEH,EADoE,CAAAI,YAAA,EAAI,EAClE;IAGJJ,EADF,CAAAE,cAAA,wBAAwD,gBAC3C;IAAAF,EAAA,CAAAG,MAAA,6BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC5CJ,EAAA,CAAAC,SAAA,iBAA4F;IAC5FD,EAAA,CAAAE,cAAA,oBAAoB;IAAAF,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3CJ,EAAA,CAAAE,cAAA,iBAAW;IAAAF,EAAA,CAAAG,MAAA,IAA0C;IACvDH,EADuD,CAAAI,YAAA,EAAY,EAClD;IAQjBJ,EANA,CAAAe,UAAA,KAAA+B,yCAAA,qBACiD,KAAAC,sCAAA,mBAK5B;IAoBrB/C,EAAA,CAAAE,cAAA,kBAA+E;IAA3BF,EAAA,CAAAK,UAAA,mBAAA2C,yDAAA;MAAAhD,EAAA,CAAAO,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAU,cAAA,EAAgB;IAAA,EAAC;IAC5EnB,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,uBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACJ;;;;IA5CoBJ,EAAA,CAAAoB,UAAA,cAAAX,MAAA,CAAAoC,OAAA,CAAqB;IAWjC7C,EAAA,CAAAsB,SAAA,IAA0C;IAA1CtB,EAAA,CAAAuB,iBAAA,CAAAd,MAAA,CAAAe,aAAA,CAAAf,MAAA,CAAAoC,OAAA,gBAA0C;IAG9C7C,EAAA,CAAAsB,SAAA,EAAc;IAAdtB,EAAA,CAAAoB,UAAA,UAAAX,MAAA,CAAAyC,OAAA,CAAc;IAMjBlD,EAAA,CAAAsB,SAAA,EAAa;IAAbtB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAyC,OAAA,CAAa;;;ADzF3B,OAAM,MAAOC,cAAc;EAYzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IAZlB,KAAAhC,OAAO,GAAG,KAAK;IACf,KAAAX,YAAY,GAAG,IAAI;IACnB,KAAA4C,YAAY,GAAG,KAAK;IACpB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAT,OAAO,GAAG,KAAK;IACf,KAAAU,SAAS,GAAG,EAAE;IASZ,IAAI,CAACvC,SAAS,GAAG,IAAI,CAACgC,WAAW,CAACQ,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC/D,UAAU,CAACgE,QAAQ,EAAEhE,UAAU,CAAC+D,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACjE,UAAU,CAACgE,QAAQ,EAAEhE,UAAU,CAACkE,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;IAEF,IAAI,CAACpB,OAAO,GAAG,IAAI,CAACQ,WAAW,CAACQ,KAAK,CAAC;MACpCK,UAAU,EAAE,CAAC,EAAE,EAAE,CAACnE,UAAU,CAACgE,QAAQ,CAAC,CAAC;MACvCI,IAAI,EAAE,CAAC,EAAE,EAAE,CAACpE,UAAU,CAACgE,QAAQ,EAAEhE,UAAU,CAACqE,OAAO,CAAC,SAAS,CAAC,CAAC;KAChE,CAAC;IAEF,IAAI,CAACnC,aAAa,GAAG,IAAI,CAACoB,WAAW,CAACQ,KAAK,CAAC;MAC1CQ,cAAc,EAAE,CAAC,EAAE,EAAE,CAACtE,UAAU,CAACgE,QAAQ,EAAEhE,UAAU,CAACqE,OAAO,CAAC,SAAS,CAAC,CAAC;KAC1E,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACV,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACe,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;IAE7E,IAAI,IAAI,CAAClB,WAAW,CAACmB,eAAe,EAAE;MACpC,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,IAAI,CAACd,SAAS,CAAC,CAAC;IACxC;IAEA,MAAMe,OAAO,GAAG,IAAI,CAACnB,KAAK,CAACe,QAAQ,CAACC,WAAW,CAAC,SAAS,CAAC;IAC1D,IAAIG,OAAO,EAAE;MACX,IAAI,CAAClB,QAAQ,CAACmB,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QAAEE,QAAQ,EAAE;MAAI,CAAE,CAAC;IAC1D;EACF;EAEAjE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACS,SAAS,CAACyD,OAAO,EAAE;MAC1B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC1D,SAAS,CAAC;MACzC;IACF;IAEA,IAAI,CAACI,OAAO,GAAG,IAAI;IACnB,MAAMuD,WAAW,GAAc,IAAI,CAAC3D,SAAS,CAAC4D,KAAK;IAEnD,IAAI,CAAC3B,WAAW,CAAC4B,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;UAC9B,IAAI,CAAC3B,aAAa,GAAG,IAAI;UACzB,IAAI,CAACF,QAAQ,CAACmB,IAAI,CAAC,kDAAkD,EAAE,OAAO,EAAE;YAC9EC,QAAQ,EAAE;WACX,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACpB,QAAQ,CAACmB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE,IAAI,CAACtB,MAAM,CAACmB,QAAQ,CAAC,CAAC,IAAI,CAACd,SAAS,CAAC,CAAC;QACxC;QACA,IAAI,CAACnC,OAAO,GAAG,KAAK;MACtB,CAAC;MACD8D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9B,QAAQ,CAACmB,IAAI,CAACW,KAAK,CAACZ,OAAO,IAAI,cAAc,EAAE,OAAO,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QAChF,IAAI,CAACpD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAG,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACK,aAAa,CAAC6C,OAAO,EAAE;MAC9B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC9C,aAAa,CAAC;MAC7C;IACF;IAEA,IAAI,CAACR,OAAO,GAAG,IAAI;IACnB,MAAMuD,WAAW,GAAc;MAC7B,GAAG,IAAI,CAAC3D,SAAS,CAAC4D,KAAK;MACvBZ,cAAc,EAAE,IAAI,CAACpC,aAAa,CAACgD,KAAK,CAACZ;KAC1C;IAED,IAAI,CAACf,WAAW,CAAC4B,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC5B,QAAQ,CAACmB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACpE,IAAI,CAACtB,MAAM,CAACmB,QAAQ,CAAC,CAAC,IAAI,CAACd,SAAS,CAAC,CAAC;QACtC,IAAI,CAACnC,OAAO,GAAG,KAAK;MACtB,CAAC;MACD8D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9B,QAAQ,CAACmB,IAAI,CAACW,KAAK,CAACZ,OAAO,IAAI,kCAAkC,EAAE,OAAO,EAAE;UAC/EE,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACpD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAW,OAAOA,CAAA;IACL,MAAM8B,UAAU,GAAG,IAAI,CAACrB,OAAO,CAAC2C,GAAG,CAAC,YAAY,CAAC,EAAEP,KAAK;IACxD,IAAI,CAACf,UAAU,EAAE;MACf,IAAI,CAACT,QAAQ,CAACmB,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACrF;IACF;IAEA,IAAI,CAACpD,OAAO,GAAG,IAAI;IACnB,MAAMgE,OAAO,GAAe;MAC1BvB,UAAU;MACVwB,IAAI,EAAE;KACP;IAED,IAAI,CAACpC,WAAW,CAAClB,OAAO,CAACqD,OAAO,CAAC,CAACN,SAAS,CAAC;MAC1CC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAClC,OAAO,GAAG,IAAI;QACnB,IAAI,CAACO,QAAQ,CAACmB,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACzE,IAAI,CAACpD,OAAO,GAAG,KAAK;MACtB,CAAC;MACD8D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9B,QAAQ,CAACmB,IAAI,CAACW,KAAK,CAACZ,OAAO,IAAI,oBAAoB,EAAE,OAAO,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QACtF,IAAI,CAACpD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAgB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACI,OAAO,CAACiC,OAAO,EAAE;MACxB,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAClC,OAAO,CAAC;MACvC;IACF;IAEA,IAAI,CAACpB,OAAO,GAAG,IAAI;IACnB,MAAM;MAAEyC,UAAU;MAAEC;IAAI,CAAE,GAAG,IAAI,CAACtB,OAAO,CAACoC,KAAK;IAE/C,IAAI,CAAC3B,WAAW,CAACb,YAAY,CAACyB,UAAU,EAAEC,IAAI,CAAC,CAACgB,SAAS,CAAC;MACxDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC3B,QAAQ,CAACmB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACpE,IAAI,CAACtB,MAAM,CAACmB,QAAQ,CAAC,CAAC,IAAI,CAACd,SAAS,CAAC,CAAC;QACtC,IAAI,CAACnC,OAAO,GAAG,KAAK;MACtB,CAAC;MACD8D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9B,QAAQ,CAACmB,IAAI,CAACW,KAAK,CAACZ,OAAO,IAAI,kBAAkB,EAAE,OAAO,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QACpF,IAAI,CAACpD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAN,cAAcA,CAAA;IACZ,IAAI,CAACuC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACT,OAAO,GAAG,KAAK;IACpB,IAAI,CAACL,OAAO,CAAC8C,KAAK,EAAE;EACtB;EAEA3D,WAAWA,CAAA;IACT,IAAI,CAAC2B,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,YAAY,GAAG,KAAK;IACzB,IAAI,CAACR,OAAO,GAAG,KAAK;EACtB;EAEA1B,aAAaA,CAACoE,IAAe,EAAEC,SAAiB;IAC9C,MAAMC,KAAK,GAAGF,IAAI,CAACJ,GAAG,CAACK,SAAS,CAAC;IACjC,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGF,SAAS,cAAc;MAC/D,IAAIC,KAAK,CAACC,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,4BAA4B;MAC9D,IAAID,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGF,SAAS,qBAAqBC,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,CAACE,cAAc,aAAa;MAC5H,IAAIH,KAAK,CAACC,MAAM,CAAC,SAAS,CAAC,EAAE,OAAO,6BAA6B;IACnE;IACA,OAAO,EAAE;EACX;EAEQhB,oBAAoBA,CAACmB,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAACV,GAAG,CAACe,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qCApLUvD,cAAc,EAAAnD,EAAA,CAAA2G,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7G,EAAA,CAAA2G,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/G,EAAA,CAAA2G,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAjH,EAAA,CAAA2G,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAAlH,EAAA,CAAA2G,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdlE,cAAc;IAAAmE,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTrB7H,EAJN,CAAAE,cAAA,aAA4B,aACK,aAEJ,SACnB;QAAAF,EAAA,CAAAG,MAAA,mBAAY;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACrBJ,EAAA,CAAAE,cAAA,QAAG;QAAAF,EAAA,CAAAG,MAAA,qCAA8B;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAEnCJ,EADF,CAAAE,cAAA,aAA4B,eAChB;QAAAF,EAAA,CAAAG,MAAA,eAAQ;QAAAH,EAAA,CAAAI,YAAA,EAAW;QAC7BJ,EAAA,CAAAG,MAAA,sBACF;QACFH,EADE,CAAAI,YAAA,EAAM,EACF;QAENJ,EAAA,CAAAE,cAAA,cAA0B;QAsExBF,EApEA,CAAAe,UAAA,KAAAgH,+BAAA,mBAA8F,KAAAC,+BAAA,mBA0CL,KAAAC,+BAAA,mBA0BxC;QA+CvDjI,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;QAnHOJ,EAAA,CAAAsB,SAAA,IAAqC;QAArCtB,EAAA,CAAAoB,UAAA,UAAA0G,GAAA,CAAApE,YAAA,KAAAoE,GAAA,CAAAnE,aAAA,CAAqC;QA0CrC3D,EAAA,CAAAsB,SAAA,EAAmB;QAAnBtB,EAAA,CAAAoB,UAAA,SAAA0G,GAAA,CAAAnE,aAAA,CAAmB;QA0BnB3D,EAAA,CAAAsB,SAAA,EAAkB;QAAlBtB,EAAA,CAAAoB,UAAA,SAAA0G,GAAA,CAAApE,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}