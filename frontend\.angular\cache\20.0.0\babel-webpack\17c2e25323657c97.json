{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Attribute } from '../attribute-BWp59EjE.mjs';\n\n/** All properties that are used by jsaction. */\nconst Property = {\n  /**\n   * The parsed value of the jsaction attribute is stored in this\n   * property on the DOM node. The parsed value is an Object. The\n   * property names of the object are the events; the values are the\n   * names of the actions. This property is attached even on nodes\n   * that don't have a jsaction attribute as an optimization, because\n   * property lookup is faster than attribute access.\n   */\n  JSACTION: '__jsaction',\n  /**\n   * The owner property references an a logical owner for a DOM node. JSAction\n   * will follow this reference instead of parentNode when traversing the DOM\n   * to find jsaction attributes. This allows overlaying a logical structure\n   * over a document where the DOM structure can't reflect that structure.\n   */\n  OWNER: '__owner'\n};\n\n/**\n * Map from jsaction annotation to a parsed map from event name to action name.\n */\nconst parseCache = {};\n/**\n * Reads the jsaction parser cache from the given DOM Element.\n */\nfunction get(element) {\n  return element[Property.JSACTION];\n}\n/**\n * Reads the jsaction parser cache for the given DOM element. If no cache is yet present,\n * creates an empty one.\n */\nfunction getDefaulted(element) {\n  const cache = get(element) ?? {};\n  set(element, cache);\n  return cache;\n}\n/**\n * Writes the jsaction parser cache to the given DOM Element.\n */\nfunction set(element, actionMap) {\n  element[Property.JSACTION] = actionMap;\n}\n/**\n * Looks up the parsed action map from the source jsaction attribute value.\n *\n * @param text Unparsed jsaction attribute value.\n * @return Parsed jsaction attribute value, if already present in the cache.\n */\nfunction getParsed(text) {\n  return parseCache[text];\n}\n/**\n * Inserts the parse result for the given source jsaction value into the cache.\n *\n * @param text Unparsed jsaction attribute value.\n * @param parsed Attribute value parsed into the action map.\n */\nfunction setParsed(text, parsed) {\n  parseCache[text] = parsed;\n}\n\n/*\n * Names of events that are special to jsaction. These are not all\n * event types that are legal to use in either HTML or the addEvent()\n * API, but these are the ones that are treated specially. All other\n * DOM events can be used in either addEvent() or in the value of the\n * jsaction attribute. Beware of browser specific events or events\n * that don't bubble though: If they are not mentioned here, then\n * event contract doesn't work around their peculiarities.\n */\nconst EventType = {\n  /**\n   * The click event. In addEvent() refers to all click events, in the\n   * jsaction attribute it refers to the unmodified click and Enter/Space\n   * keypress events.  In the latter case, a jsaction click will be triggered,\n   * for accessibility reasons.  See clickmod and clickonly, below.\n   */\n  CLICK: 'click',\n  /**\n   * Specifies the jsaction for a modified click event (i.e. a mouse\n   * click with the modifier key Cmd/Ctrl pressed). This event isn't\n   * separately enabled in addEvent(), because in the DOM, it's just a\n   * click event.\n   */\n  CLICKMOD: 'clickmod',\n  /**\n   * The dblclick event.\n   */\n  DBLCLICK: 'dblclick',\n  /**\n   * Focus doesn't bubble, but you can use it in addEvent() and\n   * jsaction anyway. EventContract does the right thing under the\n   * hood.\n   */\n  FOCUS: 'focus',\n  /**\n   * This event only exists in IE. For addEvent() and jsaction, use\n   * focus instead; EventContract does the right thing even though\n   * focus doesn't bubble.\n   */\n  FOCUSIN: 'focusin',\n  /**\n   * Analog to focus.\n   */\n  BLUR: 'blur',\n  /**\n   * Analog to focusin.\n   */\n  FOCUSOUT: 'focusout',\n  /**\n   * Submit doesn't bubble, so it cannot be used with event\n   * contract. However, the browser helpfully fires a click event on\n   * the submit button of a form (even if the form is not submitted by\n   * a click on the submit button). So you should handle click on the\n   * submit button instead.\n   */\n  SUBMIT: 'submit',\n  /**\n   * The keydown event. In addEvent() and non-click jsaction it represents the\n   * regular DOM keydown event. It represents click actions in non-Gecko\n   * browsers.\n   */\n  KEYDOWN: 'keydown',\n  /**\n   * The keypress event. In addEvent() and non-click jsaction it represents the\n   * regular DOM keypress event. It represents click actions in Gecko browsers.\n   */\n  KEYPRESS: 'keypress',\n  /**\n   * The keyup event. In addEvent() and non-click jsaction it represents the\n   * regular DOM keyup event. It represents click actions in non-Gecko\n   * browsers.\n   */\n  KEYUP: 'keyup',\n  /**\n   * The mouseover event. Can either be used directly or used implicitly to\n   * capture mouseenter events. In addEvent(), it represents a regular DOM\n   * mouseover event.\n   */\n  MOUSEOVER: 'mouseover',\n  /**\n   * The mouseout event. Can either be used directly or used implicitly to\n   * capture mouseover events. In addEvent(), it represents a regular DOM\n   * mouseout event.\n   */\n  MOUSEOUT: 'mouseout',\n  /**\n   * The mouseenter event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  MOUSEENTER: 'mouseenter',\n  /**\n   * The mouseleave event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  MOUSELEAVE: 'mouseleave',\n  /**\n   * The pointerover event. Can either be used directly or used implicitly to\n   * capture pointerenter events. In addEvent(), it represents a regular DOM\n   * pointerover event.\n   */\n  POINTEROVER: 'pointerover',\n  /**\n   * The pointerout event. Can either be used directly or used implicitly to\n   * capture pointerover events. In addEvent(), it represents a regular DOM\n   * pointerout event.\n   */\n  POINTEROUT: 'pointerout',\n  /**\n   * The pointerenter event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  POINTERENTER: 'pointerenter',\n  /**\n   * The pointerleave event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  POINTERLEAVE: 'pointerleave',\n  /**\n   * The error event. The error event doesn't bubble, but you can use it in\n   * addEvent() and jsaction anyway. EventContract does the right thing under\n   * the hood (except in IE8 which does not use error events).\n   */\n  ERROR: 'error',\n  /**\n   * The load event. The load event doesn't bubble, but you can use it in\n   * addEvent() and jsaction anyway. EventContract does the right thing\n   * under the hood.\n   */\n  LOAD: 'load',\n  /**\n   * The touchstart event. Bubbles, will only ever fire in browsers with\n   * touch support.\n   */\n  TOUCHSTART: 'touchstart',\n  /**\n   * The touchend event. Bubbles, will only ever fire in browsers with\n   * touch support.\n   */\n  TOUCHEND: 'touchend',\n  /**\n   * The touchmove event. Bubbles, will only ever fire in browsers with\n   * touch support.\n   */\n  TOUCHMOVE: 'touchmove',\n  /**\n   * The toggle event. The toggle event doesn't bubble, but you can use it in\n   * addEvent() and jsaction anyway. EventContract does the right thing\n   * under the hood.\n   */\n  TOGGLE: 'toggle'\n};\n/** All event types that do not bubble or capture and need a polyfill. */\nconst MOUSE_SPECIAL_EVENT_TYPES = [EventType.MOUSEENTER, EventType.MOUSELEAVE, 'pointerenter', 'pointerleave'];\n/** All event types that are registered in the bubble phase. */\nconst BUBBLE_EVENT_TYPES = [EventType.CLICK, EventType.DBLCLICK, EventType.FOCUSIN, EventType.FOCUSOUT, EventType.KEYDOWN, EventType.KEYUP, EventType.KEYPRESS, EventType.MOUSEOVER, EventType.MOUSEOUT, EventType.SUBMIT, EventType.TOUCHSTART, EventType.TOUCHEND, EventType.TOUCHMOVE, 'touchcancel', 'auxclick', 'change', 'compositionstart', 'compositionupdate', 'compositionend', 'beforeinput', 'input', 'select', 'copy', 'cut', 'paste', 'mousedown', 'mouseup', 'wheel', 'contextmenu', 'dragover', 'dragenter', 'dragleave', 'drop', 'dragstart', 'dragend', 'pointerdown', 'pointermove', 'pointerup', 'pointercancel', 'pointerover', 'pointerout', 'gotpointercapture', 'lostpointercapture',\n// Video events.\n'ended', 'loadedmetadata',\n// Page visibility events.\n'pagehide', 'pageshow', 'visibilitychange',\n// Content visibility events.\n'beforematch'];\n/** All event types that are registered in the capture phase. */\nconst CAPTURE_EVENT_TYPES = [EventType.FOCUS, EventType.BLUR, EventType.ERROR, EventType.LOAD, EventType.TOGGLE];\n/**\n * Whether or not an event type should be registered in the capture phase.\n * @param eventType\n * @returns bool\n */\nconst isCaptureEventType = eventType => CAPTURE_EVENT_TYPES.indexOf(eventType) >= 0;\n/** All event types that are registered early.  */\nconst EARLY_EVENT_TYPES = BUBBLE_EVENT_TYPES.concat(CAPTURE_EVENT_TYPES);\n/**\n * Whether or not an event type is registered in the early contract.\n */\nconst isEarlyEventType = eventType => EARLY_EVENT_TYPES.indexOf(eventType) >= 0;\n\n/**\n * Gets a browser event type, if it would differ from the JSAction event type.\n */\nfunction getBrowserEventType(eventType) {\n  // Mouseenter and mouseleave events are not handled directly because they\n  // are not available everywhere. In browsers where they are available, they\n  // don't bubble and aren't visible at the container boundary. Instead, we\n  // synthesize the mouseenter and mouseleave events from mouseover and\n  // mouseout events, respectively. Cf. eventcontract.js.\n  if (eventType === EventType.MOUSEENTER) {\n    return EventType.MOUSEOVER;\n  } else if (eventType === EventType.MOUSELEAVE) {\n    return EventType.MOUSEOUT;\n  } else if (eventType === EventType.POINTERENTER) {\n    return EventType.POINTEROVER;\n  } else if (eventType === EventType.POINTERLEAVE) {\n    return EventType.POINTEROUT;\n  }\n  return eventType;\n}\n/**\n * Registers the event handler function with the given DOM element for\n * the given event type.\n *\n * @param element The element.\n * @param eventType The event type.\n * @param handler The handler function to install.\n * @param passive A boolean value that, if `true`, indicates that the function\n *     specified by `handler` will never call `preventDefault()`.\n * @return Information needed to uninstall the event handler eventually.\n */\nfunction addEventListener(element, eventType, handler, passive) {\n  // All event handlers are registered in the bubbling\n  // phase.\n  //\n  // All browsers support focus and blur, but these events only are propagated\n  // in the capture phase. Very legacy browsers do not support focusin or\n  // focusout.\n  //\n  // It would be a bad idea to register all event handlers in the\n  // capture phase because then regular onclick handlers would not be\n  // executed at all on events that trigger a jsaction. That's not\n  // entirely what we want, at least for now.\n  //\n  // Error and load events (i.e. on images) do not bubble so they are also\n  // handled in the capture phase.\n  let capture = false;\n  if (isCaptureEventType(eventType)) {\n    capture = true;\n  }\n  const options = typeof passive === 'boolean' ? {\n    capture,\n    passive\n  } : capture;\n  element.addEventListener(eventType, handler, options);\n  return {\n    eventType,\n    handler,\n    capture,\n    passive\n  };\n}\n/**\n * Removes the event handler for the given event from the element.\n * the given event type.\n *\n * @param element The element.\n * @param info The information needed to deregister the handler, as returned by\n *     addEventListener(), above.\n */\nfunction removeEventListener(element, info) {\n  if (element.removeEventListener) {\n    // It's worth noting that some browser releases have been inconsistent on this, and unless\n    // you have specific reasons otherwise, it's probably wise to use the same values used for\n    // the call to addEventListener() when calling removeEventListener().\n    const options = typeof info.passive === 'boolean' ? {\n      capture: info.capture\n    } : info.capture;\n    element.removeEventListener(info.eventType, info.handler, options);\n    // `detachEvent` is an old DOM API.\n  } else if (element.detachEvent) {\n    // `detachEvent` is an old DOM API.\n    element.detachEvent(`on${info.eventType}`, info.handler);\n  }\n}\n/**\n * Prevents the default action of an event.\n * @param e The event to prevent the default action for.\n */\nfunction preventDefault(e) {\n  e.preventDefault ? e.preventDefault() : e.returnValue = false;\n}\n/**\n * Whether we are on a Mac. Not pulling in useragent just for this.\n */\nlet isMac = typeof navigator !== 'undefined' && /Macintosh/.test(navigator.userAgent);\n/**\n * Determines and returns whether the given event (which is assumed to be a\n * click event) is a middle click.\n * NOTE: There is not a consistent way to identify middle click\n * http://www.unixpapa.com/js/mouse.html\n */\nfunction isMiddleClick(e) {\n  return (\n    // `which` is an old DOM API.\n    e.which === 2 ||\n    // `which` is an old DOM API.\n    e.which == null &&\n    // `button` is an old DOM API.\n    e.button === 4 // middle click for IE\n  );\n}\n/**\n * Determines and returns whether the given event (which is assumed\n * to be a click event) is modified. A middle click is considered a modified\n * click to retain the default browser action, which opens a link in a new tab.\n * @param e The event.\n * @return Whether the given event is modified.\n */\nfunction isModifiedClickEvent(e) {\n  return (\n    // `metaKey` is an old DOM API.\n    isMac && e.metaKey ||\n    // `ctrlKey` is an old DOM API.\n    !isMac && e.ctrlKey || isMiddleClick(e) ||\n    // `shiftKey` is an old DOM API.\n    e.shiftKey\n  );\n}\n/**\n * Determines whether the event corresponds to a non-bubbling mouse\n * event type (mouseenter, mouseleave, pointerenter, and pointerleave).\n *\n * During mouseover (mouseenter) and pointerover (pointerenter), the\n * relatedTarget is the element being entered from. During mouseout (mouseleave)\n * and pointerout (pointerleave), the relatedTarget is the element being exited\n * to.\n *\n * In both cases, if relatedTarget is outside target, then the corresponding\n * special event has occurred, otherwise it hasn't.\n *\n * @param e The mouseover/mouseout event.\n * @param type The type of the mouse special event.\n * @param element The element on which the jsaction for the\n *     mouseenter/mouseleave event is defined.\n * @return True if the event is a mouseenter/mouseleave event.\n */\nfunction isMouseSpecialEvent(e, type, element) {\n  // `relatedTarget` is an old DOM API.\n  const related = e.relatedTarget;\n  return (e.type === EventType.MOUSEOVER && type === EventType.MOUSEENTER || e.type === EventType.MOUSEOUT && type === EventType.MOUSELEAVE || e.type === EventType.POINTEROVER && type === EventType.POINTERENTER || e.type === EventType.POINTEROUT && type === EventType.POINTERLEAVE) && (!related || related !== element && !element.contains(related));\n}\n/**\n * Creates a new EventLike object for a mouseenter/mouseleave event that's\n * derived from the original corresponding mouseover/mouseout event.\n * @param e The event.\n * @param target The element on which the jsaction for the mouseenter/mouseleave\n *     event is defined.\n * @return A modified event-like object copied from the event object passed into\n *     this function.\n */\nfunction createMouseSpecialEvent(e, target) {\n  // We have to create a copy of the event object because we need to mutate\n  // its fields. We do this for the special mouse events because the event\n  // target needs to be retargeted to the action element rather than the real\n  // element (since we are simulating the special mouse events with mouseover/\n  // mouseout).\n  //\n  // Since we're making a copy anyways, we might as well attempt to convert\n  // this event into a pseudo-real mouseenter/mouseleave event by adjusting\n  // its type.\n  //\n  const copy = {};\n  for (const property in e) {\n    if (property === 'srcElement' || property === 'target') {\n      continue;\n    }\n    const key = property;\n    // Making a copy requires iterating through all properties of `Event`.\n    const value = e[key];\n    if (typeof value === 'function') {\n      continue;\n    }\n    // Value should be the expected type, but the value of `key` is not known\n    // statically.\n    copy[key] = value;\n  }\n  if (e.type === EventType.MOUSEOVER) {\n    copy['type'] = EventType.MOUSEENTER;\n  } else if (e.type === EventType.MOUSEOUT) {\n    copy['type'] = EventType.MOUSELEAVE;\n  } else if (e.type === EventType.POINTEROVER) {\n    copy['type'] = EventType.POINTERENTER;\n  } else {\n    copy['type'] = EventType.POINTERLEAVE;\n  }\n  copy['target'] = copy['srcElement'] = target;\n  copy['bubbles'] = false;\n  copy['_originalEvent'] = e;\n  return copy;\n}\n\n/**\n * Whether the user agent is running on iOS.\n */\nconst isIos = typeof navigator !== 'undefined' && /iPhone|iPad|iPod/.test(navigator.userAgent);\n/**\n * A class representing a container node and all the event handlers\n * installed on it. Used so that handlers can be cleaned up if the\n * container is removed from the contract.\n */\nclass EventContractContainer {\n  element;\n  /**\n   * Array of event handlers and their corresponding event types that are\n   * installed on this container.\n   *\n   */\n  handlerInfos = [];\n  /**\n   * @param element The container Element.\n   */\n  constructor(element) {\n    this.element = element;\n  }\n  /**\n   * Installs the provided installer on the element owned by this container,\n   * and maintains a reference to resulting handler in order to remove it\n   * later if desired.\n   */\n  addEventListener(eventType, getHandler, passive) {\n    // In iOS, event bubbling doesn't happen automatically in any DOM element,\n    // unless it has an onclick attribute or DOM event handler attached to it.\n    // This breaks JsAction in some cases. See \"Making Elements Clickable\"\n    // section at http://goo.gl/2VoGnB.\n    //\n    // A workaround for this issue is to change the CSS cursor style to 'pointer'\n    // for the container element, which magically turns on event bubbling. This\n    // solution is described in the comments section at http://goo.gl/6pEO1z.\n    //\n    // We use a navigator.userAgent check here as this problem is present both\n    // on Mobile Safari and thin WebKit wrappers, such as Chrome for iOS.\n    if (isIos) {\n      this.element.style.cursor = 'pointer';\n    }\n    this.handlerInfos.push(addEventListener(this.element, eventType, getHandler(this.element), passive));\n  }\n  /**\n   * Removes all the handlers installed on this container.\n   */\n  cleanUp() {\n    for (let i = 0; i < this.handlerInfos.length; i++) {\n      removeEventListener(this.element, this.handlerInfos[i]);\n    }\n    this.handlerInfos = [];\n  }\n}\nconst Char = {\n  /**\n   * The separator between the event name and action in the jsaction\n   * attribute value.\n   */\n  EVENT_ACTION_SEPARATOR: ':'\n};\n\n/** Added for readability when accessing stable property names. */\nfunction getEventType(eventInfo) {\n  return eventInfo.eventType;\n}\n/** Added for readability when accessing stable property names. */\nfunction setEventType(eventInfo, eventType) {\n  eventInfo.eventType = eventType;\n}\n/** Added for readability when accessing stable property names. */\nfunction getEvent(eventInfo) {\n  return eventInfo.event;\n}\n/** Added for readability when accessing stable property names. */\nfunction setEvent(eventInfo, event) {\n  eventInfo.event = event;\n}\n/** Added for readability when accessing stable property names. */\nfunction getTargetElement(eventInfo) {\n  return eventInfo.targetElement;\n}\n/** Added for readability when accessing stable property names. */\nfunction setTargetElement(eventInfo, targetElement) {\n  eventInfo.targetElement = targetElement;\n}\n/** Added for readability when accessing stable property names. */\nfunction getContainer(eventInfo) {\n  return eventInfo.eic;\n}\n/** Added for readability when accessing stable property names. */\nfunction setContainer(eventInfo, container) {\n  eventInfo.eic = container;\n}\n/** Added for readability when accessing stable property names. */\nfunction getTimestamp(eventInfo) {\n  return eventInfo.timeStamp;\n}\n/** Added for readability when accessing stable property names. */\nfunction setTimestamp(eventInfo, timestamp) {\n  eventInfo.timeStamp = timestamp;\n}\n/** Added for readability when accessing stable property names. */\nfunction getAction(eventInfo) {\n  return eventInfo.eia;\n}\n/** Added for readability when accessing stable property names. */\nfunction setAction(eventInfo, actionName, actionElement) {\n  eventInfo.eia = [actionName, actionElement];\n}\n/** Added for readability when accessing stable property names. */\nfunction unsetAction(eventInfo) {\n  eventInfo.eia = undefined;\n}\n/** Added for readability when accessing stable property names. */\nfunction getActionElement(actionInfo) {\n  return actionInfo[1];\n}\n/** Added for readability when accessing stable property names. */\nfunction getIsReplay(eventInfo) {\n  return eventInfo.eirp;\n}\n/** Added for readability when accessing stable property names. */\nfunction setIsReplay(eventInfo, replay) {\n  eventInfo.eirp = replay;\n}\n/** Added for readability when accessing stable property names. */\nfunction getResolved(eventInfo) {\n  return eventInfo.eir;\n}\n/** Added for readability when accessing stable property names. */\nfunction setResolved(eventInfo, resolved) {\n  eventInfo.eir = resolved;\n}\n/** Clones an `EventInfo` */\nfunction cloneEventInfo(eventInfo) {\n  return {\n    eventType: eventInfo.eventType,\n    event: eventInfo.event,\n    targetElement: eventInfo.targetElement,\n    eic: eventInfo.eic,\n    eia: eventInfo.eia,\n    timeStamp: eventInfo.timeStamp,\n    eirp: eventInfo.eirp,\n    eiack: eventInfo.eiack,\n    eir: eventInfo.eir\n  };\n}\n/**\n * Utility function for creating an `EventInfo`.\n *\n * This can be used from code-size sensitive compilation units, as taking\n * parameters vs. an `Object` literal reduces code size.\n */\nfunction createEventInfoFromParameters(eventType, event, targetElement, container, timestamp, action, isReplay, a11yClickKey) {\n  return {\n    eventType,\n    event,\n    targetElement,\n    eic: container,\n    timeStamp: timestamp,\n    eia: action,\n    eirp: isReplay,\n    eiack: a11yClickKey\n  };\n}\n/**\n * Utility class around an `EventInfo`.\n *\n * This should be used in compilation units that are less sensitive to code\n * size.\n */\nclass EventInfoWrapper {\n  eventInfo;\n  constructor(eventInfo) {\n    this.eventInfo = eventInfo;\n  }\n  getEventType() {\n    return getEventType(this.eventInfo);\n  }\n  setEventType(eventType) {\n    setEventType(this.eventInfo, eventType);\n  }\n  getEvent() {\n    return getEvent(this.eventInfo);\n  }\n  setEvent(event) {\n    setEvent(this.eventInfo, event);\n  }\n  getTargetElement() {\n    return getTargetElement(this.eventInfo);\n  }\n  setTargetElement(targetElement) {\n    setTargetElement(this.eventInfo, targetElement);\n  }\n  getContainer() {\n    return getContainer(this.eventInfo);\n  }\n  setContainer(container) {\n    setContainer(this.eventInfo, container);\n  }\n  getTimestamp() {\n    return getTimestamp(this.eventInfo);\n  }\n  setTimestamp(timestamp) {\n    setTimestamp(this.eventInfo, timestamp);\n  }\n  getAction() {\n    const action = getAction(this.eventInfo);\n    if (!action) return undefined;\n    return {\n      name: action[0],\n      element: action[1]\n    };\n  }\n  setAction(action) {\n    if (!action) {\n      unsetAction(this.eventInfo);\n      return;\n    }\n    setAction(this.eventInfo, action.name, action.element);\n  }\n  getIsReplay() {\n    return getIsReplay(this.eventInfo);\n  }\n  setIsReplay(replay) {\n    setIsReplay(this.eventInfo, replay);\n  }\n  getResolved() {\n    return getResolved(this.eventInfo);\n  }\n  setResolved(resolved) {\n    setResolved(this.eventInfo, resolved);\n  }\n  clone() {\n    return new EventInfoWrapper(cloneEventInfo(this.eventInfo));\n  }\n}\n\n/**\n * Since maps from event to action are immutable we can use a single map\n * to represent the empty map.\n */\nconst EMPTY_ACTION_MAP = {};\n/**\n * This regular expression matches a semicolon.\n */\nconst REGEXP_SEMICOLON = /\\s*;\\s*/;\n/** If no event type is defined, defaults to `click`. */\nconst DEFAULT_EVENT_TYPE = EventType.CLICK;\n/** Resolves actions for Events. */\nclass ActionResolver {\n  a11yClickSupport = false;\n  clickModSupport = true;\n  syntheticMouseEventSupport;\n  updateEventInfoForA11yClick = undefined;\n  preventDefaultForA11yClick = undefined;\n  populateClickOnlyAction = undefined;\n  constructor({\n    syntheticMouseEventSupport = false,\n    clickModSupport = true\n  } = {}) {\n    this.syntheticMouseEventSupport = syntheticMouseEventSupport;\n    this.clickModSupport = clickModSupport;\n  }\n  resolveEventType(eventInfo) {\n    // We distinguish modified and plain clicks in order to support the\n    // default browser behavior of modified clicks on links; usually to\n    // open the URL of the link in new tab or new window on ctrl/cmd\n    // click. A DOM 'click' event is mapped to the jsaction 'click'\n    // event iff there is no modifier present on the event. If there is\n    // a modifier, it's mapped to 'clickmod' instead.\n    //\n    // It's allowed to omit the event in the jsaction attribute. In that\n    // case, 'click' is assumed. Thus the following two are equivalent:\n    //\n    //   <a href=\"someurl\" jsaction=\"gna.fu\">\n    //   <a href=\"someurl\" jsaction=\"click:gna.fu\">\n    //\n    // For unmodified clicks, EventContract invokes the jsaction\n    // 'gna.fu'. For modified clicks, EventContract won't find a\n    // suitable action and leave the event to be handled by the\n    // browser.\n    //\n    // In order to also invoke a jsaction handler for a modifier click,\n    // 'clickmod' needs to be used:\n    //\n    //   <a href=\"someurl\" jsaction=\"clickmod:gna.fu\">\n    //\n    // EventContract invokes the jsaction 'gna.fu' for modified\n    // clicks. Unmodified clicks are left to the browser.\n    //\n    // In order to set up the event contract to handle both clickonly and\n    // clickmod, only addEvent(EventType.CLICK) is necessary.\n    //\n    // In order to set up the event contract to handle click,\n    // addEvent() is necessary for CLICK, KEYDOWN, and KEYPRESS event types.  If\n    // a11y click support is enabled, addEvent() will set up the appropriate key\n    // event handler automatically.\n    if (this.clickModSupport && getEventType(eventInfo) === EventType.CLICK && isModifiedClickEvent(getEvent(eventInfo))) {\n      setEventType(eventInfo, EventType.CLICKMOD);\n    } else if (this.a11yClickSupport) {\n      this.updateEventInfoForA11yClick(eventInfo);\n    }\n  }\n  resolveAction(eventInfo) {\n    if (getResolved(eventInfo)) {\n      return;\n    }\n    this.populateAction(eventInfo, getTargetElement(eventInfo));\n    setResolved(eventInfo, true);\n  }\n  resolveParentAction(eventInfo) {\n    const action = getAction(eventInfo);\n    const actionElement = action && getActionElement(action);\n    unsetAction(eventInfo);\n    const parentNode = actionElement && this.getParentNode(actionElement);\n    if (!parentNode) {\n      return;\n    }\n    this.populateAction(eventInfo, parentNode);\n  }\n  /**\n   * Searches for a jsaction that the DOM event maps to and creates an\n   * object containing event information used for dispatching by\n   * jsaction.Dispatcher. This method populates the `action` and `actionElement`\n   * fields of the EventInfo object passed in by finding the first\n   * jsaction attribute above the target Node of the event, and below\n   * the container Node, that specifies a jsaction for the event\n   * type. If no such jsaction is found, then action is undefined.\n   *\n   * @param eventInfo `EventInfo` to set `action` and `actionElement` if an\n   *    action is found on any `Element` in the path of the `Event`.\n   */\n  populateAction(eventInfo, currentTarget) {\n    let actionElement = currentTarget;\n    while (actionElement && actionElement !== getContainer(eventInfo)) {\n      if (actionElement.nodeType === Node.ELEMENT_NODE) {\n        this.populateActionOnElement(actionElement, eventInfo);\n      }\n      if (getAction(eventInfo)) {\n        // An event is handled by at most one jsaction. Thus we stop at the\n        // first matching jsaction specified in a jsaction attribute up the\n        // ancestor chain of the event target node.\n        break;\n      }\n      actionElement = this.getParentNode(actionElement);\n    }\n    const action = getAction(eventInfo);\n    if (!action) {\n      // No action found.\n      return;\n    }\n    if (this.a11yClickSupport) {\n      this.preventDefaultForA11yClick(eventInfo);\n    }\n    // We attempt to handle the mouseenter/mouseleave events here by\n    // detecting whether the mouseover/mouseout events correspond to\n    // entering/leaving an element.\n    if (this.syntheticMouseEventSupport) {\n      if (getEventType(eventInfo) === EventType.MOUSEENTER || getEventType(eventInfo) === EventType.MOUSELEAVE || getEventType(eventInfo) === EventType.POINTERENTER || getEventType(eventInfo) === EventType.POINTERLEAVE) {\n        // We attempt to handle the mouseenter/mouseleave events here by\n        // detecting whether the mouseover/mouseout events correspond to\n        // entering/leaving an element.\n        if (isMouseSpecialEvent(getEvent(eventInfo), getEventType(eventInfo), getActionElement(action))) {\n          // If both mouseover/mouseout and mouseenter/mouseleave events are\n          // enabled, two separate handlers for mouseover/mouseout are\n          // registered. Both handlers will see the same event instance\n          // so we create a copy to avoid interfering with the dispatching of\n          // the mouseover/mouseout event.\n          const copiedEvent = createMouseSpecialEvent(getEvent(eventInfo), getActionElement(action));\n          setEvent(eventInfo, copiedEvent);\n          // Since the mouseenter/mouseleave events do not bubble, the target\n          // of the event is technically the `actionElement` (the node with the\n          // `jsaction` attribute)\n          setTargetElement(eventInfo, getActionElement(action));\n        } else {\n          unsetAction(eventInfo);\n        }\n      }\n    }\n  }\n  /**\n   * Walk to the parent node, unless the node has a different owner in\n   * which case we walk to the owner. Attempt to walk to host of a\n   * shadow root if needed.\n   */\n  getParentNode(element) {\n    const owner = element[Property.OWNER];\n    if (owner) {\n      return owner;\n    }\n    const parentNode = element.parentNode;\n    if (parentNode?.nodeName === '#document-fragment') {\n      return parentNode?.host ?? null;\n    }\n    return parentNode;\n  }\n  /**\n   * Accesses the jsaction map on a node and retrieves the name of the\n   * action the given event is mapped to, if any. It parses the\n   * attribute value and stores it in a property on the node for\n   * subsequent retrieval without re-parsing and re-accessing the\n   * attribute.\n   *\n   * @param actionElement The DOM node to retrieve the jsaction map from.\n   * @param eventInfo `EventInfo` to set `action` and `actionElement` if an\n   *    action is found on the `actionElement`.\n   */\n  populateActionOnElement(actionElement, eventInfo) {\n    const actionMap = this.parseActions(actionElement);\n    const actionName = actionMap[getEventType(eventInfo)];\n    if (actionName !== undefined) {\n      setAction(eventInfo, actionName, actionElement);\n    }\n    if (this.a11yClickSupport) {\n      this.populateClickOnlyAction(actionElement, eventInfo, actionMap);\n    }\n  }\n  /**\n   * Parses and caches an element's jsaction element into a map.\n   *\n   * This is primarily for internal use.\n   *\n   * @param actionElement The DOM node to retrieve the jsaction map from.\n   * @return Map from event to qualified name of the jsaction bound to it.\n   */\n  parseActions(actionElement) {\n    let actionMap = get(actionElement);\n    if (!actionMap) {\n      const jsactionAttribute = actionElement.getAttribute(Attribute.JSACTION);\n      if (!jsactionAttribute) {\n        actionMap = EMPTY_ACTION_MAP;\n        set(actionElement, actionMap);\n      } else {\n        actionMap = getParsed(jsactionAttribute);\n        if (!actionMap) {\n          actionMap = {};\n          const values = jsactionAttribute.split(REGEXP_SEMICOLON);\n          for (let idx = 0; idx < values.length; idx++) {\n            const value = values[idx];\n            if (!value) {\n              continue;\n            }\n            const colon = value.indexOf(Char.EVENT_ACTION_SEPARATOR);\n            const hasColon = colon !== -1;\n            const type = hasColon ? value.substr(0, colon).trim() : DEFAULT_EVENT_TYPE;\n            const action = hasColon ? value.substr(colon + 1).trim() : value;\n            actionMap[type] = action;\n          }\n          setParsed(jsactionAttribute, actionMap);\n        }\n        set(actionElement, actionMap);\n      }\n    }\n    return actionMap;\n  }\n  addA11yClickSupport(updateEventInfoForA11yClick, preventDefaultForA11yClick, populateClickOnlyAction) {\n    this.a11yClickSupport = true;\n    this.updateEventInfoForA11yClick = updateEventInfoForA11yClick;\n    this.preventDefaultForA11yClick = preventDefaultForA11yClick;\n    this.populateClickOnlyAction = populateClickOnlyAction;\n  }\n}\n\n/**\n * @fileoverview An enum to control who can call certain jsaction APIs.\n */\nvar Restriction;\n(function (Restriction) {\n  Restriction[Restriction[\"I_AM_THE_JSACTION_FRAMEWORK\"] = 0] = \"I_AM_THE_JSACTION_FRAMEWORK\";\n})(Restriction || (Restriction = {}));\n\n/**\n * Receives a DOM event, determines the jsaction associated with the source\n * element of the DOM event, and invokes the handler associated with the\n * jsaction.\n */\nclass Dispatcher {\n  dispatchDelegate;\n  // The ActionResolver to use to resolve actions.\n  actionResolver;\n  /** The replayer function to be called when there are queued events. */\n  eventReplayer;\n  /** Whether the event replay is scheduled. */\n  eventReplayScheduled = false;\n  /** The queue of events. */\n  replayEventInfoWrappers = [];\n  /**\n   * Options are:\n   *   - `eventReplayer`: When the event contract dispatches replay events\n   *      to the Dispatcher, the Dispatcher collects them and in the next tick\n   *      dispatches them to the `eventReplayer`. Defaults to dispatching to `dispatchDelegate`.\n   * @param dispatchDelegate A function that should handle dispatching an `EventInfoWrapper` to handlers.\n   */\n  constructor(dispatchDelegate, {\n    actionResolver,\n    eventReplayer\n  } = {}) {\n    this.dispatchDelegate = dispatchDelegate;\n    this.actionResolver = actionResolver;\n    this.eventReplayer = eventReplayer;\n  }\n  /**\n   * Receives an event or the event queue from the EventContract. The event\n   * queue is copied and it attempts to replay.\n   * If event info is passed in it looks for an action handler that can handle\n   * the given event.  If there is no handler registered queues the event and\n   * checks if a loader is registered for the given namespace. If so, calls it.\n   *\n   * Alternatively, if in global dispatch mode, calls all registered global\n   * handlers for the appropriate event type.\n   *\n   * The three functionalities of this call are deliberately not split into\n   * three methods (and then declared as an abstract interface), because the\n   * interface is used by EventContract, which lives in a different jsbinary.\n   * Therefore the interface between the three is defined entirely in terms that\n   * are invariant under jscompiler processing (Function and Array, as opposed\n   * to a custom type with method names).\n   *\n   * @param eventInfo The info for the event that triggered this call or the\n   *     queue of events from EventContract.\n   */\n  dispatch(eventInfo) {\n    const eventInfoWrapper = new EventInfoWrapper(eventInfo);\n    this.actionResolver?.resolveEventType(eventInfo);\n    this.actionResolver?.resolveAction(eventInfo);\n    const action = eventInfoWrapper.getAction();\n    if (action && shouldPreventDefaultBeforeDispatching(action.element, eventInfoWrapper)) {\n      preventDefault(eventInfoWrapper.getEvent());\n    }\n    if (this.eventReplayer && eventInfoWrapper.getIsReplay()) {\n      this.scheduleEventInfoWrapperReplay(eventInfoWrapper);\n      return;\n    }\n    this.dispatchDelegate(eventInfoWrapper);\n  }\n  /**\n   * Schedules an `EventInfoWrapper` for replay. The replaying will happen in its own\n   * stack once the current flow cedes control. This is done to mimic\n   * browser event handling.\n   */\n  scheduleEventInfoWrapperReplay(eventInfoWrapper) {\n    this.replayEventInfoWrappers.push(eventInfoWrapper);\n    if (this.eventReplayScheduled) {\n      return;\n    }\n    this.eventReplayScheduled = true;\n    Promise.resolve().then(() => {\n      this.eventReplayScheduled = false;\n      this.eventReplayer(this.replayEventInfoWrappers);\n    });\n  }\n}\n/**\n * Returns true if the default action of this event should be prevented before\n * this event is dispatched.\n */\nfunction shouldPreventDefaultBeforeDispatching(actionElement, eventInfoWrapper) {\n  // Prevent browser from following <a> node links if a jsaction is present\n  // and we are dispatching the action now. Note that the targetElement may be\n  // a child of an anchor that has a jsaction attached. For that reason, we\n  // need to check the actionElement rather than the targetElement.\n  return actionElement.tagName === 'A' && (eventInfoWrapper.getEventType() === EventType.CLICK || eventInfoWrapper.getEventType() === EventType.CLICKMOD);\n}\n\n/** An internal symbol used to indicate whether propagation should be stopped or not. */\nconst PROPAGATION_STOPPED_SYMBOL = /* @__PURE__ */Symbol.for('propagationStopped');\n/** Extra event phases beyond what the browser provides. */\nconst EventPhase = {\n  REPLAY: 101\n};\nconst PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS = ' Because event replay occurs after browser dispatch, `preventDefault` would have no ' + 'effect. You can check whether an event is being replayed by accessing the event phase: ' + '`event.eventPhase === EventPhase.REPLAY`.';\nconst PREVENT_DEFAULT_ERROR_MESSAGE = `\\`preventDefault\\` called during event replay.`;\nconst COMPOSED_PATH_ERROR_MESSAGE_DETAILS = ' Because event replay occurs after browser ' + 'dispatch, `composedPath()` will be empty. Iterate parent nodes from `event.target` or ' + '`event.currentTarget` if you need to check elements in the event path.';\nconst COMPOSED_PATH_ERROR_MESSAGE = `\\`composedPath\\` called during event replay.`;\n/**\n * A dispatcher that uses browser-based `Event` semantics, for example bubbling, `stopPropagation`,\n * `currentTarget`, etc.\n */\nclass EventDispatcher {\n  dispatchDelegate;\n  clickModSupport;\n  actionResolver;\n  dispatcher;\n  constructor(dispatchDelegate, clickModSupport = true) {\n    this.dispatchDelegate = dispatchDelegate;\n    this.clickModSupport = clickModSupport;\n    this.actionResolver = new ActionResolver({\n      clickModSupport\n    });\n    this.dispatcher = new Dispatcher(eventInfoWrapper => {\n      this.dispatchToDelegate(eventInfoWrapper);\n    }, {\n      actionResolver: this.actionResolver\n    });\n  }\n  /**\n   * The entrypoint for the `EventContract` dispatch.\n   */\n  dispatch(eventInfo) {\n    this.dispatcher.dispatch(eventInfo);\n  }\n  /** Internal method that does basic disaptching. */\n  dispatchToDelegate(eventInfoWrapper) {\n    if (eventInfoWrapper.getIsReplay()) {\n      prepareEventForReplay(eventInfoWrapper);\n    }\n    prepareEventForBubbling(eventInfoWrapper);\n    while (eventInfoWrapper.getAction()) {\n      prepareEventForDispatch(eventInfoWrapper);\n      // If this is a capture event, ONLY dispatch if the action element is the target.\n      if (isCaptureEventType(eventInfoWrapper.getEventType()) && eventInfoWrapper.getAction().element !== eventInfoWrapper.getTargetElement()) {\n        return;\n      }\n      this.dispatchDelegate(eventInfoWrapper.getEvent(), eventInfoWrapper.getAction().name);\n      if (propagationStopped(eventInfoWrapper)) {\n        return;\n      }\n      this.actionResolver.resolveParentAction(eventInfoWrapper.eventInfo);\n    }\n  }\n}\nfunction prepareEventForBubbling(eventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  const originalStopPropagation = eventInfoWrapper.getEvent().stopPropagation.bind(event);\n  const stopPropagation = () => {\n    event[PROPAGATION_STOPPED_SYMBOL] = true;\n    originalStopPropagation();\n  };\n  patchEventInstance(event, 'stopPropagation', stopPropagation);\n  patchEventInstance(event, 'stopImmediatePropagation', stopPropagation);\n}\nfunction propagationStopped(eventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  return !!event[PROPAGATION_STOPPED_SYMBOL];\n}\nfunction prepareEventForReplay(eventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  const target = eventInfoWrapper.getTargetElement();\n  const originalPreventDefault = event.preventDefault.bind(event);\n  patchEventInstance(event, 'target', target);\n  patchEventInstance(event, 'eventPhase', EventPhase.REPLAY);\n  patchEventInstance(event, 'preventDefault', () => {\n    originalPreventDefault();\n    throw new Error(PREVENT_DEFAULT_ERROR_MESSAGE + (ngDevMode ? PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS : ''));\n  });\n  patchEventInstance(event, 'composedPath', () => {\n    throw new Error(COMPOSED_PATH_ERROR_MESSAGE + (ngDevMode ? COMPOSED_PATH_ERROR_MESSAGE_DETAILS : ''));\n  });\n}\nfunction prepareEventForDispatch(eventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  const currentTarget = eventInfoWrapper.getAction()?.element;\n  if (currentTarget) {\n    patchEventInstance(event, 'currentTarget', currentTarget, {\n      // `currentTarget` is going to get reassigned every dispatch.\n      configurable: true\n    });\n  }\n}\n/**\n * Patch `Event` instance during non-standard `Event` dispatch. This patches just the `Event`\n * instance that the browser created, it does not patch global properties or methods.\n *\n * This is necessary because dispatching an `Event` outside of browser dispatch results in\n * incorrect properties and methods that need to be polyfilled or do not work.\n *\n * JSAction dispatch adds two extra \"phases\" to event dispatch:\n * 1. Event delegation - the event is being dispatched by a delegating event handler on a container\n *    (typically `window.document.documentElement`), to a delegated event handler on some child\n *    element. Certain `Event` properties will be unintuitive, such as `currentTarget`, which would\n *    be the container rather than the child element. Bubbling would also not work. In order to\n *    emulate the browser, these properties and methods on the `Event` are patched.\n * 2. Event replay - the event is being dispatched by the framework once the handlers have been\n *    loaded (during hydration, or late-loaded). Certain `Event` properties can be unset by the\n *    browser because the `Event` is no longer actively being dispatched, such as `target`. Other\n *    methods have no effect because the `Event` has already been dispatched, such as\n *    `preventDefault`. Bubbling would also not work. These properties and methods are patched,\n *    either to fill in information that the browser may have removed, or to throw errors in methods\n *    that no longer behave as expected.\n */\nfunction patchEventInstance(event, property, value, {\n  configurable = false\n} = {}) {\n  Object.defineProperty(event, property, {\n    value,\n    configurable\n  });\n}\n/**\n * Registers deferred functionality for an EventContract and a Jsaction\n * Dispatcher.\n */\nfunction registerDispatcher$1(eventContract, dispatcher) {\n  eventContract.ecrd(eventInfo => {\n    dispatcher.dispatch(eventInfo);\n  }, Restriction.I_AM_THE_JSACTION_FRAMEWORK);\n}\n\n/** Creates an `EarlyJsactionData` object. */\nfunction createEarlyJsactionData(container) {\n  const q = [];\n  const d = eventInfo => {\n    q.push(eventInfo);\n  };\n  const h = event => {\n    d(createEventInfoFromParameters(event.type, event, event.target, container, Date.now()));\n  };\n  return {\n    c: container,\n    q,\n    et: [],\n    etc: [],\n    d,\n    h\n  };\n}\n/** Add all the events to the container stored in the `EarlyJsactionData`. */\nfunction addEvents(earlyJsactionData, types, capture) {\n  for (let i = 0; i < types.length; i++) {\n    const eventType = types[i];\n    const eventTypes = capture ? earlyJsactionData.etc : earlyJsactionData.et;\n    eventTypes.push(eventType);\n    earlyJsactionData.c.addEventListener(eventType, earlyJsactionData.h, capture);\n  }\n}\n/** Get the queued `EventInfo` objects that were dispatched before a dispatcher was registered. */\nfunction getQueuedEventInfos(earlyJsactionData) {\n  return earlyJsactionData?.q ?? [];\n}\n/** Register a different dispatcher function on the `EarlyJsactionData`. */\nfunction registerDispatcher(earlyJsactionData, dispatcher) {\n  if (!earlyJsactionData) {\n    return;\n  }\n  earlyJsactionData.d = dispatcher;\n}\n/** Removes all event listener handlers. */\nfunction removeAllEventListeners(earlyJsactionData) {\n  if (!earlyJsactionData) {\n    return;\n  }\n  removeEventListeners(earlyJsactionData.c, earlyJsactionData.et, earlyJsactionData.h);\n  removeEventListeners(earlyJsactionData.c, earlyJsactionData.etc, earlyJsactionData.h, true);\n}\nfunction removeEventListeners(container, eventTypes, earlyEventHandler, capture) {\n  for (let i = 0; i < eventTypes.length; i++) {\n    container.removeEventListener(eventTypes[i], earlyEventHandler, /* useCapture */capture);\n  }\n}\n\n/**\n * @define Support for the non-bubbling mouseenter and mouseleave events.  This\n * flag can be overridden in a build rule.\n */\nconst MOUSE_SPECIAL_SUPPORT = false;\n\n/**\n * @fileoverview Implements the local event handling contract. This\n * allows DOM objects in a container that enters into this contract to\n * define event handlers which are executed in a local context.\n *\n * One EventContract instance can manage the contract for multiple\n * containers, which are added using the addContainer() method.\n *\n * Events can be registered using the addEvent() method.\n *\n * A Dispatcher is added using the registerDispatcher() method. Until there is\n * a dispatcher, events are queued. The idea is that the EventContract\n * class is inlined in the HTML of the top level page and instantiated\n * right after the start of <body>. The Dispatcher class is contained\n * in the external deferred js, and instantiated and registered with\n * EventContract when the external javascript in the page loads. The\n * external javascript will also register the jsaction handlers, which\n * then pick up the queued events at the time of registration.\n *\n * Since this class is meant to be inlined in the main page HTML, the\n * size of the binary compiled from this file MUST be kept as small as\n * possible and thus its dependencies to a minimum.\n */\n/**\n * EventContract intercepts events in the bubbling phase at the\n * boundary of a container element, and maps them to generic actions\n * which are specified using the custom jsaction attribute in\n * HTML. Behavior of the application is then specified in terms of\n * handler for such actions, cf. jsaction.Dispatcher in dispatcher.js.\n *\n * This has several benefits: (1) No DOM event handlers need to be\n * registered on the specific elements in the UI. (2) The set of\n * events that the application has to handle can be specified in terms\n * of the semantics of the application, rather than in terms of DOM\n * events. (3) Invocation of handlers can be delayed and handlers can\n * be delay loaded in a generic way.\n */\nclass EventContract {\n  static MOUSE_SPECIAL_SUPPORT = MOUSE_SPECIAL_SUPPORT;\n  containerManager;\n  /**\n   * The DOM events which this contract covers. Used to prevent double\n   * registration of event types. The value of the map is the\n   * internally created DOM event handler function that handles the\n   * DOM events. See addEvent().\n   *\n   */\n  eventHandlers = {};\n  browserEventTypeToExtraEventTypes = {};\n  /**\n   * The dispatcher function. Events are passed to this function for\n   * handling once it was set using the registerDispatcher() method. This is\n   * done because the function is passed from another jsbinary, so passing the\n   * instance and invoking the method here would require to leave the method\n   * unobfuscated.\n   */\n  dispatcher = null;\n  /**\n   * The list of suspended `EventInfo` that will be dispatched\n   * as soon as the `Dispatcher` is registered.\n   */\n  queuedEventInfos = [];\n  constructor(containerManager) {\n    this.containerManager = containerManager;\n  }\n  handleEvent(eventType, event, container) {\n    const eventInfo = createEventInfoFromParameters(/* eventType= */eventType, /* event= */event, /* targetElement= */event.target, /* container= */container, /* timestamp= */Date.now());\n    this.handleEventInfo(eventInfo);\n  }\n  /**\n   * Handle an `EventInfo`.\n   */\n  handleEventInfo(eventInfo) {\n    if (!this.dispatcher) {\n      // All events are queued when the dispatcher isn't yet loaded.\n      setIsReplay(eventInfo, true);\n      this.queuedEventInfos?.push(eventInfo);\n      return;\n    }\n    this.dispatcher(eventInfo);\n  }\n  /**\n   * Enables jsaction handlers to be called for the event type given by\n   * name.\n   *\n   * If the event is already registered, this does nothing.\n   *\n   * @param prefixedEventType If supplied, this event is used in\n   *     the actual browser event registration instead of the name that is\n   *     exposed to jsaction. Use this if you e.g. want users to be able\n   *     to subscribe to jsaction=\"transitionEnd:foo\" while the underlying\n   *     event is webkitTransitionEnd in one browser and mozTransitionEnd\n   *     in another.\n   *\n   * @param passive A boolean value that, if `true`, indicates that the event\n   *     handler will never call `preventDefault()`.\n   */\n  addEvent(eventType, prefixedEventType, passive) {\n    if (eventType in this.eventHandlers || !this.containerManager) {\n      return;\n    }\n    if (!EventContract.MOUSE_SPECIAL_SUPPORT && MOUSE_SPECIAL_EVENT_TYPES.indexOf(eventType) >= 0) {\n      return;\n    }\n    const eventHandler = (eventType, event, container) => {\n      this.handleEvent(eventType, event, container);\n    };\n    // Store the callback to allow us to replay events.\n    this.eventHandlers[eventType] = eventHandler;\n    const browserEventType = getBrowserEventType(prefixedEventType || eventType);\n    if (browserEventType !== eventType) {\n      const eventTypes = this.browserEventTypeToExtraEventTypes[browserEventType] || [];\n      eventTypes.push(eventType);\n      this.browserEventTypeToExtraEventTypes[browserEventType] = eventTypes;\n    }\n    this.containerManager.addEventListener(browserEventType, element => {\n      return event => {\n        eventHandler(eventType, event, element);\n      };\n    }, passive);\n  }\n  /**\n   * Gets the queued early events and replay them using the appropriate handler\n   * in the provided event contract. Once all the events are replayed, it cleans\n   * up the early contract.\n   */\n  replayEarlyEvents(earlyJsactionData = window._ejsa) {\n    // Check if the early contract is present and prevent calling this function\n    // more than once.\n    if (!earlyJsactionData) {\n      return;\n    }\n    // Replay the early contract events.\n    this.replayEarlyEventInfos(earlyJsactionData.q);\n    // Clean up the early contract.\n    removeAllEventListeners(earlyJsactionData);\n    delete window._ejsa;\n  }\n  /**\n   * Replays all the early `EventInfo` objects, dispatching them through the normal\n   * `EventContract` flow.\n   */\n  replayEarlyEventInfos(earlyEventInfos) {\n    for (let i = 0; i < earlyEventInfos.length; i++) {\n      const earlyEventInfo = earlyEventInfos[i];\n      const eventTypes = this.getEventTypesForBrowserEventType(earlyEventInfo.eventType);\n      for (let j = 0; j < eventTypes.length; j++) {\n        const eventInfo = cloneEventInfo(earlyEventInfo);\n        // EventInfo eventType maps to JSAction's internal event type,\n        // rather than the browser event type.\n        setEventType(eventInfo, eventTypes[j]);\n        this.handleEventInfo(eventInfo);\n      }\n    }\n  }\n  /**\n   * Returns all JSAction event types that have been registered for a given\n   * browser event type.\n   */\n  getEventTypesForBrowserEventType(browserEventType) {\n    const eventTypes = [];\n    if (this.eventHandlers[browserEventType]) {\n      eventTypes.push(browserEventType);\n    }\n    if (this.browserEventTypeToExtraEventTypes[browserEventType]) {\n      eventTypes.push(...this.browserEventTypeToExtraEventTypes[browserEventType]);\n    }\n    return eventTypes;\n  }\n  /**\n   * Returns the event handler function for a given event type.\n   */\n  handler(eventType) {\n    return this.eventHandlers[eventType];\n  }\n  /**\n   * Cleans up the event contract. This resets all of the `EventContract`'s\n   * internal state. Users are responsible for not using this `EventContract`\n   * after it has been cleaned up.\n   */\n  cleanUp() {\n    this.containerManager?.cleanUp();\n    this.containerManager = null;\n    this.eventHandlers = {};\n    this.browserEventTypeToExtraEventTypes = {};\n    this.dispatcher = null;\n    this.queuedEventInfos = [];\n  }\n  /**\n   * Register a dispatcher function. Event info of each event mapped to\n   * a jsaction is passed for handling to this callback. The queued\n   * events are passed as well to the dispatcher for later replaying\n   * once the dispatcher is registered. Clears the event queue to null.\n   *\n   * @param dispatcher The dispatcher function.\n   * @param restriction\n   */\n  registerDispatcher(dispatcher, restriction) {\n    this.ecrd(dispatcher, restriction);\n  }\n  /**\n   * Unrenamed alias for registerDispatcher. Necessary for any codebases that\n   * split the `EventContract` and `Dispatcher` code into different compilation\n   * units.\n   */\n  ecrd(dispatcher, restriction) {\n    this.dispatcher = dispatcher;\n    if (this.queuedEventInfos?.length) {\n      for (let i = 0; i < this.queuedEventInfos.length; i++) {\n        this.handleEventInfo(this.queuedEventInfos[i]);\n      }\n      this.queuedEventInfos = null;\n    }\n  }\n}\n\n/**\n * Creates an `EarlyJsactionData`, adds events to it, and populates it on a nested object on\n * the window.\n */\nfunction bootstrapAppScopedEarlyEventContract(container, appId, bubbleEventTypes, captureEventTypes, dataContainer = window) {\n  const earlyJsactionData = createEarlyJsactionData(container);\n  if (!dataContainer._ejsas) {\n    dataContainer._ejsas = {};\n  }\n  dataContainer._ejsas[appId] = earlyJsactionData;\n  addEvents(earlyJsactionData, bubbleEventTypes);\n  addEvents(earlyJsactionData, captureEventTypes, /* capture= */true);\n}\n/** Get the queued `EventInfo` objects that were dispatched before a dispatcher was registered. */\nfunction getAppScopedQueuedEventInfos(appId, dataContainer = window) {\n  return getQueuedEventInfos(dataContainer._ejsas?.[appId]);\n}\n/**\n * Registers a dispatcher function on the `EarlyJsactionData` present on the nested object on the\n * window.\n */\nfunction registerAppScopedDispatcher(restriction, appId, dispatcher, dataContainer = window) {\n  registerDispatcher(dataContainer._ejsas?.[appId], dispatcher);\n}\n/** Removes all event listener handlers. */\nfunction removeAllAppScopedEventListeners(appId, dataContainer = window) {\n  removeAllEventListeners(dataContainer._ejsas?.[appId]);\n}\n/** Clear the early event contract. */\nfunction clearAppScopedEarlyEventContract(appId, dataContainer = window) {\n  if (!dataContainer._ejsas) {\n    return;\n  }\n  dataContainer._ejsas[appId] = undefined;\n}\nexport { Attribute, EventContract, EventContractContainer, EventDispatcher, EventInfoWrapper, EventPhase, bootstrapAppScopedEarlyEventContract, clearAppScopedEarlyEventContract, getDefaulted as getActionCache, getAppScopedQueuedEventInfos, isCaptureEventType, isEarlyEventType, registerAppScopedDispatcher, registerDispatcher$1 as registerDispatcher, removeAllAppScopedEventListeners };", "map": {"version": 3, "names": ["Attribute", "Property", "JSACTION", "OWNER", "parseCache", "get", "element", "getDefaulted", "cache", "set", "actionMap", "getParsed", "text", "setParsed", "parsed", "EventType", "CLICK", "CLICKMOD", "DBLCLICK", "FOCUS", "FOCUSIN", "BLUR", "FOCUSOUT", "SUBMIT", "KEYDOWN", "KEYPRESS", "KEYUP", "MOUSEOVER", "MOUSEOUT", "MOUSEENTER", "MOUSELEAVE", "POINTEROVER", "POINTEROUT", "POINTERENTER", "POINTERLEAVE", "ERROR", "LOAD", "TOUCHSTART", "TOUCHEND", "TOUCHMOVE", "TOGGLE", "MOUSE_SPECIAL_EVENT_TYPES", "BUBBLE_EVENT_TYPES", "CAPTURE_EVENT_TYPES", "isCaptureEventType", "eventType", "indexOf", "EARLY_EVENT_TYPES", "concat", "isEarlyEventType", "getBrowserEventType", "addEventListener", "handler", "passive", "capture", "options", "removeEventListener", "info", "detachEvent", "preventDefault", "e", "returnValue", "isMac", "navigator", "test", "userAgent", "isMiddleClick", "which", "button", "isModifiedClickEvent", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "isMouseSpecialEvent", "type", "related", "relatedTarget", "contains", "createMouseSpecialEvent", "target", "copy", "property", "key", "value", "isIos", "EventContractContainer", "handlerInfos", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "style", "cursor", "push", "cleanUp", "i", "length", "Char", "EVENT_ACTION_SEPARATOR", "getEventType", "eventInfo", "setEventType", "getEvent", "event", "setEvent", "getTargetElement", "targetElement", "setTargetElement", "getContainer", "eic", "<PERSON><PERSON><PERSON><PERSON>", "container", "getTimestamp", "timeStamp", "setTimestamp", "timestamp", "getAction", "eia", "setAction", "actionName", "actionElement", "unsetAction", "undefined", "getActionElement", "actionInfo", "getIsReplay", "eirp", "setIsReplay", "replay", "getResolved", "eir", "setResolved", "resolved", "cloneEventInfo", "eiack", "createEventInfoFromParameters", "action", "isReplay", "a11yClickKey", "EventInfoWrapper", "name", "clone", "EMPTY_ACTION_MAP", "REGEXP_SEMICOLON", "DEFAULT_EVENT_TYPE", "ActionResolver", "a11yClickSupport", "clickModSupport", "syntheticMouseEventSupport", "updateEventInfoForA11yClick", "preventDefaultForA11yClick", "populateClickOnlyAction", "resolveEventType", "resolveAction", "populateAction", "resolveParentAction", "parentNode", "getParentNode", "currentTarget", "nodeType", "Node", "ELEMENT_NODE", "populateActionOnElement", "copiedEvent", "owner", "nodeName", "host", "parseActions", "jsactionAttribute", "getAttribute", "values", "split", "idx", "colon", "hasColon", "substr", "trim", "addA11yClickSupport", "Restriction", "Di<PERSON>atcher", "dispatchDelegate", "actionResolver", "eventReplayer", "eventReplayScheduled", "replayEventInfoWrappers", "dispatch", "eventInfoWrapper", "shouldPreventDefaultBeforeDispatching", "scheduleEventInfoWrapperReplay", "Promise", "resolve", "then", "tagName", "PROPAGATION_STOPPED_SYMBOL", "Symbol", "for", "EventPhase", "REPLAY", "PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS", "PREVENT_DEFAULT_ERROR_MESSAGE", "COMPOSED_PATH_ERROR_MESSAGE_DETAILS", "COMPOSED_PATH_ERROR_MESSAGE", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatcher", "dispatchToDelegate", "prepareEventForReplay", "prepareEventForBubbling", "prepareEventForDispatch", "propagationStopped", "originalStopPropagation", "stopPropagation", "bind", "patchEventInstance", "originalPreventDefault", "Error", "ngDevMode", "configurable", "Object", "defineProperty", "registerDispatcher$1", "eventContract", "ecrd", "I_AM_THE_JSACTION_FRAMEWORK", "createEarlyJsactionData", "q", "d", "h", "Date", "now", "c", "et", "etc", "addEvents", "earlyJsactionData", "types", "eventTypes", "getQueuedEventInfos", "registerDispatcher", "removeAllEventListeners", "removeEventListeners", "earlyEventHandler", "MOUSE_SPECIAL_SUPPORT", "EventContract", "containerManager", "eventHandlers", "browserEventTypeToExtraEventTypes", "queuedEventInfos", "handleEvent", "handleEventInfo", "addEvent", "prefixedEventType", "<PERSON><PERSON><PERSON><PERSON>", "browserEventType", "replay<PERSON>ar<PERSON>E<PERSON>s", "window", "_ejsa", "replayEarlyEventInfos", "earlyEventInfos", "earlyEventInfo", "getEventTypesForBrowserEventType", "j", "restriction", "bootstrapAppScopedEarlyEventContract", "appId", "bubbleEventTypes", "captureEventTypes", "dataContainer", "_ejsas", "getAppScopedQueuedEventInfos", "registerAppScopedDispatcher", "removeAllAppScopedEventListeners", "clearAppScopedEarlyEventContract", "getActionCache"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/core/fesm2022/primitives/event-dispatch.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Attribute } from '../attribute-BWp59EjE.mjs';\n\n/** All properties that are used by jsaction. */\nconst Property = {\n    /**\n     * The parsed value of the jsaction attribute is stored in this\n     * property on the DOM node. The parsed value is an Object. The\n     * property names of the object are the events; the values are the\n     * names of the actions. This property is attached even on nodes\n     * that don't have a jsaction attribute as an optimization, because\n     * property lookup is faster than attribute access.\n     */\n    JSACTION: '__jsaction',\n    /**\n     * The owner property references an a logical owner for a DOM node. JSAction\n     * will follow this reference instead of parentNode when traversing the DOM\n     * to find jsaction attributes. This allows overlaying a logical structure\n     * over a document where the DOM structure can't reflect that structure.\n     */\n    OWNER: '__owner',\n};\n\n/**\n * Map from jsaction annotation to a parsed map from event name to action name.\n */\nconst parseCache = {};\n/**\n * Reads the jsaction parser cache from the given DOM Element.\n */\nfunction get(element) {\n    return element[Property.JSACTION];\n}\n/**\n * Reads the jsaction parser cache for the given DOM element. If no cache is yet present,\n * creates an empty one.\n */\nfunction getDefaulted(element) {\n    const cache = get(element) ?? {};\n    set(element, cache);\n    return cache;\n}\n/**\n * Writes the jsaction parser cache to the given DOM Element.\n */\nfunction set(element, actionMap) {\n    element[Property.JSACTION] = actionMap;\n}\n/**\n * Looks up the parsed action map from the source jsaction attribute value.\n *\n * @param text Unparsed jsaction attribute value.\n * @return Parsed jsaction attribute value, if already present in the cache.\n */\nfunction getParsed(text) {\n    return parseCache[text];\n}\n/**\n * Inserts the parse result for the given source jsaction value into the cache.\n *\n * @param text Unparsed jsaction attribute value.\n * @param parsed Attribute value parsed into the action map.\n */\nfunction setParsed(text, parsed) {\n    parseCache[text] = parsed;\n}\n\n/*\n * Names of events that are special to jsaction. These are not all\n * event types that are legal to use in either HTML or the addEvent()\n * API, but these are the ones that are treated specially. All other\n * DOM events can be used in either addEvent() or in the value of the\n * jsaction attribute. Beware of browser specific events or events\n * that don't bubble though: If they are not mentioned here, then\n * event contract doesn't work around their peculiarities.\n */\nconst EventType = {\n    /**\n     * The click event. In addEvent() refers to all click events, in the\n     * jsaction attribute it refers to the unmodified click and Enter/Space\n     * keypress events.  In the latter case, a jsaction click will be triggered,\n     * for accessibility reasons.  See clickmod and clickonly, below.\n     */\n    CLICK: 'click',\n    /**\n     * Specifies the jsaction for a modified click event (i.e. a mouse\n     * click with the modifier key Cmd/Ctrl pressed). This event isn't\n     * separately enabled in addEvent(), because in the DOM, it's just a\n     * click event.\n     */\n    CLICKMOD: 'clickmod',\n    /**\n     * The dblclick event.\n     */\n    DBLCLICK: 'dblclick',\n    /**\n     * Focus doesn't bubble, but you can use it in addEvent() and\n     * jsaction anyway. EventContract does the right thing under the\n     * hood.\n     */\n    FOCUS: 'focus',\n    /**\n     * This event only exists in IE. For addEvent() and jsaction, use\n     * focus instead; EventContract does the right thing even though\n     * focus doesn't bubble.\n     */\n    FOCUSIN: 'focusin',\n    /**\n     * Analog to focus.\n     */\n    BLUR: 'blur',\n    /**\n     * Analog to focusin.\n     */\n    FOCUSOUT: 'focusout',\n    /**\n     * Submit doesn't bubble, so it cannot be used with event\n     * contract. However, the browser helpfully fires a click event on\n     * the submit button of a form (even if the form is not submitted by\n     * a click on the submit button). So you should handle click on the\n     * submit button instead.\n     */\n    SUBMIT: 'submit',\n    /**\n     * The keydown event. In addEvent() and non-click jsaction it represents the\n     * regular DOM keydown event. It represents click actions in non-Gecko\n     * browsers.\n     */\n    KEYDOWN: 'keydown',\n    /**\n     * The keypress event. In addEvent() and non-click jsaction it represents the\n     * regular DOM keypress event. It represents click actions in Gecko browsers.\n     */\n    KEYPRESS: 'keypress',\n    /**\n     * The keyup event. In addEvent() and non-click jsaction it represents the\n     * regular DOM keyup event. It represents click actions in non-Gecko\n     * browsers.\n     */\n    KEYUP: 'keyup',\n    /**\n     * The mouseover event. Can either be used directly or used implicitly to\n     * capture mouseenter events. In addEvent(), it represents a regular DOM\n     * mouseover event.\n     */\n    MOUSEOVER: 'mouseover',\n    /**\n     * The mouseout event. Can either be used directly or used implicitly to\n     * capture mouseover events. In addEvent(), it represents a regular DOM\n     * mouseout event.\n     */\n    MOUSEOUT: 'mouseout',\n    /**\n     * The mouseenter event. Does not bubble and fires individually on each\n     * element being entered within a DOM tree.\n     */\n    MOUSEENTER: 'mouseenter',\n    /**\n     * The mouseleave event. Does not bubble and fires individually on each\n     * element being entered within a DOM tree.\n     */\n    MOUSELEAVE: 'mouseleave',\n    /**\n     * The pointerover event. Can either be used directly or used implicitly to\n     * capture pointerenter events. In addEvent(), it represents a regular DOM\n     * pointerover event.\n     */\n    POINTEROVER: 'pointerover',\n    /**\n     * The pointerout event. Can either be used directly or used implicitly to\n     * capture pointerover events. In addEvent(), it represents a regular DOM\n     * pointerout event.\n     */\n    POINTEROUT: 'pointerout',\n    /**\n     * The pointerenter event. Does not bubble and fires individually on each\n     * element being entered within a DOM tree.\n     */\n    POINTERENTER: 'pointerenter',\n    /**\n     * The pointerleave event. Does not bubble and fires individually on each\n     * element being entered within a DOM tree.\n     */\n    POINTERLEAVE: 'pointerleave',\n    /**\n     * The error event. The error event doesn't bubble, but you can use it in\n     * addEvent() and jsaction anyway. EventContract does the right thing under\n     * the hood (except in IE8 which does not use error events).\n     */\n    ERROR: 'error',\n    /**\n     * The load event. The load event doesn't bubble, but you can use it in\n     * addEvent() and jsaction anyway. EventContract does the right thing\n     * under the hood.\n     */\n    LOAD: 'load',\n    /**\n     * The touchstart event. Bubbles, will only ever fire in browsers with\n     * touch support.\n     */\n    TOUCHSTART: 'touchstart',\n    /**\n     * The touchend event. Bubbles, will only ever fire in browsers with\n     * touch support.\n     */\n    TOUCHEND: 'touchend',\n    /**\n     * The touchmove event. Bubbles, will only ever fire in browsers with\n     * touch support.\n     */\n    TOUCHMOVE: 'touchmove',\n    /**\n     * The toggle event. The toggle event doesn't bubble, but you can use it in\n     * addEvent() and jsaction anyway. EventContract does the right thing\n     * under the hood.\n     */\n    TOGGLE: 'toggle'};\n/** All event types that do not bubble or capture and need a polyfill. */\nconst MOUSE_SPECIAL_EVENT_TYPES = [\n    EventType.MOUSEENTER,\n    EventType.MOUSELEAVE,\n    'pointerenter',\n    'pointerleave',\n];\n/** All event types that are registered in the bubble phase. */\nconst BUBBLE_EVENT_TYPES = [\n    EventType.CLICK,\n    EventType.DBLCLICK,\n    EventType.FOCUSIN,\n    EventType.FOCUSOUT,\n    EventType.KEYDOWN,\n    EventType.KEYUP,\n    EventType.KEYPRESS,\n    EventType.MOUSEOVER,\n    EventType.MOUSEOUT,\n    EventType.SUBMIT,\n    EventType.TOUCHSTART,\n    EventType.TOUCHEND,\n    EventType.TOUCHMOVE,\n    'touchcancel',\n    'auxclick',\n    'change',\n    'compositionstart',\n    'compositionupdate',\n    'compositionend',\n    'beforeinput',\n    'input',\n    'select',\n    'copy',\n    'cut',\n    'paste',\n    'mousedown',\n    'mouseup',\n    'wheel',\n    'contextmenu',\n    'dragover',\n    'dragenter',\n    'dragleave',\n    'drop',\n    'dragstart',\n    'dragend',\n    'pointerdown',\n    'pointermove',\n    'pointerup',\n    'pointercancel',\n    'pointerover',\n    'pointerout',\n    'gotpointercapture',\n    'lostpointercapture',\n    // Video events.\n    'ended',\n    'loadedmetadata',\n    // Page visibility events.\n    'pagehide',\n    'pageshow',\n    'visibilitychange',\n    // Content visibility events.\n    'beforematch',\n];\n/** All event types that are registered in the capture phase. */\nconst CAPTURE_EVENT_TYPES = [\n    EventType.FOCUS,\n    EventType.BLUR,\n    EventType.ERROR,\n    EventType.LOAD,\n    EventType.TOGGLE,\n];\n/**\n * Whether or not an event type should be registered in the capture phase.\n * @param eventType\n * @returns bool\n */\nconst isCaptureEventType = (eventType) => CAPTURE_EVENT_TYPES.indexOf(eventType) >= 0;\n/** All event types that are registered early.  */\nconst EARLY_EVENT_TYPES = BUBBLE_EVENT_TYPES.concat(CAPTURE_EVENT_TYPES);\n/**\n * Whether or not an event type is registered in the early contract.\n */\nconst isEarlyEventType = (eventType) => EARLY_EVENT_TYPES.indexOf(eventType) >= 0;\n\n/**\n * Gets a browser event type, if it would differ from the JSAction event type.\n */\nfunction getBrowserEventType(eventType) {\n    // Mouseenter and mouseleave events are not handled directly because they\n    // are not available everywhere. In browsers where they are available, they\n    // don't bubble and aren't visible at the container boundary. Instead, we\n    // synthesize the mouseenter and mouseleave events from mouseover and\n    // mouseout events, respectively. Cf. eventcontract.js.\n    if (eventType === EventType.MOUSEENTER) {\n        return EventType.MOUSEOVER;\n    }\n    else if (eventType === EventType.MOUSELEAVE) {\n        return EventType.MOUSEOUT;\n    }\n    else if (eventType === EventType.POINTERENTER) {\n        return EventType.POINTEROVER;\n    }\n    else if (eventType === EventType.POINTERLEAVE) {\n        return EventType.POINTEROUT;\n    }\n    return eventType;\n}\n/**\n * Registers the event handler function with the given DOM element for\n * the given event type.\n *\n * @param element The element.\n * @param eventType The event type.\n * @param handler The handler function to install.\n * @param passive A boolean value that, if `true`, indicates that the function\n *     specified by `handler` will never call `preventDefault()`.\n * @return Information needed to uninstall the event handler eventually.\n */\nfunction addEventListener(element, eventType, handler, passive) {\n    // All event handlers are registered in the bubbling\n    // phase.\n    //\n    // All browsers support focus and blur, but these events only are propagated\n    // in the capture phase. Very legacy browsers do not support focusin or\n    // focusout.\n    //\n    // It would be a bad idea to register all event handlers in the\n    // capture phase because then regular onclick handlers would not be\n    // executed at all on events that trigger a jsaction. That's not\n    // entirely what we want, at least for now.\n    //\n    // Error and load events (i.e. on images) do not bubble so they are also\n    // handled in the capture phase.\n    let capture = false;\n    if (isCaptureEventType(eventType)) {\n        capture = true;\n    }\n    const options = typeof passive === 'boolean' ? { capture, passive } : capture;\n    element.addEventListener(eventType, handler, options);\n    return { eventType, handler, capture, passive };\n}\n/**\n * Removes the event handler for the given event from the element.\n * the given event type.\n *\n * @param element The element.\n * @param info The information needed to deregister the handler, as returned by\n *     addEventListener(), above.\n */\nfunction removeEventListener(element, info) {\n    if (element.removeEventListener) {\n        // It's worth noting that some browser releases have been inconsistent on this, and unless\n        // you have specific reasons otherwise, it's probably wise to use the same values used for\n        // the call to addEventListener() when calling removeEventListener().\n        const options = typeof info.passive === 'boolean' ? { capture: info.capture } : info.capture;\n        element.removeEventListener(info.eventType, info.handler, options);\n        // `detachEvent` is an old DOM API.\n    }\n    else if (element.detachEvent) {\n        // `detachEvent` is an old DOM API.\n        element.detachEvent(`on${info.eventType}`, info.handler);\n    }\n}\n/**\n * Prevents the default action of an event.\n * @param e The event to prevent the default action for.\n */\nfunction preventDefault(e) {\n    e.preventDefault ? e.preventDefault() : (e.returnValue = false);\n}\n/**\n * Whether we are on a Mac. Not pulling in useragent just for this.\n */\nlet isMac = typeof navigator !== 'undefined' && /Macintosh/.test(navigator.userAgent);\n/**\n * Determines and returns whether the given event (which is assumed to be a\n * click event) is a middle click.\n * NOTE: There is not a consistent way to identify middle click\n * http://www.unixpapa.com/js/mouse.html\n */\nfunction isMiddleClick(e) {\n    return (\n    // `which` is an old DOM API.\n    e.which === 2 ||\n        // `which` is an old DOM API.\n        (e.which == null &&\n            // `button` is an old DOM API.\n            e.button === 4) // middle click for IE\n    );\n}\n/**\n * Determines and returns whether the given event (which is assumed\n * to be a click event) is modified. A middle click is considered a modified\n * click to retain the default browser action, which opens a link in a new tab.\n * @param e The event.\n * @return Whether the given event is modified.\n */\nfunction isModifiedClickEvent(e) {\n    return (\n    // `metaKey` is an old DOM API.\n    (isMac && e.metaKey) ||\n        // `ctrlKey` is an old DOM API.\n        (!isMac && e.ctrlKey) ||\n        isMiddleClick(e) ||\n        // `shiftKey` is an old DOM API.\n        e.shiftKey);\n}\n/**\n * Determines whether the event corresponds to a non-bubbling mouse\n * event type (mouseenter, mouseleave, pointerenter, and pointerleave).\n *\n * During mouseover (mouseenter) and pointerover (pointerenter), the\n * relatedTarget is the element being entered from. During mouseout (mouseleave)\n * and pointerout (pointerleave), the relatedTarget is the element being exited\n * to.\n *\n * In both cases, if relatedTarget is outside target, then the corresponding\n * special event has occurred, otherwise it hasn't.\n *\n * @param e The mouseover/mouseout event.\n * @param type The type of the mouse special event.\n * @param element The element on which the jsaction for the\n *     mouseenter/mouseleave event is defined.\n * @return True if the event is a mouseenter/mouseleave event.\n */\nfunction isMouseSpecialEvent(e, type, element) {\n    // `relatedTarget` is an old DOM API.\n    const related = e.relatedTarget;\n    return (((e.type === EventType.MOUSEOVER && type === EventType.MOUSEENTER) ||\n        (e.type === EventType.MOUSEOUT && type === EventType.MOUSELEAVE) ||\n        (e.type === EventType.POINTEROVER && type === EventType.POINTERENTER) ||\n        (e.type === EventType.POINTEROUT && type === EventType.POINTERLEAVE)) &&\n        (!related || (related !== element && !element.contains(related))));\n}\n/**\n * Creates a new EventLike object for a mouseenter/mouseleave event that's\n * derived from the original corresponding mouseover/mouseout event.\n * @param e The event.\n * @param target The element on which the jsaction for the mouseenter/mouseleave\n *     event is defined.\n * @return A modified event-like object copied from the event object passed into\n *     this function.\n */\nfunction createMouseSpecialEvent(e, target) {\n    // We have to create a copy of the event object because we need to mutate\n    // its fields. We do this for the special mouse events because the event\n    // target needs to be retargeted to the action element rather than the real\n    // element (since we are simulating the special mouse events with mouseover/\n    // mouseout).\n    //\n    // Since we're making a copy anyways, we might as well attempt to convert\n    // this event into a pseudo-real mouseenter/mouseleave event by adjusting\n    // its type.\n    //\n    const copy = {};\n    for (const property in e) {\n        if (property === 'srcElement' || property === 'target') {\n            continue;\n        }\n        const key = property;\n        // Making a copy requires iterating through all properties of `Event`.\n        const value = e[key];\n        if (typeof value === 'function') {\n            continue;\n        }\n        // Value should be the expected type, but the value of `key` is not known\n        // statically.\n        copy[key] = value;\n    }\n    if (e.type === EventType.MOUSEOVER) {\n        copy['type'] = EventType.MOUSEENTER;\n    }\n    else if (e.type === EventType.MOUSEOUT) {\n        copy['type'] = EventType.MOUSELEAVE;\n    }\n    else if (e.type === EventType.POINTEROVER) {\n        copy['type'] = EventType.POINTERENTER;\n    }\n    else {\n        copy['type'] = EventType.POINTERLEAVE;\n    }\n    copy['target'] = copy['srcElement'] = target;\n    copy['bubbles'] = false;\n    copy['_originalEvent'] = e;\n    return copy;\n}\n\n/**\n * Whether the user agent is running on iOS.\n */\nconst isIos = typeof navigator !== 'undefined' && /iPhone|iPad|iPod/.test(navigator.userAgent);\n/**\n * A class representing a container node and all the event handlers\n * installed on it. Used so that handlers can be cleaned up if the\n * container is removed from the contract.\n */\nclass EventContractContainer {\n    element;\n    /**\n     * Array of event handlers and their corresponding event types that are\n     * installed on this container.\n     *\n     */\n    handlerInfos = [];\n    /**\n     * @param element The container Element.\n     */\n    constructor(element) {\n        this.element = element;\n    }\n    /**\n     * Installs the provided installer on the element owned by this container,\n     * and maintains a reference to resulting handler in order to remove it\n     * later if desired.\n     */\n    addEventListener(eventType, getHandler, passive) {\n        // In iOS, event bubbling doesn't happen automatically in any DOM element,\n        // unless it has an onclick attribute or DOM event handler attached to it.\n        // This breaks JsAction in some cases. See \"Making Elements Clickable\"\n        // section at http://goo.gl/2VoGnB.\n        //\n        // A workaround for this issue is to change the CSS cursor style to 'pointer'\n        // for the container element, which magically turns on event bubbling. This\n        // solution is described in the comments section at http://goo.gl/6pEO1z.\n        //\n        // We use a navigator.userAgent check here as this problem is present both\n        // on Mobile Safari and thin WebKit wrappers, such as Chrome for iOS.\n        if (isIos) {\n            this.element.style.cursor = 'pointer';\n        }\n        this.handlerInfos.push(addEventListener(this.element, eventType, getHandler(this.element), passive));\n    }\n    /**\n     * Removes all the handlers installed on this container.\n     */\n    cleanUp() {\n        for (let i = 0; i < this.handlerInfos.length; i++) {\n            removeEventListener(this.element, this.handlerInfos[i]);\n        }\n        this.handlerInfos = [];\n    }\n}\n\nconst Char = {\n    /**\n     * The separator between the event name and action in the jsaction\n     * attribute value.\n     */\n    EVENT_ACTION_SEPARATOR: ':',\n};\n\n/** Added for readability when accessing stable property names. */\nfunction getEventType(eventInfo) {\n    return eventInfo.eventType;\n}\n/** Added for readability when accessing stable property names. */\nfunction setEventType(eventInfo, eventType) {\n    eventInfo.eventType = eventType;\n}\n/** Added for readability when accessing stable property names. */\nfunction getEvent(eventInfo) {\n    return eventInfo.event;\n}\n/** Added for readability when accessing stable property names. */\nfunction setEvent(eventInfo, event) {\n    eventInfo.event = event;\n}\n/** Added for readability when accessing stable property names. */\nfunction getTargetElement(eventInfo) {\n    return eventInfo.targetElement;\n}\n/** Added for readability when accessing stable property names. */\nfunction setTargetElement(eventInfo, targetElement) {\n    eventInfo.targetElement = targetElement;\n}\n/** Added for readability when accessing stable property names. */\nfunction getContainer(eventInfo) {\n    return eventInfo.eic;\n}\n/** Added for readability when accessing stable property names. */\nfunction setContainer(eventInfo, container) {\n    eventInfo.eic = container;\n}\n/** Added for readability when accessing stable property names. */\nfunction getTimestamp(eventInfo) {\n    return eventInfo.timeStamp;\n}\n/** Added for readability when accessing stable property names. */\nfunction setTimestamp(eventInfo, timestamp) {\n    eventInfo.timeStamp = timestamp;\n}\n/** Added for readability when accessing stable property names. */\nfunction getAction(eventInfo) {\n    return eventInfo.eia;\n}\n/** Added for readability when accessing stable property names. */\nfunction setAction(eventInfo, actionName, actionElement) {\n    eventInfo.eia = [actionName, actionElement];\n}\n/** Added for readability when accessing stable property names. */\nfunction unsetAction(eventInfo) {\n    eventInfo.eia = undefined;\n}\n/** Added for readability when accessing stable property names. */\nfunction getActionElement(actionInfo) {\n    return actionInfo[1];\n}\n/** Added for readability when accessing stable property names. */\nfunction getIsReplay(eventInfo) {\n    return eventInfo.eirp;\n}\n/** Added for readability when accessing stable property names. */\nfunction setIsReplay(eventInfo, replay) {\n    eventInfo.eirp = replay;\n}\n/** Added for readability when accessing stable property names. */\nfunction getResolved(eventInfo) {\n    return eventInfo.eir;\n}\n/** Added for readability when accessing stable property names. */\nfunction setResolved(eventInfo, resolved) {\n    eventInfo.eir = resolved;\n}\n/** Clones an `EventInfo` */\nfunction cloneEventInfo(eventInfo) {\n    return {\n        eventType: eventInfo.eventType,\n        event: eventInfo.event,\n        targetElement: eventInfo.targetElement,\n        eic: eventInfo.eic,\n        eia: eventInfo.eia,\n        timeStamp: eventInfo.timeStamp,\n        eirp: eventInfo.eirp,\n        eiack: eventInfo.eiack,\n        eir: eventInfo.eir,\n    };\n}\n/**\n * Utility function for creating an `EventInfo`.\n *\n * This can be used from code-size sensitive compilation units, as taking\n * parameters vs. an `Object` literal reduces code size.\n */\nfunction createEventInfoFromParameters(eventType, event, targetElement, container, timestamp, action, isReplay, a11yClickKey) {\n    return {\n        eventType,\n        event,\n        targetElement,\n        eic: container,\n        timeStamp: timestamp,\n        eia: action,\n        eirp: isReplay,\n        eiack: a11yClickKey,\n    };\n}\n/**\n * Utility class around an `EventInfo`.\n *\n * This should be used in compilation units that are less sensitive to code\n * size.\n */\nclass EventInfoWrapper {\n    eventInfo;\n    constructor(eventInfo) {\n        this.eventInfo = eventInfo;\n    }\n    getEventType() {\n        return getEventType(this.eventInfo);\n    }\n    setEventType(eventType) {\n        setEventType(this.eventInfo, eventType);\n    }\n    getEvent() {\n        return getEvent(this.eventInfo);\n    }\n    setEvent(event) {\n        setEvent(this.eventInfo, event);\n    }\n    getTargetElement() {\n        return getTargetElement(this.eventInfo);\n    }\n    setTargetElement(targetElement) {\n        setTargetElement(this.eventInfo, targetElement);\n    }\n    getContainer() {\n        return getContainer(this.eventInfo);\n    }\n    setContainer(container) {\n        setContainer(this.eventInfo, container);\n    }\n    getTimestamp() {\n        return getTimestamp(this.eventInfo);\n    }\n    setTimestamp(timestamp) {\n        setTimestamp(this.eventInfo, timestamp);\n    }\n    getAction() {\n        const action = getAction(this.eventInfo);\n        if (!action)\n            return undefined;\n        return {\n            name: action[0],\n            element: action[1],\n        };\n    }\n    setAction(action) {\n        if (!action) {\n            unsetAction(this.eventInfo);\n            return;\n        }\n        setAction(this.eventInfo, action.name, action.element);\n    }\n    getIsReplay() {\n        return getIsReplay(this.eventInfo);\n    }\n    setIsReplay(replay) {\n        setIsReplay(this.eventInfo, replay);\n    }\n    getResolved() {\n        return getResolved(this.eventInfo);\n    }\n    setResolved(resolved) {\n        setResolved(this.eventInfo, resolved);\n    }\n    clone() {\n        return new EventInfoWrapper(cloneEventInfo(this.eventInfo));\n    }\n}\n\n/**\n * Since maps from event to action are immutable we can use a single map\n * to represent the empty map.\n */\nconst EMPTY_ACTION_MAP = {};\n/**\n * This regular expression matches a semicolon.\n */\nconst REGEXP_SEMICOLON = /\\s*;\\s*/;\n/** If no event type is defined, defaults to `click`. */\nconst DEFAULT_EVENT_TYPE = EventType.CLICK;\n/** Resolves actions for Events. */\nclass ActionResolver {\n    a11yClickSupport = false;\n    clickModSupport = true;\n    syntheticMouseEventSupport;\n    updateEventInfoForA11yClick = undefined;\n    preventDefaultForA11yClick = undefined;\n    populateClickOnlyAction = undefined;\n    constructor({ syntheticMouseEventSupport = false, clickModSupport = true, } = {}) {\n        this.syntheticMouseEventSupport = syntheticMouseEventSupport;\n        this.clickModSupport = clickModSupport;\n    }\n    resolveEventType(eventInfo) {\n        // We distinguish modified and plain clicks in order to support the\n        // default browser behavior of modified clicks on links; usually to\n        // open the URL of the link in new tab or new window on ctrl/cmd\n        // click. A DOM 'click' event is mapped to the jsaction 'click'\n        // event iff there is no modifier present on the event. If there is\n        // a modifier, it's mapped to 'clickmod' instead.\n        //\n        // It's allowed to omit the event in the jsaction attribute. In that\n        // case, 'click' is assumed. Thus the following two are equivalent:\n        //\n        //   <a href=\"someurl\" jsaction=\"gna.fu\">\n        //   <a href=\"someurl\" jsaction=\"click:gna.fu\">\n        //\n        // For unmodified clicks, EventContract invokes the jsaction\n        // 'gna.fu'. For modified clicks, EventContract won't find a\n        // suitable action and leave the event to be handled by the\n        // browser.\n        //\n        // In order to also invoke a jsaction handler for a modifier click,\n        // 'clickmod' needs to be used:\n        //\n        //   <a href=\"someurl\" jsaction=\"clickmod:gna.fu\">\n        //\n        // EventContract invokes the jsaction 'gna.fu' for modified\n        // clicks. Unmodified clicks are left to the browser.\n        //\n        // In order to set up the event contract to handle both clickonly and\n        // clickmod, only addEvent(EventType.CLICK) is necessary.\n        //\n        // In order to set up the event contract to handle click,\n        // addEvent() is necessary for CLICK, KEYDOWN, and KEYPRESS event types.  If\n        // a11y click support is enabled, addEvent() will set up the appropriate key\n        // event handler automatically.\n        if (this.clickModSupport &&\n            getEventType(eventInfo) === EventType.CLICK &&\n            isModifiedClickEvent(getEvent(eventInfo))) {\n            setEventType(eventInfo, EventType.CLICKMOD);\n        }\n        else if (this.a11yClickSupport) {\n            this.updateEventInfoForA11yClick(eventInfo);\n        }\n    }\n    resolveAction(eventInfo) {\n        if (getResolved(eventInfo)) {\n            return;\n        }\n        this.populateAction(eventInfo, getTargetElement(eventInfo));\n        setResolved(eventInfo, true);\n    }\n    resolveParentAction(eventInfo) {\n        const action = getAction(eventInfo);\n        const actionElement = action && getActionElement(action);\n        unsetAction(eventInfo);\n        const parentNode = actionElement && this.getParentNode(actionElement);\n        if (!parentNode) {\n            return;\n        }\n        this.populateAction(eventInfo, parentNode);\n    }\n    /**\n     * Searches for a jsaction that the DOM event maps to and creates an\n     * object containing event information used for dispatching by\n     * jsaction.Dispatcher. This method populates the `action` and `actionElement`\n     * fields of the EventInfo object passed in by finding the first\n     * jsaction attribute above the target Node of the event, and below\n     * the container Node, that specifies a jsaction for the event\n     * type. If no such jsaction is found, then action is undefined.\n     *\n     * @param eventInfo `EventInfo` to set `action` and `actionElement` if an\n     *    action is found on any `Element` in the path of the `Event`.\n     */\n    populateAction(eventInfo, currentTarget) {\n        let actionElement = currentTarget;\n        while (actionElement && actionElement !== getContainer(eventInfo)) {\n            if (actionElement.nodeType === Node.ELEMENT_NODE) {\n                this.populateActionOnElement(actionElement, eventInfo);\n            }\n            if (getAction(eventInfo)) {\n                // An event is handled by at most one jsaction. Thus we stop at the\n                // first matching jsaction specified in a jsaction attribute up the\n                // ancestor chain of the event target node.\n                break;\n            }\n            actionElement = this.getParentNode(actionElement);\n        }\n        const action = getAction(eventInfo);\n        if (!action) {\n            // No action found.\n            return;\n        }\n        if (this.a11yClickSupport) {\n            this.preventDefaultForA11yClick(eventInfo);\n        }\n        // We attempt to handle the mouseenter/mouseleave events here by\n        // detecting whether the mouseover/mouseout events correspond to\n        // entering/leaving an element.\n        if (this.syntheticMouseEventSupport) {\n            if (getEventType(eventInfo) === EventType.MOUSEENTER ||\n                getEventType(eventInfo) === EventType.MOUSELEAVE ||\n                getEventType(eventInfo) === EventType.POINTERENTER ||\n                getEventType(eventInfo) === EventType.POINTERLEAVE) {\n                // We attempt to handle the mouseenter/mouseleave events here by\n                // detecting whether the mouseover/mouseout events correspond to\n                // entering/leaving an element.\n                if (isMouseSpecialEvent(getEvent(eventInfo), getEventType(eventInfo), getActionElement(action))) {\n                    // If both mouseover/mouseout and mouseenter/mouseleave events are\n                    // enabled, two separate handlers for mouseover/mouseout are\n                    // registered. Both handlers will see the same event instance\n                    // so we create a copy to avoid interfering with the dispatching of\n                    // the mouseover/mouseout event.\n                    const copiedEvent = createMouseSpecialEvent(getEvent(eventInfo), getActionElement(action));\n                    setEvent(eventInfo, copiedEvent);\n                    // Since the mouseenter/mouseleave events do not bubble, the target\n                    // of the event is technically the `actionElement` (the node with the\n                    // `jsaction` attribute)\n                    setTargetElement(eventInfo, getActionElement(action));\n                }\n                else {\n                    unsetAction(eventInfo);\n                }\n            }\n        }\n    }\n    /**\n     * Walk to the parent node, unless the node has a different owner in\n     * which case we walk to the owner. Attempt to walk to host of a\n     * shadow root if needed.\n     */\n    getParentNode(element) {\n        const owner = element[Property.OWNER];\n        if (owner) {\n            return owner;\n        }\n        const parentNode = element.parentNode;\n        if (parentNode?.nodeName === '#document-fragment') {\n            return parentNode?.host ?? null;\n        }\n        return parentNode;\n    }\n    /**\n     * Accesses the jsaction map on a node and retrieves the name of the\n     * action the given event is mapped to, if any. It parses the\n     * attribute value and stores it in a property on the node for\n     * subsequent retrieval without re-parsing and re-accessing the\n     * attribute.\n     *\n     * @param actionElement The DOM node to retrieve the jsaction map from.\n     * @param eventInfo `EventInfo` to set `action` and `actionElement` if an\n     *    action is found on the `actionElement`.\n     */\n    populateActionOnElement(actionElement, eventInfo) {\n        const actionMap = this.parseActions(actionElement);\n        const actionName = actionMap[getEventType(eventInfo)];\n        if (actionName !== undefined) {\n            setAction(eventInfo, actionName, actionElement);\n        }\n        if (this.a11yClickSupport) {\n            this.populateClickOnlyAction(actionElement, eventInfo, actionMap);\n        }\n    }\n    /**\n     * Parses and caches an element's jsaction element into a map.\n     *\n     * This is primarily for internal use.\n     *\n     * @param actionElement The DOM node to retrieve the jsaction map from.\n     * @return Map from event to qualified name of the jsaction bound to it.\n     */\n    parseActions(actionElement) {\n        let actionMap = get(actionElement);\n        if (!actionMap) {\n            const jsactionAttribute = actionElement.getAttribute(Attribute.JSACTION);\n            if (!jsactionAttribute) {\n                actionMap = EMPTY_ACTION_MAP;\n                set(actionElement, actionMap);\n            }\n            else {\n                actionMap = getParsed(jsactionAttribute);\n                if (!actionMap) {\n                    actionMap = {};\n                    const values = jsactionAttribute.split(REGEXP_SEMICOLON);\n                    for (let idx = 0; idx < values.length; idx++) {\n                        const value = values[idx];\n                        if (!value) {\n                            continue;\n                        }\n                        const colon = value.indexOf(Char.EVENT_ACTION_SEPARATOR);\n                        const hasColon = colon !== -1;\n                        const type = hasColon ? value.substr(0, colon).trim() : DEFAULT_EVENT_TYPE;\n                        const action = hasColon ? value.substr(colon + 1).trim() : value;\n                        actionMap[type] = action;\n                    }\n                    setParsed(jsactionAttribute, actionMap);\n                }\n                set(actionElement, actionMap);\n            }\n        }\n        return actionMap;\n    }\n    addA11yClickSupport(updateEventInfoForA11yClick, preventDefaultForA11yClick, populateClickOnlyAction) {\n        this.a11yClickSupport = true;\n        this.updateEventInfoForA11yClick = updateEventInfoForA11yClick;\n        this.preventDefaultForA11yClick = preventDefaultForA11yClick;\n        this.populateClickOnlyAction = populateClickOnlyAction;\n    }\n}\n\n/**\n * @fileoverview An enum to control who can call certain jsaction APIs.\n */\nvar Restriction;\n(function (Restriction) {\n    Restriction[Restriction[\"I_AM_THE_JSACTION_FRAMEWORK\"] = 0] = \"I_AM_THE_JSACTION_FRAMEWORK\";\n})(Restriction || (Restriction = {}));\n\n/**\n * Receives a DOM event, determines the jsaction associated with the source\n * element of the DOM event, and invokes the handler associated with the\n * jsaction.\n */\nclass Dispatcher {\n    dispatchDelegate;\n    // The ActionResolver to use to resolve actions.\n    actionResolver;\n    /** The replayer function to be called when there are queued events. */\n    eventReplayer;\n    /** Whether the event replay is scheduled. */\n    eventReplayScheduled = false;\n    /** The queue of events. */\n    replayEventInfoWrappers = [];\n    /**\n     * Options are:\n     *   - `eventReplayer`: When the event contract dispatches replay events\n     *      to the Dispatcher, the Dispatcher collects them and in the next tick\n     *      dispatches them to the `eventReplayer`. Defaults to dispatching to `dispatchDelegate`.\n     * @param dispatchDelegate A function that should handle dispatching an `EventInfoWrapper` to handlers.\n     */\n    constructor(dispatchDelegate, { actionResolver, eventReplayer, } = {}) {\n        this.dispatchDelegate = dispatchDelegate;\n        this.actionResolver = actionResolver;\n        this.eventReplayer = eventReplayer;\n    }\n    /**\n     * Receives an event or the event queue from the EventContract. The event\n     * queue is copied and it attempts to replay.\n     * If event info is passed in it looks for an action handler that can handle\n     * the given event.  If there is no handler registered queues the event and\n     * checks if a loader is registered for the given namespace. If so, calls it.\n     *\n     * Alternatively, if in global dispatch mode, calls all registered global\n     * handlers for the appropriate event type.\n     *\n     * The three functionalities of this call are deliberately not split into\n     * three methods (and then declared as an abstract interface), because the\n     * interface is used by EventContract, which lives in a different jsbinary.\n     * Therefore the interface between the three is defined entirely in terms that\n     * are invariant under jscompiler processing (Function and Array, as opposed\n     * to a custom type with method names).\n     *\n     * @param eventInfo The info for the event that triggered this call or the\n     *     queue of events from EventContract.\n     */\n    dispatch(eventInfo) {\n        const eventInfoWrapper = new EventInfoWrapper(eventInfo);\n        this.actionResolver?.resolveEventType(eventInfo);\n        this.actionResolver?.resolveAction(eventInfo);\n        const action = eventInfoWrapper.getAction();\n        if (action && shouldPreventDefaultBeforeDispatching(action.element, eventInfoWrapper)) {\n            preventDefault(eventInfoWrapper.getEvent());\n        }\n        if (this.eventReplayer && eventInfoWrapper.getIsReplay()) {\n            this.scheduleEventInfoWrapperReplay(eventInfoWrapper);\n            return;\n        }\n        this.dispatchDelegate(eventInfoWrapper);\n    }\n    /**\n     * Schedules an `EventInfoWrapper` for replay. The replaying will happen in its own\n     * stack once the current flow cedes control. This is done to mimic\n     * browser event handling.\n     */\n    scheduleEventInfoWrapperReplay(eventInfoWrapper) {\n        this.replayEventInfoWrappers.push(eventInfoWrapper);\n        if (this.eventReplayScheduled) {\n            return;\n        }\n        this.eventReplayScheduled = true;\n        Promise.resolve().then(() => {\n            this.eventReplayScheduled = false;\n            this.eventReplayer(this.replayEventInfoWrappers);\n        });\n    }\n}\n/**\n * Returns true if the default action of this event should be prevented before\n * this event is dispatched.\n */\nfunction shouldPreventDefaultBeforeDispatching(actionElement, eventInfoWrapper) {\n    // Prevent browser from following <a> node links if a jsaction is present\n    // and we are dispatching the action now. Note that the targetElement may be\n    // a child of an anchor that has a jsaction attached. For that reason, we\n    // need to check the actionElement rather than the targetElement.\n    return (actionElement.tagName === 'A' &&\n        (eventInfoWrapper.getEventType() === EventType.CLICK ||\n            eventInfoWrapper.getEventType() === EventType.CLICKMOD));\n}\n\n/** An internal symbol used to indicate whether propagation should be stopped or not. */\nconst PROPAGATION_STOPPED_SYMBOL = \n/* @__PURE__ */ Symbol.for('propagationStopped');\n/** Extra event phases beyond what the browser provides. */\nconst EventPhase = {\n    REPLAY: 101,\n};\nconst PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS = ' Because event replay occurs after browser dispatch, `preventDefault` would have no ' +\n    'effect. You can check whether an event is being replayed by accessing the event phase: ' +\n    '`event.eventPhase === EventPhase.REPLAY`.';\nconst PREVENT_DEFAULT_ERROR_MESSAGE = `\\`preventDefault\\` called during event replay.`;\nconst COMPOSED_PATH_ERROR_MESSAGE_DETAILS = ' Because event replay occurs after browser ' +\n    'dispatch, `composedPath()` will be empty. Iterate parent nodes from `event.target` or ' +\n    '`event.currentTarget` if you need to check elements in the event path.';\nconst COMPOSED_PATH_ERROR_MESSAGE = `\\`composedPath\\` called during event replay.`;\n/**\n * A dispatcher that uses browser-based `Event` semantics, for example bubbling, `stopPropagation`,\n * `currentTarget`, etc.\n */\nclass EventDispatcher {\n    dispatchDelegate;\n    clickModSupport;\n    actionResolver;\n    dispatcher;\n    constructor(dispatchDelegate, clickModSupport = true) {\n        this.dispatchDelegate = dispatchDelegate;\n        this.clickModSupport = clickModSupport;\n        this.actionResolver = new ActionResolver({ clickModSupport });\n        this.dispatcher = new Dispatcher((eventInfoWrapper) => {\n            this.dispatchToDelegate(eventInfoWrapper);\n        }, {\n            actionResolver: this.actionResolver,\n        });\n    }\n    /**\n     * The entrypoint for the `EventContract` dispatch.\n     */\n    dispatch(eventInfo) {\n        this.dispatcher.dispatch(eventInfo);\n    }\n    /** Internal method that does basic disaptching. */\n    dispatchToDelegate(eventInfoWrapper) {\n        if (eventInfoWrapper.getIsReplay()) {\n            prepareEventForReplay(eventInfoWrapper);\n        }\n        prepareEventForBubbling(eventInfoWrapper);\n        while (eventInfoWrapper.getAction()) {\n            prepareEventForDispatch(eventInfoWrapper);\n            // If this is a capture event, ONLY dispatch if the action element is the target.\n            if (isCaptureEventType(eventInfoWrapper.getEventType()) &&\n                eventInfoWrapper.getAction().element !== eventInfoWrapper.getTargetElement()) {\n                return;\n            }\n            this.dispatchDelegate(eventInfoWrapper.getEvent(), eventInfoWrapper.getAction().name);\n            if (propagationStopped(eventInfoWrapper)) {\n                return;\n            }\n            this.actionResolver.resolveParentAction(eventInfoWrapper.eventInfo);\n        }\n    }\n}\nfunction prepareEventForBubbling(eventInfoWrapper) {\n    const event = eventInfoWrapper.getEvent();\n    const originalStopPropagation = eventInfoWrapper.getEvent().stopPropagation.bind(event);\n    const stopPropagation = () => {\n        event[PROPAGATION_STOPPED_SYMBOL] = true;\n        originalStopPropagation();\n    };\n    patchEventInstance(event, 'stopPropagation', stopPropagation);\n    patchEventInstance(event, 'stopImmediatePropagation', stopPropagation);\n}\nfunction propagationStopped(eventInfoWrapper) {\n    const event = eventInfoWrapper.getEvent();\n    return !!event[PROPAGATION_STOPPED_SYMBOL];\n}\nfunction prepareEventForReplay(eventInfoWrapper) {\n    const event = eventInfoWrapper.getEvent();\n    const target = eventInfoWrapper.getTargetElement();\n    const originalPreventDefault = event.preventDefault.bind(event);\n    patchEventInstance(event, 'target', target);\n    patchEventInstance(event, 'eventPhase', EventPhase.REPLAY);\n    patchEventInstance(event, 'preventDefault', () => {\n        originalPreventDefault();\n        throw new Error(PREVENT_DEFAULT_ERROR_MESSAGE + (ngDevMode ? PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS : ''));\n    });\n    patchEventInstance(event, 'composedPath', () => {\n        throw new Error(COMPOSED_PATH_ERROR_MESSAGE + (ngDevMode ? COMPOSED_PATH_ERROR_MESSAGE_DETAILS : ''));\n    });\n}\nfunction prepareEventForDispatch(eventInfoWrapper) {\n    const event = eventInfoWrapper.getEvent();\n    const currentTarget = eventInfoWrapper.getAction()?.element;\n    if (currentTarget) {\n        patchEventInstance(event, 'currentTarget', currentTarget, {\n            // `currentTarget` is going to get reassigned every dispatch.\n            configurable: true,\n        });\n    }\n}\n/**\n * Patch `Event` instance during non-standard `Event` dispatch. This patches just the `Event`\n * instance that the browser created, it does not patch global properties or methods.\n *\n * This is necessary because dispatching an `Event` outside of browser dispatch results in\n * incorrect properties and methods that need to be polyfilled or do not work.\n *\n * JSAction dispatch adds two extra \"phases\" to event dispatch:\n * 1. Event delegation - the event is being dispatched by a delegating event handler on a container\n *    (typically `window.document.documentElement`), to a delegated event handler on some child\n *    element. Certain `Event` properties will be unintuitive, such as `currentTarget`, which would\n *    be the container rather than the child element. Bubbling would also not work. In order to\n *    emulate the browser, these properties and methods on the `Event` are patched.\n * 2. Event replay - the event is being dispatched by the framework once the handlers have been\n *    loaded (during hydration, or late-loaded). Certain `Event` properties can be unset by the\n *    browser because the `Event` is no longer actively being dispatched, such as `target`. Other\n *    methods have no effect because the `Event` has already been dispatched, such as\n *    `preventDefault`. Bubbling would also not work. These properties and methods are patched,\n *    either to fill in information that the browser may have removed, or to throw errors in methods\n *    that no longer behave as expected.\n */\nfunction patchEventInstance(event, property, value, { configurable = false } = {}) {\n    Object.defineProperty(event, property, { value, configurable });\n}\n/**\n * Registers deferred functionality for an EventContract and a Jsaction\n * Dispatcher.\n */\nfunction registerDispatcher$1(eventContract, dispatcher) {\n    eventContract.ecrd((eventInfo) => {\n        dispatcher.dispatch(eventInfo);\n    }, Restriction.I_AM_THE_JSACTION_FRAMEWORK);\n}\n\n/** Creates an `EarlyJsactionData` object. */\nfunction createEarlyJsactionData(container) {\n    const q = [];\n    const d = (eventInfo) => {\n        q.push(eventInfo);\n    };\n    const h = (event) => {\n        d(createEventInfoFromParameters(event.type, event, event.target, container, Date.now()));\n    };\n    return {\n        c: container,\n        q,\n        et: [],\n        etc: [],\n        d,\n        h,\n    };\n}\n/** Add all the events to the container stored in the `EarlyJsactionData`. */\nfunction addEvents(earlyJsactionData, types, capture) {\n    for (let i = 0; i < types.length; i++) {\n        const eventType = types[i];\n        const eventTypes = capture ? earlyJsactionData.etc : earlyJsactionData.et;\n        eventTypes.push(eventType);\n        earlyJsactionData.c.addEventListener(eventType, earlyJsactionData.h, capture);\n    }\n}\n/** Get the queued `EventInfo` objects that were dispatched before a dispatcher was registered. */\nfunction getQueuedEventInfos(earlyJsactionData) {\n    return earlyJsactionData?.q ?? [];\n}\n/** Register a different dispatcher function on the `EarlyJsactionData`. */\nfunction registerDispatcher(earlyJsactionData, dispatcher) {\n    if (!earlyJsactionData) {\n        return;\n    }\n    earlyJsactionData.d = dispatcher;\n}\n/** Removes all event listener handlers. */\nfunction removeAllEventListeners(earlyJsactionData) {\n    if (!earlyJsactionData) {\n        return;\n    }\n    removeEventListeners(earlyJsactionData.c, earlyJsactionData.et, earlyJsactionData.h);\n    removeEventListeners(earlyJsactionData.c, earlyJsactionData.etc, earlyJsactionData.h, true);\n}\nfunction removeEventListeners(container, eventTypes, earlyEventHandler, capture) {\n    for (let i = 0; i < eventTypes.length; i++) {\n        container.removeEventListener(eventTypes[i], earlyEventHandler, /* useCapture */ capture);\n    }\n}\n\n/**\n * @define Support for the non-bubbling mouseenter and mouseleave events.  This\n * flag can be overridden in a build rule.\n */\nconst MOUSE_SPECIAL_SUPPORT = false;\n\n/**\n * @fileoverview Implements the local event handling contract. This\n * allows DOM objects in a container that enters into this contract to\n * define event handlers which are executed in a local context.\n *\n * One EventContract instance can manage the contract for multiple\n * containers, which are added using the addContainer() method.\n *\n * Events can be registered using the addEvent() method.\n *\n * A Dispatcher is added using the registerDispatcher() method. Until there is\n * a dispatcher, events are queued. The idea is that the EventContract\n * class is inlined in the HTML of the top level page and instantiated\n * right after the start of <body>. The Dispatcher class is contained\n * in the external deferred js, and instantiated and registered with\n * EventContract when the external javascript in the page loads. The\n * external javascript will also register the jsaction handlers, which\n * then pick up the queued events at the time of registration.\n *\n * Since this class is meant to be inlined in the main page HTML, the\n * size of the binary compiled from this file MUST be kept as small as\n * possible and thus its dependencies to a minimum.\n */\n/**\n * EventContract intercepts events in the bubbling phase at the\n * boundary of a container element, and maps them to generic actions\n * which are specified using the custom jsaction attribute in\n * HTML. Behavior of the application is then specified in terms of\n * handler for such actions, cf. jsaction.Dispatcher in dispatcher.js.\n *\n * This has several benefits: (1) No DOM event handlers need to be\n * registered on the specific elements in the UI. (2) The set of\n * events that the application has to handle can be specified in terms\n * of the semantics of the application, rather than in terms of DOM\n * events. (3) Invocation of handlers can be delayed and handlers can\n * be delay loaded in a generic way.\n */\nclass EventContract {\n    static MOUSE_SPECIAL_SUPPORT = MOUSE_SPECIAL_SUPPORT;\n    containerManager;\n    /**\n     * The DOM events which this contract covers. Used to prevent double\n     * registration of event types. The value of the map is the\n     * internally created DOM event handler function that handles the\n     * DOM events. See addEvent().\n     *\n     */\n    eventHandlers = {};\n    browserEventTypeToExtraEventTypes = {};\n    /**\n     * The dispatcher function. Events are passed to this function for\n     * handling once it was set using the registerDispatcher() method. This is\n     * done because the function is passed from another jsbinary, so passing the\n     * instance and invoking the method here would require to leave the method\n     * unobfuscated.\n     */\n    dispatcher = null;\n    /**\n     * The list of suspended `EventInfo` that will be dispatched\n     * as soon as the `Dispatcher` is registered.\n     */\n    queuedEventInfos = [];\n    constructor(containerManager) {\n        this.containerManager = containerManager;\n    }\n    handleEvent(eventType, event, container) {\n        const eventInfo = createEventInfoFromParameters(\n        /* eventType= */ eventType, \n        /* event= */ event, \n        /* targetElement= */ event.target, \n        /* container= */ container, \n        /* timestamp= */ Date.now());\n        this.handleEventInfo(eventInfo);\n    }\n    /**\n     * Handle an `EventInfo`.\n     */\n    handleEventInfo(eventInfo) {\n        if (!this.dispatcher) {\n            // All events are queued when the dispatcher isn't yet loaded.\n            setIsReplay(eventInfo, true);\n            this.queuedEventInfos?.push(eventInfo);\n            return;\n        }\n        this.dispatcher(eventInfo);\n    }\n    /**\n     * Enables jsaction handlers to be called for the event type given by\n     * name.\n     *\n     * If the event is already registered, this does nothing.\n     *\n     * @param prefixedEventType If supplied, this event is used in\n     *     the actual browser event registration instead of the name that is\n     *     exposed to jsaction. Use this if you e.g. want users to be able\n     *     to subscribe to jsaction=\"transitionEnd:foo\" while the underlying\n     *     event is webkitTransitionEnd in one browser and mozTransitionEnd\n     *     in another.\n     *\n     * @param passive A boolean value that, if `true`, indicates that the event\n     *     handler will never call `preventDefault()`.\n     */\n    addEvent(eventType, prefixedEventType, passive) {\n        if (eventType in this.eventHandlers || !this.containerManager) {\n            return;\n        }\n        if (!EventContract.MOUSE_SPECIAL_SUPPORT && MOUSE_SPECIAL_EVENT_TYPES.indexOf(eventType) >= 0) {\n            return;\n        }\n        const eventHandler = (eventType, event, container) => {\n            this.handleEvent(eventType, event, container);\n        };\n        // Store the callback to allow us to replay events.\n        this.eventHandlers[eventType] = eventHandler;\n        const browserEventType = getBrowserEventType(prefixedEventType || eventType);\n        if (browserEventType !== eventType) {\n            const eventTypes = this.browserEventTypeToExtraEventTypes[browserEventType] || [];\n            eventTypes.push(eventType);\n            this.browserEventTypeToExtraEventTypes[browserEventType] = eventTypes;\n        }\n        this.containerManager.addEventListener(browserEventType, (element) => {\n            return (event) => {\n                eventHandler(eventType, event, element);\n            };\n        }, passive);\n    }\n    /**\n     * Gets the queued early events and replay them using the appropriate handler\n     * in the provided event contract. Once all the events are replayed, it cleans\n     * up the early contract.\n     */\n    replayEarlyEvents(earlyJsactionData = window._ejsa) {\n        // Check if the early contract is present and prevent calling this function\n        // more than once.\n        if (!earlyJsactionData) {\n            return;\n        }\n        // Replay the early contract events.\n        this.replayEarlyEventInfos(earlyJsactionData.q);\n        // Clean up the early contract.\n        removeAllEventListeners(earlyJsactionData);\n        delete window._ejsa;\n    }\n    /**\n     * Replays all the early `EventInfo` objects, dispatching them through the normal\n     * `EventContract` flow.\n     */\n    replayEarlyEventInfos(earlyEventInfos) {\n        for (let i = 0; i < earlyEventInfos.length; i++) {\n            const earlyEventInfo = earlyEventInfos[i];\n            const eventTypes = this.getEventTypesForBrowserEventType(earlyEventInfo.eventType);\n            for (let j = 0; j < eventTypes.length; j++) {\n                const eventInfo = cloneEventInfo(earlyEventInfo);\n                // EventInfo eventType maps to JSAction's internal event type,\n                // rather than the browser event type.\n                setEventType(eventInfo, eventTypes[j]);\n                this.handleEventInfo(eventInfo);\n            }\n        }\n    }\n    /**\n     * Returns all JSAction event types that have been registered for a given\n     * browser event type.\n     */\n    getEventTypesForBrowserEventType(browserEventType) {\n        const eventTypes = [];\n        if (this.eventHandlers[browserEventType]) {\n            eventTypes.push(browserEventType);\n        }\n        if (this.browserEventTypeToExtraEventTypes[browserEventType]) {\n            eventTypes.push(...this.browserEventTypeToExtraEventTypes[browserEventType]);\n        }\n        return eventTypes;\n    }\n    /**\n     * Returns the event handler function for a given event type.\n     */\n    handler(eventType) {\n        return this.eventHandlers[eventType];\n    }\n    /**\n     * Cleans up the event contract. This resets all of the `EventContract`'s\n     * internal state. Users are responsible for not using this `EventContract`\n     * after it has been cleaned up.\n     */\n    cleanUp() {\n        this.containerManager?.cleanUp();\n        this.containerManager = null;\n        this.eventHandlers = {};\n        this.browserEventTypeToExtraEventTypes = {};\n        this.dispatcher = null;\n        this.queuedEventInfos = [];\n    }\n    /**\n     * Register a dispatcher function. Event info of each event mapped to\n     * a jsaction is passed for handling to this callback. The queued\n     * events are passed as well to the dispatcher for later replaying\n     * once the dispatcher is registered. Clears the event queue to null.\n     *\n     * @param dispatcher The dispatcher function.\n     * @param restriction\n     */\n    registerDispatcher(dispatcher, restriction) {\n        this.ecrd(dispatcher, restriction);\n    }\n    /**\n     * Unrenamed alias for registerDispatcher. Necessary for any codebases that\n     * split the `EventContract` and `Dispatcher` code into different compilation\n     * units.\n     */\n    ecrd(dispatcher, restriction) {\n        this.dispatcher = dispatcher;\n        if (this.queuedEventInfos?.length) {\n            for (let i = 0; i < this.queuedEventInfos.length; i++) {\n                this.handleEventInfo(this.queuedEventInfos[i]);\n            }\n            this.queuedEventInfos = null;\n        }\n    }\n}\n\n/**\n * Creates an `EarlyJsactionData`, adds events to it, and populates it on a nested object on\n * the window.\n */\nfunction bootstrapAppScopedEarlyEventContract(container, appId, bubbleEventTypes, captureEventTypes, dataContainer = window) {\n    const earlyJsactionData = createEarlyJsactionData(container);\n    if (!dataContainer._ejsas) {\n        dataContainer._ejsas = {};\n    }\n    dataContainer._ejsas[appId] = earlyJsactionData;\n    addEvents(earlyJsactionData, bubbleEventTypes);\n    addEvents(earlyJsactionData, captureEventTypes, /* capture= */ true);\n}\n/** Get the queued `EventInfo` objects that were dispatched before a dispatcher was registered. */\nfunction getAppScopedQueuedEventInfos(appId, dataContainer = window) {\n    return getQueuedEventInfos(dataContainer._ejsas?.[appId]);\n}\n/**\n * Registers a dispatcher function on the `EarlyJsactionData` present on the nested object on the\n * window.\n */\nfunction registerAppScopedDispatcher(restriction, appId, dispatcher, dataContainer = window) {\n    registerDispatcher(dataContainer._ejsas?.[appId], dispatcher);\n}\n/** Removes all event listener handlers. */\nfunction removeAllAppScopedEventListeners(appId, dataContainer = window) {\n    removeAllEventListeners(dataContainer._ejsas?.[appId]);\n}\n/** Clear the early event contract. */\nfunction clearAppScopedEarlyEventContract(appId, dataContainer = window) {\n    if (!dataContainer._ejsas) {\n        return;\n    }\n    dataContainer._ejsas[appId] = undefined;\n}\n\nexport { Attribute, EventContract, EventContractContainer, EventDispatcher, EventInfoWrapper, EventPhase, bootstrapAppScopedEarlyEventContract, clearAppScopedEarlyEventContract, getDefaulted as getActionCache, getAppScopedQueuedEventInfos, isCaptureEventType, isEarlyEventType, registerAppScopedDispatcher, registerDispatcher$1 as registerDispatcher, removeAllAppScopedEventListeners };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,SAAS,QAAQ,2BAA2B;;AAErD;AACA,MAAMC,QAAQ,GAAG;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,QAAQ,EAAE,YAAY;EACtB;AACJ;AACA;AACA;AACA;AACA;EACIC,KAAK,EAAE;AACX,CAAC;;AAED;AACA;AACA;AACA,MAAMC,UAAU,GAAG,CAAC,CAAC;AACrB;AACA;AACA;AACA,SAASC,GAAGA,CAACC,OAAO,EAAE;EAClB,OAAOA,OAAO,CAACL,QAAQ,CAACC,QAAQ,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA,SAASK,YAAYA,CAACD,OAAO,EAAE;EAC3B,MAAME,KAAK,GAAGH,GAAG,CAACC,OAAO,CAAC,IAAI,CAAC,CAAC;EAChCG,GAAG,CAACH,OAAO,EAAEE,KAAK,CAAC;EACnB,OAAOA,KAAK;AAChB;AACA;AACA;AACA;AACA,SAASC,GAAGA,CAACH,OAAO,EAAEI,SAAS,EAAE;EAC7BJ,OAAO,CAACL,QAAQ,CAACC,QAAQ,CAAC,GAAGQ,SAAS;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,IAAI,EAAE;EACrB,OAAOR,UAAU,CAACQ,IAAI,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACD,IAAI,EAAEE,MAAM,EAAE;EAC7BV,UAAU,CAACQ,IAAI,CAAC,GAAGE,MAAM;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG;EACd;AACJ;AACA;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EACd;AACJ;AACA;AACA;AACA;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EACd;AACJ;AACA;AACA;AACA;EACIC,OAAO,EAAE,SAAS;EAClB;AACJ;AACA;EACIC,IAAI,EAAE,MAAM;EACZ;AACJ;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,MAAM,EAAE,QAAQ;EAChB;AACJ;AACA;AACA;AACA;EACIC,OAAO,EAAE,SAAS;EAClB;AACJ;AACA;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EACd;AACJ;AACA;AACA;AACA;EACIC,SAAS,EAAE,WAAW;EACtB;AACJ;AACA;AACA;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;EACIC,UAAU,EAAE,YAAY;EACxB;AACJ;AACA;AACA;EACIC,UAAU,EAAE,YAAY;EACxB;AACJ;AACA;AACA;AACA;EACIC,WAAW,EAAE,aAAa;EAC1B;AACJ;AACA;AACA;AACA;EACIC,UAAU,EAAE,YAAY;EACxB;AACJ;AACA;AACA;EACIC,YAAY,EAAE,cAAc;EAC5B;AACJ;AACA;AACA;EACIC,YAAY,EAAE,cAAc;EAC5B;AACJ;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EACd;AACJ;AACA;AACA;AACA;EACIC,IAAI,EAAE,MAAM;EACZ;AACJ;AACA;AACA;EACIC,UAAU,EAAE,YAAY;EACxB;AACJ;AACA;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;EACIC,SAAS,EAAE,WAAW;EACtB;AACJ;AACA;AACA;AACA;EACIC,MAAM,EAAE;AAAQ,CAAC;AACrB;AACA,MAAMC,yBAAyB,GAAG,CAC9B1B,SAAS,CAACc,UAAU,EACpBd,SAAS,CAACe,UAAU,EACpB,cAAc,EACd,cAAc,CACjB;AACD;AACA,MAAMY,kBAAkB,GAAG,CACvB3B,SAAS,CAACC,KAAK,EACfD,SAAS,CAACG,QAAQ,EAClBH,SAAS,CAACK,OAAO,EACjBL,SAAS,CAACO,QAAQ,EAClBP,SAAS,CAACS,OAAO,EACjBT,SAAS,CAACW,KAAK,EACfX,SAAS,CAACU,QAAQ,EAClBV,SAAS,CAACY,SAAS,EACnBZ,SAAS,CAACa,QAAQ,EAClBb,SAAS,CAACQ,MAAM,EAChBR,SAAS,CAACsB,UAAU,EACpBtB,SAAS,CAACuB,QAAQ,EAClBvB,SAAS,CAACwB,SAAS,EACnB,aAAa,EACb,UAAU,EACV,QAAQ,EACR,kBAAkB,EAClB,mBAAmB,EACnB,gBAAgB,EAChB,aAAa,EACb,OAAO,EACP,QAAQ,EACR,MAAM,EACN,KAAK,EACL,OAAO,EACP,WAAW,EACX,SAAS,EACT,OAAO,EACP,aAAa,EACb,UAAU,EACV,WAAW,EACX,WAAW,EACX,MAAM,EACN,WAAW,EACX,SAAS,EACT,aAAa,EACb,aAAa,EACb,WAAW,EACX,eAAe,EACf,aAAa,EACb,YAAY,EACZ,mBAAmB,EACnB,oBAAoB;AACpB;AACA,OAAO,EACP,gBAAgB;AAChB;AACA,UAAU,EACV,UAAU,EACV,kBAAkB;AAClB;AACA,aAAa,CAChB;AACD;AACA,MAAMI,mBAAmB,GAAG,CACxB5B,SAAS,CAACI,KAAK,EACfJ,SAAS,CAACM,IAAI,EACdN,SAAS,CAACoB,KAAK,EACfpB,SAAS,CAACqB,IAAI,EACdrB,SAAS,CAACyB,MAAM,CACnB;AACD;AACA;AACA;AACA;AACA;AACA,MAAMI,kBAAkB,GAAIC,SAAS,IAAKF,mBAAmB,CAACG,OAAO,CAACD,SAAS,CAAC,IAAI,CAAC;AACrF;AACA,MAAME,iBAAiB,GAAGL,kBAAkB,CAACM,MAAM,CAACL,mBAAmB,CAAC;AACxE;AACA;AACA;AACA,MAAMM,gBAAgB,GAAIJ,SAAS,IAAKE,iBAAiB,CAACD,OAAO,CAACD,SAAS,CAAC,IAAI,CAAC;;AAEjF;AACA;AACA;AACA,SAASK,mBAAmBA,CAACL,SAAS,EAAE;EACpC;EACA;EACA;EACA;EACA;EACA,IAAIA,SAAS,KAAK9B,SAAS,CAACc,UAAU,EAAE;IACpC,OAAOd,SAAS,CAACY,SAAS;EAC9B,CAAC,MACI,IAAIkB,SAAS,KAAK9B,SAAS,CAACe,UAAU,EAAE;IACzC,OAAOf,SAAS,CAACa,QAAQ;EAC7B,CAAC,MACI,IAAIiB,SAAS,KAAK9B,SAAS,CAACkB,YAAY,EAAE;IAC3C,OAAOlB,SAAS,CAACgB,WAAW;EAChC,CAAC,MACI,IAAIc,SAAS,KAAK9B,SAAS,CAACmB,YAAY,EAAE;IAC3C,OAAOnB,SAAS,CAACiB,UAAU;EAC/B;EACA,OAAOa,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,gBAAgBA,CAAC7C,OAAO,EAAEuC,SAAS,EAAEO,OAAO,EAAEC,OAAO,EAAE;EAC5D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIV,kBAAkB,CAACC,SAAS,CAAC,EAAE;IAC/BS,OAAO,GAAG,IAAI;EAClB;EACA,MAAMC,OAAO,GAAG,OAAOF,OAAO,KAAK,SAAS,GAAG;IAAEC,OAAO;IAAED;EAAQ,CAAC,GAAGC,OAAO;EAC7EhD,OAAO,CAAC6C,gBAAgB,CAACN,SAAS,EAAEO,OAAO,EAAEG,OAAO,CAAC;EACrD,OAAO;IAAEV,SAAS;IAAEO,OAAO;IAAEE,OAAO;IAAED;EAAQ,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,mBAAmBA,CAAClD,OAAO,EAAEmD,IAAI,EAAE;EACxC,IAAInD,OAAO,CAACkD,mBAAmB,EAAE;IAC7B;IACA;IACA;IACA,MAAMD,OAAO,GAAG,OAAOE,IAAI,CAACJ,OAAO,KAAK,SAAS,GAAG;MAAEC,OAAO,EAAEG,IAAI,CAACH;IAAQ,CAAC,GAAGG,IAAI,CAACH,OAAO;IAC5FhD,OAAO,CAACkD,mBAAmB,CAACC,IAAI,CAACZ,SAAS,EAAEY,IAAI,CAACL,OAAO,EAAEG,OAAO,CAAC;IAClE;EACJ,CAAC,MACI,IAAIjD,OAAO,CAACoD,WAAW,EAAE;IAC1B;IACApD,OAAO,CAACoD,WAAW,CAAC,KAAKD,IAAI,CAACZ,SAAS,EAAE,EAAEY,IAAI,CAACL,OAAO,CAAC;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA,SAASO,cAAcA,CAACC,CAAC,EAAE;EACvBA,CAAC,CAACD,cAAc,GAAGC,CAAC,CAACD,cAAc,CAAC,CAAC,GAAIC,CAAC,CAACC,WAAW,GAAG,KAAM;AACnE;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAI,WAAW,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACN,CAAC,EAAE;EACtB;IACA;IACAA,CAAC,CAACO,KAAK,KAAK,CAAC;IACT;IACCP,CAAC,CAACO,KAAK,IAAI,IAAI;IACZ;IACAP,CAAC,CAACQ,MAAM,KAAK,CAAE,CAAC;EAAA;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACT,CAAC,EAAE;EAC7B;IACA;IACCE,KAAK,IAAIF,CAAC,CAACU,OAAO;IACf;IACC,CAACR,KAAK,IAAIF,CAAC,CAACW,OAAQ,IACrBL,aAAa,CAACN,CAAC,CAAC;IAChB;IACAA,CAAC,CAACY;EAAQ;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACb,CAAC,EAAEc,IAAI,EAAEpE,OAAO,EAAE;EAC3C;EACA,MAAMqE,OAAO,GAAGf,CAAC,CAACgB,aAAa;EAC/B,OAAQ,CAAEhB,CAAC,CAACc,IAAI,KAAK3D,SAAS,CAACY,SAAS,IAAI+C,IAAI,KAAK3D,SAAS,CAACc,UAAU,IACpE+B,CAAC,CAACc,IAAI,KAAK3D,SAAS,CAACa,QAAQ,IAAI8C,IAAI,KAAK3D,SAAS,CAACe,UAAW,IAC/D8B,CAAC,CAACc,IAAI,KAAK3D,SAAS,CAACgB,WAAW,IAAI2C,IAAI,KAAK3D,SAAS,CAACkB,YAAa,IACpE2B,CAAC,CAACc,IAAI,KAAK3D,SAAS,CAACiB,UAAU,IAAI0C,IAAI,KAAK3D,SAAS,CAACmB,YAAa,MACnE,CAACyC,OAAO,IAAKA,OAAO,KAAKrE,OAAO,IAAI,CAACA,OAAO,CAACuE,QAAQ,CAACF,OAAO,CAAE,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,uBAAuBA,CAAClB,CAAC,EAAEmB,MAAM,EAAE;EACxC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,IAAI,GAAG,CAAC,CAAC;EACf,KAAK,MAAMC,QAAQ,IAAIrB,CAAC,EAAE;IACtB,IAAIqB,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,QAAQ,EAAE;MACpD;IACJ;IACA,MAAMC,GAAG,GAAGD,QAAQ;IACpB;IACA,MAAME,KAAK,GAAGvB,CAAC,CAACsB,GAAG,CAAC;IACpB,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE;MAC7B;IACJ;IACA;IACA;IACAH,IAAI,CAACE,GAAG,CAAC,GAAGC,KAAK;EACrB;EACA,IAAIvB,CAAC,CAACc,IAAI,KAAK3D,SAAS,CAACY,SAAS,EAAE;IAChCqD,IAAI,CAAC,MAAM,CAAC,GAAGjE,SAAS,CAACc,UAAU;EACvC,CAAC,MACI,IAAI+B,CAAC,CAACc,IAAI,KAAK3D,SAAS,CAACa,QAAQ,EAAE;IACpCoD,IAAI,CAAC,MAAM,CAAC,GAAGjE,SAAS,CAACe,UAAU;EACvC,CAAC,MACI,IAAI8B,CAAC,CAACc,IAAI,KAAK3D,SAAS,CAACgB,WAAW,EAAE;IACvCiD,IAAI,CAAC,MAAM,CAAC,GAAGjE,SAAS,CAACkB,YAAY;EACzC,CAAC,MACI;IACD+C,IAAI,CAAC,MAAM,CAAC,GAAGjE,SAAS,CAACmB,YAAY;EACzC;EACA8C,IAAI,CAAC,QAAQ,CAAC,GAAGA,IAAI,CAAC,YAAY,CAAC,GAAGD,MAAM;EAC5CC,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK;EACvBA,IAAI,CAAC,gBAAgB,CAAC,GAAGpB,CAAC;EAC1B,OAAOoB,IAAI;AACf;;AAEA;AACA;AACA;AACA,MAAMI,KAAK,GAAG,OAAOrB,SAAS,KAAK,WAAW,IAAI,kBAAkB,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;AAC9F;AACA;AACA;AACA;AACA;AACA,MAAMoB,sBAAsB,CAAC;EACzB/E,OAAO;EACP;AACJ;AACA;AACA;AACA;EACIgF,YAAY,GAAG,EAAE;EACjB;AACJ;AACA;EACIC,WAAWA,CAACjF,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI6C,gBAAgBA,CAACN,SAAS,EAAE2C,UAAU,EAAEnC,OAAO,EAAE;IAC7C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI+B,KAAK,EAAE;MACP,IAAI,CAAC9E,OAAO,CAACmF,KAAK,CAACC,MAAM,GAAG,SAAS;IACzC;IACA,IAAI,CAACJ,YAAY,CAACK,IAAI,CAACxC,gBAAgB,CAAC,IAAI,CAAC7C,OAAO,EAAEuC,SAAS,EAAE2C,UAAU,CAAC,IAAI,CAAClF,OAAO,CAAC,EAAE+C,OAAO,CAAC,CAAC;EACxG;EACA;AACJ;AACA;EACIuC,OAAOA,CAAA,EAAG;IACN,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACP,YAAY,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/CrC,mBAAmB,CAAC,IAAI,CAAClD,OAAO,EAAE,IAAI,CAACgF,YAAY,CAACO,CAAC,CAAC,CAAC;IAC3D;IACA,IAAI,CAACP,YAAY,GAAG,EAAE;EAC1B;AACJ;AAEA,MAAMS,IAAI,GAAG;EACT;AACJ;AACA;AACA;EACIC,sBAAsB,EAAE;AAC5B,CAAC;;AAED;AACA,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC7B,OAAOA,SAAS,CAACrD,SAAS;AAC9B;AACA;AACA,SAASsD,YAAYA,CAACD,SAAS,EAAErD,SAAS,EAAE;EACxCqD,SAAS,CAACrD,SAAS,GAAGA,SAAS;AACnC;AACA;AACA,SAASuD,QAAQA,CAACF,SAAS,EAAE;EACzB,OAAOA,SAAS,CAACG,KAAK;AAC1B;AACA;AACA,SAASC,QAAQA,CAACJ,SAAS,EAAEG,KAAK,EAAE;EAChCH,SAAS,CAACG,KAAK,GAAGA,KAAK;AAC3B;AACA;AACA,SAASE,gBAAgBA,CAACL,SAAS,EAAE;EACjC,OAAOA,SAAS,CAACM,aAAa;AAClC;AACA;AACA,SAASC,gBAAgBA,CAACP,SAAS,EAAEM,aAAa,EAAE;EAChDN,SAAS,CAACM,aAAa,GAAGA,aAAa;AAC3C;AACA;AACA,SAASE,YAAYA,CAACR,SAAS,EAAE;EAC7B,OAAOA,SAAS,CAACS,GAAG;AACxB;AACA;AACA,SAASC,YAAYA,CAACV,SAAS,EAAEW,SAAS,EAAE;EACxCX,SAAS,CAACS,GAAG,GAAGE,SAAS;AAC7B;AACA;AACA,SAASC,YAAYA,CAACZ,SAAS,EAAE;EAC7B,OAAOA,SAAS,CAACa,SAAS;AAC9B;AACA;AACA,SAASC,YAAYA,CAACd,SAAS,EAAEe,SAAS,EAAE;EACxCf,SAAS,CAACa,SAAS,GAAGE,SAAS;AACnC;AACA;AACA,SAASC,SAASA,CAAChB,SAAS,EAAE;EAC1B,OAAOA,SAAS,CAACiB,GAAG;AACxB;AACA;AACA,SAASC,SAASA,CAAClB,SAAS,EAAEmB,UAAU,EAAEC,aAAa,EAAE;EACrDpB,SAAS,CAACiB,GAAG,GAAG,CAACE,UAAU,EAAEC,aAAa,CAAC;AAC/C;AACA;AACA,SAASC,WAAWA,CAACrB,SAAS,EAAE;EAC5BA,SAAS,CAACiB,GAAG,GAAGK,SAAS;AAC7B;AACA;AACA,SAASC,gBAAgBA,CAACC,UAAU,EAAE;EAClC,OAAOA,UAAU,CAAC,CAAC,CAAC;AACxB;AACA;AACA,SAASC,WAAWA,CAACzB,SAAS,EAAE;EAC5B,OAAOA,SAAS,CAAC0B,IAAI;AACzB;AACA;AACA,SAASC,WAAWA,CAAC3B,SAAS,EAAE4B,MAAM,EAAE;EACpC5B,SAAS,CAAC0B,IAAI,GAAGE,MAAM;AAC3B;AACA;AACA,SAASC,WAAWA,CAAC7B,SAAS,EAAE;EAC5B,OAAOA,SAAS,CAAC8B,GAAG;AACxB;AACA;AACA,SAASC,WAAWA,CAAC/B,SAAS,EAAEgC,QAAQ,EAAE;EACtChC,SAAS,CAAC8B,GAAG,GAAGE,QAAQ;AAC5B;AACA;AACA,SAASC,cAAcA,CAACjC,SAAS,EAAE;EAC/B,OAAO;IACHrD,SAAS,EAAEqD,SAAS,CAACrD,SAAS;IAC9BwD,KAAK,EAAEH,SAAS,CAACG,KAAK;IACtBG,aAAa,EAAEN,SAAS,CAACM,aAAa;IACtCG,GAAG,EAAET,SAAS,CAACS,GAAG;IAClBQ,GAAG,EAAEjB,SAAS,CAACiB,GAAG;IAClBJ,SAAS,EAAEb,SAAS,CAACa,SAAS;IAC9Ba,IAAI,EAAE1B,SAAS,CAAC0B,IAAI;IACpBQ,KAAK,EAAElC,SAAS,CAACkC,KAAK;IACtBJ,GAAG,EAAE9B,SAAS,CAAC8B;EACnB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,6BAA6BA,CAACxF,SAAS,EAAEwD,KAAK,EAAEG,aAAa,EAAEK,SAAS,EAAEI,SAAS,EAAEqB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE;EAC1H,OAAO;IACH3F,SAAS;IACTwD,KAAK;IACLG,aAAa;IACbG,GAAG,EAAEE,SAAS;IACdE,SAAS,EAAEE,SAAS;IACpBE,GAAG,EAAEmB,MAAM;IACXV,IAAI,EAAEW,QAAQ;IACdH,KAAK,EAAEI;EACX,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBvC,SAAS;EACTX,WAAWA,CAACW,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAD,YAAYA,CAAA,EAAG;IACX,OAAOA,YAAY,CAAC,IAAI,CAACC,SAAS,CAAC;EACvC;EACAC,YAAYA,CAACtD,SAAS,EAAE;IACpBsD,YAAY,CAAC,IAAI,CAACD,SAAS,EAAErD,SAAS,CAAC;EAC3C;EACAuD,QAAQA,CAAA,EAAG;IACP,OAAOA,QAAQ,CAAC,IAAI,CAACF,SAAS,CAAC;EACnC;EACAI,QAAQA,CAACD,KAAK,EAAE;IACZC,QAAQ,CAAC,IAAI,CAACJ,SAAS,EAAEG,KAAK,CAAC;EACnC;EACAE,gBAAgBA,CAAA,EAAG;IACf,OAAOA,gBAAgB,CAAC,IAAI,CAACL,SAAS,CAAC;EAC3C;EACAO,gBAAgBA,CAACD,aAAa,EAAE;IAC5BC,gBAAgB,CAAC,IAAI,CAACP,SAAS,EAAEM,aAAa,CAAC;EACnD;EACAE,YAAYA,CAAA,EAAG;IACX,OAAOA,YAAY,CAAC,IAAI,CAACR,SAAS,CAAC;EACvC;EACAU,YAAYA,CAACC,SAAS,EAAE;IACpBD,YAAY,CAAC,IAAI,CAACV,SAAS,EAAEW,SAAS,CAAC;EAC3C;EACAC,YAAYA,CAAA,EAAG;IACX,OAAOA,YAAY,CAAC,IAAI,CAACZ,SAAS,CAAC;EACvC;EACAc,YAAYA,CAACC,SAAS,EAAE;IACpBD,YAAY,CAAC,IAAI,CAACd,SAAS,EAAEe,SAAS,CAAC;EAC3C;EACAC,SAASA,CAAA,EAAG;IACR,MAAMoB,MAAM,GAAGpB,SAAS,CAAC,IAAI,CAAChB,SAAS,CAAC;IACxC,IAAI,CAACoC,MAAM,EACP,OAAOd,SAAS;IACpB,OAAO;MACHkB,IAAI,EAAEJ,MAAM,CAAC,CAAC,CAAC;MACfhI,OAAO,EAAEgI,MAAM,CAAC,CAAC;IACrB,CAAC;EACL;EACAlB,SAASA,CAACkB,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,EAAE;MACTf,WAAW,CAAC,IAAI,CAACrB,SAAS,CAAC;MAC3B;IACJ;IACAkB,SAAS,CAAC,IAAI,CAAClB,SAAS,EAAEoC,MAAM,CAACI,IAAI,EAAEJ,MAAM,CAAChI,OAAO,CAAC;EAC1D;EACAqH,WAAWA,CAAA,EAAG;IACV,OAAOA,WAAW,CAAC,IAAI,CAACzB,SAAS,CAAC;EACtC;EACA2B,WAAWA,CAACC,MAAM,EAAE;IAChBD,WAAW,CAAC,IAAI,CAAC3B,SAAS,EAAE4B,MAAM,CAAC;EACvC;EACAC,WAAWA,CAAA,EAAG;IACV,OAAOA,WAAW,CAAC,IAAI,CAAC7B,SAAS,CAAC;EACtC;EACA+B,WAAWA,CAACC,QAAQ,EAAE;IAClBD,WAAW,CAAC,IAAI,CAAC/B,SAAS,EAAEgC,QAAQ,CAAC;EACzC;EACAS,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIF,gBAAgB,CAACN,cAAc,CAAC,IAAI,CAACjC,SAAS,CAAC,CAAC;EAC/D;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM0C,gBAAgB,GAAG,CAAC,CAAC;AAC3B;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,SAAS;AAClC;AACA,MAAMC,kBAAkB,GAAG/H,SAAS,CAACC,KAAK;AAC1C;AACA,MAAM+H,cAAc,CAAC;EACjBC,gBAAgB,GAAG,KAAK;EACxBC,eAAe,GAAG,IAAI;EACtBC,0BAA0B;EAC1BC,2BAA2B,GAAG3B,SAAS;EACvC4B,0BAA0B,GAAG5B,SAAS;EACtC6B,uBAAuB,GAAG7B,SAAS;EACnCjC,WAAWA,CAAC;IAAE2D,0BAA0B,GAAG,KAAK;IAAED,eAAe,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC9E,IAAI,CAACC,0BAA0B,GAAGA,0BAA0B;IAC5D,IAAI,CAACD,eAAe,GAAGA,eAAe;EAC1C;EACAK,gBAAgBA,CAACpD,SAAS,EAAE;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC+C,eAAe,IACpBhD,YAAY,CAACC,SAAS,CAAC,KAAKnF,SAAS,CAACC,KAAK,IAC3CqD,oBAAoB,CAAC+B,QAAQ,CAACF,SAAS,CAAC,CAAC,EAAE;MAC3CC,YAAY,CAACD,SAAS,EAAEnF,SAAS,CAACE,QAAQ,CAAC;IAC/C,CAAC,MACI,IAAI,IAAI,CAAC+H,gBAAgB,EAAE;MAC5B,IAAI,CAACG,2BAA2B,CAACjD,SAAS,CAAC;IAC/C;EACJ;EACAqD,aAAaA,CAACrD,SAAS,EAAE;IACrB,IAAI6B,WAAW,CAAC7B,SAAS,CAAC,EAAE;MACxB;IACJ;IACA,IAAI,CAACsD,cAAc,CAACtD,SAAS,EAAEK,gBAAgB,CAACL,SAAS,CAAC,CAAC;IAC3D+B,WAAW,CAAC/B,SAAS,EAAE,IAAI,CAAC;EAChC;EACAuD,mBAAmBA,CAACvD,SAAS,EAAE;IAC3B,MAAMoC,MAAM,GAAGpB,SAAS,CAAChB,SAAS,CAAC;IACnC,MAAMoB,aAAa,GAAGgB,MAAM,IAAIb,gBAAgB,CAACa,MAAM,CAAC;IACxDf,WAAW,CAACrB,SAAS,CAAC;IACtB,MAAMwD,UAAU,GAAGpC,aAAa,IAAI,IAAI,CAACqC,aAAa,CAACrC,aAAa,CAAC;IACrE,IAAI,CAACoC,UAAU,EAAE;MACb;IACJ;IACA,IAAI,CAACF,cAAc,CAACtD,SAAS,EAAEwD,UAAU,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,cAAcA,CAACtD,SAAS,EAAE0D,aAAa,EAAE;IACrC,IAAItC,aAAa,GAAGsC,aAAa;IACjC,OAAOtC,aAAa,IAAIA,aAAa,KAAKZ,YAAY,CAACR,SAAS,CAAC,EAAE;MAC/D,IAAIoB,aAAa,CAACuC,QAAQ,KAAKC,IAAI,CAACC,YAAY,EAAE;QAC9C,IAAI,CAACC,uBAAuB,CAAC1C,aAAa,EAAEpB,SAAS,CAAC;MAC1D;MACA,IAAIgB,SAAS,CAAChB,SAAS,CAAC,EAAE;QACtB;QACA;QACA;QACA;MACJ;MACAoB,aAAa,GAAG,IAAI,CAACqC,aAAa,CAACrC,aAAa,CAAC;IACrD;IACA,MAAMgB,MAAM,GAAGpB,SAAS,CAAChB,SAAS,CAAC;IACnC,IAAI,CAACoC,MAAM,EAAE;MACT;MACA;IACJ;IACA,IAAI,IAAI,CAACU,gBAAgB,EAAE;MACvB,IAAI,CAACI,0BAA0B,CAAClD,SAAS,CAAC;IAC9C;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACgD,0BAA0B,EAAE;MACjC,IAAIjD,YAAY,CAACC,SAAS,CAAC,KAAKnF,SAAS,CAACc,UAAU,IAChDoE,YAAY,CAACC,SAAS,CAAC,KAAKnF,SAAS,CAACe,UAAU,IAChDmE,YAAY,CAACC,SAAS,CAAC,KAAKnF,SAAS,CAACkB,YAAY,IAClDgE,YAAY,CAACC,SAAS,CAAC,KAAKnF,SAAS,CAACmB,YAAY,EAAE;QACpD;QACA;QACA;QACA,IAAIuC,mBAAmB,CAAC2B,QAAQ,CAACF,SAAS,CAAC,EAAED,YAAY,CAACC,SAAS,CAAC,EAAEuB,gBAAgB,CAACa,MAAM,CAAC,CAAC,EAAE;UAC7F;UACA;UACA;UACA;UACA;UACA,MAAM2B,WAAW,GAAGnF,uBAAuB,CAACsB,QAAQ,CAACF,SAAS,CAAC,EAAEuB,gBAAgB,CAACa,MAAM,CAAC,CAAC;UAC1FhC,QAAQ,CAACJ,SAAS,EAAE+D,WAAW,CAAC;UAChC;UACA;UACA;UACAxD,gBAAgB,CAACP,SAAS,EAAEuB,gBAAgB,CAACa,MAAM,CAAC,CAAC;QACzD,CAAC,MACI;UACDf,WAAW,CAACrB,SAAS,CAAC;QAC1B;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIyD,aAAaA,CAACrJ,OAAO,EAAE;IACnB,MAAM4J,KAAK,GAAG5J,OAAO,CAACL,QAAQ,CAACE,KAAK,CAAC;IACrC,IAAI+J,KAAK,EAAE;MACP,OAAOA,KAAK;IAChB;IACA,MAAMR,UAAU,GAAGpJ,OAAO,CAACoJ,UAAU;IACrC,IAAIA,UAAU,EAAES,QAAQ,KAAK,oBAAoB,EAAE;MAC/C,OAAOT,UAAU,EAAEU,IAAI,IAAI,IAAI;IACnC;IACA,OAAOV,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIM,uBAAuBA,CAAC1C,aAAa,EAAEpB,SAAS,EAAE;IAC9C,MAAMxF,SAAS,GAAG,IAAI,CAAC2J,YAAY,CAAC/C,aAAa,CAAC;IAClD,MAAMD,UAAU,GAAG3G,SAAS,CAACuF,YAAY,CAACC,SAAS,CAAC,CAAC;IACrD,IAAImB,UAAU,KAAKG,SAAS,EAAE;MAC1BJ,SAAS,CAAClB,SAAS,EAAEmB,UAAU,EAAEC,aAAa,CAAC;IACnD;IACA,IAAI,IAAI,CAAC0B,gBAAgB,EAAE;MACvB,IAAI,CAACK,uBAAuB,CAAC/B,aAAa,EAAEpB,SAAS,EAAExF,SAAS,CAAC;IACrE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI2J,YAAYA,CAAC/C,aAAa,EAAE;IACxB,IAAI5G,SAAS,GAAGL,GAAG,CAACiH,aAAa,CAAC;IAClC,IAAI,CAAC5G,SAAS,EAAE;MACZ,MAAM4J,iBAAiB,GAAGhD,aAAa,CAACiD,YAAY,CAACvK,SAAS,CAACE,QAAQ,CAAC;MACxE,IAAI,CAACoK,iBAAiB,EAAE;QACpB5J,SAAS,GAAGkI,gBAAgB;QAC5BnI,GAAG,CAAC6G,aAAa,EAAE5G,SAAS,CAAC;MACjC,CAAC,MACI;QACDA,SAAS,GAAGC,SAAS,CAAC2J,iBAAiB,CAAC;QACxC,IAAI,CAAC5J,SAAS,EAAE;UACZA,SAAS,GAAG,CAAC,CAAC;UACd,MAAM8J,MAAM,GAAGF,iBAAiB,CAACG,KAAK,CAAC5B,gBAAgB,CAAC;UACxD,KAAK,IAAI6B,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,MAAM,CAAC1E,MAAM,EAAE4E,GAAG,EAAE,EAAE;YAC1C,MAAMvF,KAAK,GAAGqF,MAAM,CAACE,GAAG,CAAC;YACzB,IAAI,CAACvF,KAAK,EAAE;cACR;YACJ;YACA,MAAMwF,KAAK,GAAGxF,KAAK,CAACrC,OAAO,CAACiD,IAAI,CAACC,sBAAsB,CAAC;YACxD,MAAM4E,QAAQ,GAAGD,KAAK,KAAK,CAAC,CAAC;YAC7B,MAAMjG,IAAI,GAAGkG,QAAQ,GAAGzF,KAAK,CAAC0F,MAAM,CAAC,CAAC,EAAEF,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC,GAAGhC,kBAAkB;YAC1E,MAAMR,MAAM,GAAGsC,QAAQ,GAAGzF,KAAK,CAAC0F,MAAM,CAACF,KAAK,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG3F,KAAK;YAChEzE,SAAS,CAACgE,IAAI,CAAC,GAAG4D,MAAM;UAC5B;UACAzH,SAAS,CAACyJ,iBAAiB,EAAE5J,SAAS,CAAC;QAC3C;QACAD,GAAG,CAAC6G,aAAa,EAAE5G,SAAS,CAAC;MACjC;IACJ;IACA,OAAOA,SAAS;EACpB;EACAqK,mBAAmBA,CAAC5B,2BAA2B,EAAEC,0BAA0B,EAAEC,uBAAuB,EAAE;IAClG,IAAI,CAACL,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACG,2BAA2B,GAAGA,2BAA2B;IAC9D,IAAI,CAACC,0BAA0B,GAAGA,0BAA0B;IAC5D,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;EAC1D;AACJ;;AAEA;AACA;AACA;AACA,IAAI2B,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpBA,WAAW,CAACA,WAAW,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC,GAAG,6BAA6B;AAC/F,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,gBAAgB;EAChB;EACAC,cAAc;EACd;EACAC,aAAa;EACb;EACAC,oBAAoB,GAAG,KAAK;EAC5B;EACAC,uBAAuB,GAAG,EAAE;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;EACI/F,WAAWA,CAAC2F,gBAAgB,EAAE;IAAEC,cAAc;IAAEC;EAAe,CAAC,GAAG,CAAC,CAAC,EAAE;IACnE,IAAI,CAACF,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,QAAQA,CAACrF,SAAS,EAAE;IAChB,MAAMsF,gBAAgB,GAAG,IAAI/C,gBAAgB,CAACvC,SAAS,CAAC;IACxD,IAAI,CAACiF,cAAc,EAAE7B,gBAAgB,CAACpD,SAAS,CAAC;IAChD,IAAI,CAACiF,cAAc,EAAE5B,aAAa,CAACrD,SAAS,CAAC;IAC7C,MAAMoC,MAAM,GAAGkD,gBAAgB,CAACtE,SAAS,CAAC,CAAC;IAC3C,IAAIoB,MAAM,IAAImD,qCAAqC,CAACnD,MAAM,CAAChI,OAAO,EAAEkL,gBAAgB,CAAC,EAAE;MACnF7H,cAAc,CAAC6H,gBAAgB,CAACpF,QAAQ,CAAC,CAAC,CAAC;IAC/C;IACA,IAAI,IAAI,CAACgF,aAAa,IAAII,gBAAgB,CAAC7D,WAAW,CAAC,CAAC,EAAE;MACtD,IAAI,CAAC+D,8BAA8B,CAACF,gBAAgB,CAAC;MACrD;IACJ;IACA,IAAI,CAACN,gBAAgB,CAACM,gBAAgB,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACIE,8BAA8BA,CAACF,gBAAgB,EAAE;IAC7C,IAAI,CAACF,uBAAuB,CAAC3F,IAAI,CAAC6F,gBAAgB,CAAC;IACnD,IAAI,IAAI,CAACH,oBAAoB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACA,oBAAoB,GAAG,IAAI;IAChCM,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,CAACR,oBAAoB,GAAG,KAAK;MACjC,IAAI,CAACD,aAAa,CAAC,IAAI,CAACE,uBAAuB,CAAC;IACpD,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA,SAASG,qCAAqCA,CAACnE,aAAa,EAAEkE,gBAAgB,EAAE;EAC5E;EACA;EACA;EACA;EACA,OAAQlE,aAAa,CAACwE,OAAO,KAAK,GAAG,KAChCN,gBAAgB,CAACvF,YAAY,CAAC,CAAC,KAAKlF,SAAS,CAACC,KAAK,IAChDwK,gBAAgB,CAACvF,YAAY,CAAC,CAAC,KAAKlF,SAAS,CAACE,QAAQ,CAAC;AACnE;;AAEA;AACA,MAAM8K,0BAA0B,GAChC,eAAgBC,MAAM,CAACC,GAAG,CAAC,oBAAoB,CAAC;AAChD;AACA,MAAMC,UAAU,GAAG;EACfC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,qCAAqC,GAAG,sFAAsF,GAChI,yFAAyF,GACzF,2CAA2C;AAC/C,MAAMC,6BAA6B,GAAG,gDAAgD;AACtF,MAAMC,mCAAmC,GAAG,6CAA6C,GACrF,wFAAwF,GACxF,wEAAwE;AAC5E,MAAMC,2BAA2B,GAAG,8CAA8C;AAClF;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBtB,gBAAgB;EAChBjC,eAAe;EACfkC,cAAc;EACdsB,UAAU;EACVlH,WAAWA,CAAC2F,gBAAgB,EAAEjC,eAAe,GAAG,IAAI,EAAE;IAClD,IAAI,CAACiC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACjC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACkC,cAAc,GAAG,IAAIpC,cAAc,CAAC;MAAEE;IAAgB,CAAC,CAAC;IAC7D,IAAI,CAACwD,UAAU,GAAG,IAAIxB,UAAU,CAAEO,gBAAgB,IAAK;MACnD,IAAI,CAACkB,kBAAkB,CAAClB,gBAAgB,CAAC;IAC7C,CAAC,EAAE;MACCL,cAAc,EAAE,IAAI,CAACA;IACzB,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACII,QAAQA,CAACrF,SAAS,EAAE;IAChB,IAAI,CAACuG,UAAU,CAAClB,QAAQ,CAACrF,SAAS,CAAC;EACvC;EACA;EACAwG,kBAAkBA,CAAClB,gBAAgB,EAAE;IACjC,IAAIA,gBAAgB,CAAC7D,WAAW,CAAC,CAAC,EAAE;MAChCgF,qBAAqB,CAACnB,gBAAgB,CAAC;IAC3C;IACAoB,uBAAuB,CAACpB,gBAAgB,CAAC;IACzC,OAAOA,gBAAgB,CAACtE,SAAS,CAAC,CAAC,EAAE;MACjC2F,uBAAuB,CAACrB,gBAAgB,CAAC;MACzC;MACA,IAAI5I,kBAAkB,CAAC4I,gBAAgB,CAACvF,YAAY,CAAC,CAAC,CAAC,IACnDuF,gBAAgB,CAACtE,SAAS,CAAC,CAAC,CAAC5G,OAAO,KAAKkL,gBAAgB,CAACjF,gBAAgB,CAAC,CAAC,EAAE;QAC9E;MACJ;MACA,IAAI,CAAC2E,gBAAgB,CAACM,gBAAgB,CAACpF,QAAQ,CAAC,CAAC,EAAEoF,gBAAgB,CAACtE,SAAS,CAAC,CAAC,CAACwB,IAAI,CAAC;MACrF,IAAIoE,kBAAkB,CAACtB,gBAAgB,CAAC,EAAE;QACtC;MACJ;MACA,IAAI,CAACL,cAAc,CAAC1B,mBAAmB,CAAC+B,gBAAgB,CAACtF,SAAS,CAAC;IACvE;EACJ;AACJ;AACA,SAAS0G,uBAAuBA,CAACpB,gBAAgB,EAAE;EAC/C,MAAMnF,KAAK,GAAGmF,gBAAgB,CAACpF,QAAQ,CAAC,CAAC;EACzC,MAAM2G,uBAAuB,GAAGvB,gBAAgB,CAACpF,QAAQ,CAAC,CAAC,CAAC4G,eAAe,CAACC,IAAI,CAAC5G,KAAK,CAAC;EACvF,MAAM2G,eAAe,GAAGA,CAAA,KAAM;IAC1B3G,KAAK,CAAC0F,0BAA0B,CAAC,GAAG,IAAI;IACxCgB,uBAAuB,CAAC,CAAC;EAC7B,CAAC;EACDG,kBAAkB,CAAC7G,KAAK,EAAE,iBAAiB,EAAE2G,eAAe,CAAC;EAC7DE,kBAAkB,CAAC7G,KAAK,EAAE,0BAA0B,EAAE2G,eAAe,CAAC;AAC1E;AACA,SAASF,kBAAkBA,CAACtB,gBAAgB,EAAE;EAC1C,MAAMnF,KAAK,GAAGmF,gBAAgB,CAACpF,QAAQ,CAAC,CAAC;EACzC,OAAO,CAAC,CAACC,KAAK,CAAC0F,0BAA0B,CAAC;AAC9C;AACA,SAASY,qBAAqBA,CAACnB,gBAAgB,EAAE;EAC7C,MAAMnF,KAAK,GAAGmF,gBAAgB,CAACpF,QAAQ,CAAC,CAAC;EACzC,MAAMrB,MAAM,GAAGyG,gBAAgB,CAACjF,gBAAgB,CAAC,CAAC;EAClD,MAAM4G,sBAAsB,GAAG9G,KAAK,CAAC1C,cAAc,CAACsJ,IAAI,CAAC5G,KAAK,CAAC;EAC/D6G,kBAAkB,CAAC7G,KAAK,EAAE,QAAQ,EAAEtB,MAAM,CAAC;EAC3CmI,kBAAkB,CAAC7G,KAAK,EAAE,YAAY,EAAE6F,UAAU,CAACC,MAAM,CAAC;EAC1De,kBAAkB,CAAC7G,KAAK,EAAE,gBAAgB,EAAE,MAAM;IAC9C8G,sBAAsB,CAAC,CAAC;IACxB,MAAM,IAAIC,KAAK,CAACf,6BAA6B,IAAIgB,SAAS,GAAGjB,qCAAqC,GAAG,EAAE,CAAC,CAAC;EAC7G,CAAC,CAAC;EACFc,kBAAkB,CAAC7G,KAAK,EAAE,cAAc,EAAE,MAAM;IAC5C,MAAM,IAAI+G,KAAK,CAACb,2BAA2B,IAAIc,SAAS,GAAGf,mCAAmC,GAAG,EAAE,CAAC,CAAC;EACzG,CAAC,CAAC;AACN;AACA,SAASO,uBAAuBA,CAACrB,gBAAgB,EAAE;EAC/C,MAAMnF,KAAK,GAAGmF,gBAAgB,CAACpF,QAAQ,CAAC,CAAC;EACzC,MAAMwD,aAAa,GAAG4B,gBAAgB,CAACtE,SAAS,CAAC,CAAC,EAAE5G,OAAO;EAC3D,IAAIsJ,aAAa,EAAE;IACfsD,kBAAkB,CAAC7G,KAAK,EAAE,eAAe,EAAEuD,aAAa,EAAE;MACtD;MACA0D,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,kBAAkBA,CAAC7G,KAAK,EAAEpB,QAAQ,EAAEE,KAAK,EAAE;EAAEmI,YAAY,GAAG;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EAC/EC,MAAM,CAACC,cAAc,CAACnH,KAAK,EAAEpB,QAAQ,EAAE;IAAEE,KAAK;IAAEmI;EAAa,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA,SAASG,oBAAoBA,CAACC,aAAa,EAAEjB,UAAU,EAAE;EACrDiB,aAAa,CAACC,IAAI,CAAEzH,SAAS,IAAK;IAC9BuG,UAAU,CAAClB,QAAQ,CAACrF,SAAS,CAAC;EAClC,CAAC,EAAE8E,WAAW,CAAC4C,2BAA2B,CAAC;AAC/C;;AAEA;AACA,SAASC,uBAAuBA,CAAChH,SAAS,EAAE;EACxC,MAAMiH,CAAC,GAAG,EAAE;EACZ,MAAMC,CAAC,GAAI7H,SAAS,IAAK;IACrB4H,CAAC,CAACnI,IAAI,CAACO,SAAS,CAAC;EACrB,CAAC;EACD,MAAM8H,CAAC,GAAI3H,KAAK,IAAK;IACjB0H,CAAC,CAAC1F,6BAA6B,CAAChC,KAAK,CAAC3B,IAAI,EAAE2B,KAAK,EAAEA,KAAK,CAACtB,MAAM,EAAE8B,SAAS,EAAEoH,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5F,CAAC;EACD,OAAO;IACHC,CAAC,EAAEtH,SAAS;IACZiH,CAAC;IACDM,EAAE,EAAE,EAAE;IACNC,GAAG,EAAE,EAAE;IACPN,CAAC;IACDC;EACJ,CAAC;AACL;AACA;AACA,SAASM,SAASA,CAACC,iBAAiB,EAAEC,KAAK,EAAElL,OAAO,EAAE;EAClD,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2I,KAAK,CAAC1I,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,MAAMhD,SAAS,GAAG2L,KAAK,CAAC3I,CAAC,CAAC;IAC1B,MAAM4I,UAAU,GAAGnL,OAAO,GAAGiL,iBAAiB,CAACF,GAAG,GAAGE,iBAAiB,CAACH,EAAE;IACzEK,UAAU,CAAC9I,IAAI,CAAC9C,SAAS,CAAC;IAC1B0L,iBAAiB,CAACJ,CAAC,CAAChL,gBAAgB,CAACN,SAAS,EAAE0L,iBAAiB,CAACP,CAAC,EAAE1K,OAAO,CAAC;EACjF;AACJ;AACA;AACA,SAASoL,mBAAmBA,CAACH,iBAAiB,EAAE;EAC5C,OAAOA,iBAAiB,EAAET,CAAC,IAAI,EAAE;AACrC;AACA;AACA,SAASa,kBAAkBA,CAACJ,iBAAiB,EAAE9B,UAAU,EAAE;EACvD,IAAI,CAAC8B,iBAAiB,EAAE;IACpB;EACJ;EACAA,iBAAiB,CAACR,CAAC,GAAGtB,UAAU;AACpC;AACA;AACA,SAASmC,uBAAuBA,CAACL,iBAAiB,EAAE;EAChD,IAAI,CAACA,iBAAiB,EAAE;IACpB;EACJ;EACAM,oBAAoB,CAACN,iBAAiB,CAACJ,CAAC,EAAEI,iBAAiB,CAACH,EAAE,EAAEG,iBAAiB,CAACP,CAAC,CAAC;EACpFa,oBAAoB,CAACN,iBAAiB,CAACJ,CAAC,EAAEI,iBAAiB,CAACF,GAAG,EAAEE,iBAAiB,CAACP,CAAC,EAAE,IAAI,CAAC;AAC/F;AACA,SAASa,oBAAoBA,CAAChI,SAAS,EAAE4H,UAAU,EAAEK,iBAAiB,EAAExL,OAAO,EAAE;EAC7E,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,UAAU,CAAC3I,MAAM,EAAED,CAAC,EAAE,EAAE;IACxCgB,SAAS,CAACrD,mBAAmB,CAACiL,UAAU,CAAC5I,CAAC,CAAC,EAAEiJ,iBAAiB,EAAE,gBAAiBxL,OAAO,CAAC;EAC7F;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMyL,qBAAqB,GAAG,KAAK;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB,OAAOD,qBAAqB,GAAGA,qBAAqB;EACpDE,gBAAgB;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,aAAa,GAAG,CAAC,CAAC;EAClBC,iCAAiC,GAAG,CAAC,CAAC;EACtC;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1C,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;EACI2C,gBAAgB,GAAG,EAAE;EACrB7J,WAAWA,CAAC0J,gBAAgB,EAAE;IAC1B,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;EAC5C;EACAI,WAAWA,CAACxM,SAAS,EAAEwD,KAAK,EAAEQ,SAAS,EAAE;IACrC,MAAMX,SAAS,GAAGmC,6BAA6B,CAC/C,gBAAiBxF,SAAS,EAC1B,YAAawD,KAAK,EAClB,oBAAqBA,KAAK,CAACtB,MAAM,EACjC,gBAAiB8B,SAAS,EAC1B,gBAAiBoH,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACoB,eAAe,CAACpJ,SAAS,CAAC;EACnC;EACA;AACJ;AACA;EACIoJ,eAAeA,CAACpJ,SAAS,EAAE;IACvB,IAAI,CAAC,IAAI,CAACuG,UAAU,EAAE;MAClB;MACA5E,WAAW,CAAC3B,SAAS,EAAE,IAAI,CAAC;MAC5B,IAAI,CAACkJ,gBAAgB,EAAEzJ,IAAI,CAACO,SAAS,CAAC;MACtC;IACJ;IACA,IAAI,CAACuG,UAAU,CAACvG,SAAS,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIqJ,QAAQA,CAAC1M,SAAS,EAAE2M,iBAAiB,EAAEnM,OAAO,EAAE;IAC5C,IAAIR,SAAS,IAAI,IAAI,CAACqM,aAAa,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC3D;IACJ;IACA,IAAI,CAACD,aAAa,CAACD,qBAAqB,IAAItM,yBAAyB,CAACK,OAAO,CAACD,SAAS,CAAC,IAAI,CAAC,EAAE;MAC3F;IACJ;IACA,MAAM4M,YAAY,GAAGA,CAAC5M,SAAS,EAAEwD,KAAK,EAAEQ,SAAS,KAAK;MAClD,IAAI,CAACwI,WAAW,CAACxM,SAAS,EAAEwD,KAAK,EAAEQ,SAAS,CAAC;IACjD,CAAC;IACD;IACA,IAAI,CAACqI,aAAa,CAACrM,SAAS,CAAC,GAAG4M,YAAY;IAC5C,MAAMC,gBAAgB,GAAGxM,mBAAmB,CAACsM,iBAAiB,IAAI3M,SAAS,CAAC;IAC5E,IAAI6M,gBAAgB,KAAK7M,SAAS,EAAE;MAChC,MAAM4L,UAAU,GAAG,IAAI,CAACU,iCAAiC,CAACO,gBAAgB,CAAC,IAAI,EAAE;MACjFjB,UAAU,CAAC9I,IAAI,CAAC9C,SAAS,CAAC;MAC1B,IAAI,CAACsM,iCAAiC,CAACO,gBAAgB,CAAC,GAAGjB,UAAU;IACzE;IACA,IAAI,CAACQ,gBAAgB,CAAC9L,gBAAgB,CAACuM,gBAAgB,EAAGpP,OAAO,IAAK;MAClE,OAAQ+F,KAAK,IAAK;QACdoJ,YAAY,CAAC5M,SAAS,EAAEwD,KAAK,EAAE/F,OAAO,CAAC;MAC3C,CAAC;IACL,CAAC,EAAE+C,OAAO,CAAC;EACf;EACA;AACJ;AACA;AACA;AACA;EACIsM,iBAAiBA,CAACpB,iBAAiB,GAAGqB,MAAM,CAACC,KAAK,EAAE;IAChD;IACA;IACA,IAAI,CAACtB,iBAAiB,EAAE;MACpB;IACJ;IACA;IACA,IAAI,CAACuB,qBAAqB,CAACvB,iBAAiB,CAACT,CAAC,CAAC;IAC/C;IACAc,uBAAuB,CAACL,iBAAiB,CAAC;IAC1C,OAAOqB,MAAM,CAACC,KAAK;EACvB;EACA;AACJ;AACA;AACA;EACIC,qBAAqBA,CAACC,eAAe,EAAE;IACnC,KAAK,IAAIlK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkK,eAAe,CAACjK,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMmK,cAAc,GAAGD,eAAe,CAAClK,CAAC,CAAC;MACzC,MAAM4I,UAAU,GAAG,IAAI,CAACwB,gCAAgC,CAACD,cAAc,CAACnN,SAAS,CAAC;MAClF,KAAK,IAAIqN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,UAAU,CAAC3I,MAAM,EAAEoK,CAAC,EAAE,EAAE;QACxC,MAAMhK,SAAS,GAAGiC,cAAc,CAAC6H,cAAc,CAAC;QAChD;QACA;QACA7J,YAAY,CAACD,SAAS,EAAEuI,UAAU,CAACyB,CAAC,CAAC,CAAC;QACtC,IAAI,CAACZ,eAAe,CAACpJ,SAAS,CAAC;MACnC;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACI+J,gCAAgCA,CAACP,gBAAgB,EAAE;IAC/C,MAAMjB,UAAU,GAAG,EAAE;IACrB,IAAI,IAAI,CAACS,aAAa,CAACQ,gBAAgB,CAAC,EAAE;MACtCjB,UAAU,CAAC9I,IAAI,CAAC+J,gBAAgB,CAAC;IACrC;IACA,IAAI,IAAI,CAACP,iCAAiC,CAACO,gBAAgB,CAAC,EAAE;MAC1DjB,UAAU,CAAC9I,IAAI,CAAC,GAAG,IAAI,CAACwJ,iCAAiC,CAACO,gBAAgB,CAAC,CAAC;IAChF;IACA,OAAOjB,UAAU;EACrB;EACA;AACJ;AACA;EACIrL,OAAOA,CAACP,SAAS,EAAE;IACf,OAAO,IAAI,CAACqM,aAAa,CAACrM,SAAS,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;EACI+C,OAAOA,CAAA,EAAG;IACN,IAAI,CAACqJ,gBAAgB,EAAErJ,OAAO,CAAC,CAAC;IAChC,IAAI,CAACqJ,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,iCAAiC,GAAG,CAAC,CAAC;IAC3C,IAAI,CAAC1C,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC2C,gBAAgB,GAAG,EAAE;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIT,kBAAkBA,CAAClC,UAAU,EAAE0D,WAAW,EAAE;IACxC,IAAI,CAACxC,IAAI,CAAClB,UAAU,EAAE0D,WAAW,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIxC,IAAIA,CAAClB,UAAU,EAAE0D,WAAW,EAAE;IAC1B,IAAI,CAAC1D,UAAU,GAAGA,UAAU;IAC5B,IAAI,IAAI,CAAC2C,gBAAgB,EAAEtJ,MAAM,EAAE;MAC/B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACuJ,gBAAgB,CAACtJ,MAAM,EAAED,CAAC,EAAE,EAAE;QACnD,IAAI,CAACyJ,eAAe,CAAC,IAAI,CAACF,gBAAgB,CAACvJ,CAAC,CAAC,CAAC;MAClD;MACA,IAAI,CAACuJ,gBAAgB,GAAG,IAAI;IAChC;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASgB,oCAAoCA,CAACvJ,SAAS,EAAEwJ,KAAK,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,aAAa,GAAGZ,MAAM,EAAE;EACzH,MAAMrB,iBAAiB,GAAGV,uBAAuB,CAAChH,SAAS,CAAC;EAC5D,IAAI,CAAC2J,aAAa,CAACC,MAAM,EAAE;IACvBD,aAAa,CAACC,MAAM,GAAG,CAAC,CAAC;EAC7B;EACAD,aAAa,CAACC,MAAM,CAACJ,KAAK,CAAC,GAAG9B,iBAAiB;EAC/CD,SAAS,CAACC,iBAAiB,EAAE+B,gBAAgB,CAAC;EAC9ChC,SAAS,CAACC,iBAAiB,EAAEgC,iBAAiB,EAAE,cAAe,IAAI,CAAC;AACxE;AACA;AACA,SAASG,4BAA4BA,CAACL,KAAK,EAAEG,aAAa,GAAGZ,MAAM,EAAE;EACjE,OAAOlB,mBAAmB,CAAC8B,aAAa,CAACC,MAAM,GAAGJ,KAAK,CAAC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA,SAASM,2BAA2BA,CAACR,WAAW,EAAEE,KAAK,EAAE5D,UAAU,EAAE+D,aAAa,GAAGZ,MAAM,EAAE;EACzFjB,kBAAkB,CAAC6B,aAAa,CAACC,MAAM,GAAGJ,KAAK,CAAC,EAAE5D,UAAU,CAAC;AACjE;AACA;AACA,SAASmE,gCAAgCA,CAACP,KAAK,EAAEG,aAAa,GAAGZ,MAAM,EAAE;EACrEhB,uBAAuB,CAAC4B,aAAa,CAACC,MAAM,GAAGJ,KAAK,CAAC,CAAC;AAC1D;AACA;AACA,SAASQ,gCAAgCA,CAACR,KAAK,EAAEG,aAAa,GAAGZ,MAAM,EAAE;EACrE,IAAI,CAACY,aAAa,CAACC,MAAM,EAAE;IACvB;EACJ;EACAD,aAAa,CAACC,MAAM,CAACJ,KAAK,CAAC,GAAG7I,SAAS;AAC3C;AAEA,SAASxH,SAAS,EAAEgP,aAAa,EAAE3J,sBAAsB,EAAEmH,eAAe,EAAE/D,gBAAgB,EAAEyD,UAAU,EAAEkE,oCAAoC,EAAES,gCAAgC,EAAEtQ,YAAY,IAAIuQ,cAAc,EAAEJ,4BAA4B,EAAE9N,kBAAkB,EAAEK,gBAAgB,EAAE0N,2BAA2B,EAAElD,oBAAoB,IAAIkB,kBAAkB,EAAEiC,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}