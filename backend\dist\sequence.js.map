{"version": 3, "file": "sequence.js", "sourceRoot": "", "sources": ["../src/sequence.ts"], "names": [], "mappings": ";;;;AAAA,yCAAsC;AACtC,yCASwB;AACxB,6DAGkC;AAKlC,MAAM,eAAe,GAAG,mBAAY,CAAC,eAAe,CAAC;AAErD,IAAa,gBAAgB,GAA7B,MAAa,gBAAgB;IAC3B,YACgD,SAAoB,EAClB,WAAwB,EACvB,MAAoB,EAChC,IAAU,EACR,MAAc,EAE3C,mBAAmC;QANC,cAAS,GAAT,SAAS,CAAW;QAClB,gBAAW,GAAX,WAAW,CAAa;QACvB,WAAM,GAAN,MAAM,CAAc;QAChC,SAAI,GAAJ,IAAI,CAAM;QACR,WAAM,GAAN,MAAM,CAAQ;QAE3C,wBAAmB,GAAnB,mBAAmB,CAAgB;IAC5C,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,OAAuB;QAClC,IAAI,CAAC;YACH,MAAM,EAAC,OAAO,EAAE,QAAQ,EAAC,GAAG,OAAO,CAAC;YAEpC,eAAe;YACf,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;YACtC,MAAM,cAAc,GAAG;gBACrB,uBAAuB;gBACvB,uBAAuB;gBACvB,uBAAuB;gBACvB,OAAO,CAAC,GAAG,CAAC,YAAY;aACzB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAElB,IAAI,MAAM,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC9C,QAAQ,CAAC,SAAS,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;YAC5D,CAAC;iBAAM,IAAI,CAAC,MAAM,EAAE,CAAC;gBACnB,2BAA2B;gBAC3B,QAAQ,CAAC,SAAS,CAAC,6BAA6B,EAAE,uBAAuB,CAAC,CAAC;YAC7E,CAAC;YAED,QAAQ,CAAC,SAAS,CAAC,8BAA8B,EAAE,wCAAwC,CAAC,CAAC;YAC7F,QAAQ,CAAC,SAAS,CAAC,8BAA8B,EAAE,wEAAwE,CAAC,CAAC;YAC7H,QAAQ,CAAC,SAAS,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAC;YAC/D,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,OAAO,CAAC,CAAC;YAEtD,4BAA4B;YAC5B,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACjC,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC;gBAC1B,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACf,OAAO;YACT,CAAC;YAED,mBAAmB;YACnB,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;YACxD,QAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAC9C,QAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;YACxD,QAAQ,CAAC,SAAS,CAAC,2BAA2B,EAAE,qCAAqC,CAAC,CAAC;YACvF,QAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;YAEzE,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACtC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAEpD,iBAAiB;YACjB,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAExC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC9C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;CACF,CAAA;AA9DY,4CAAgB;2BAAhB,gBAAgB;IAExB,mBAAA,IAAA,aAAM,EAAC,eAAe,CAAC,UAAU,CAAC,CAAA;IAClC,mBAAA,IAAA,aAAM,EAAC,eAAe,CAAC,YAAY,CAAC,CAAA;IACpC,mBAAA,IAAA,aAAM,EAAC,eAAe,CAAC,aAAa,CAAC,CAAA;IACrC,mBAAA,IAAA,aAAM,EAAC,eAAe,CAAC,IAAI,CAAC,CAAA;IAC5B,mBAAA,IAAA,aAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAA;IAC9B,mBAAA,IAAA,aAAM,EAAC,uCAAsB,CAAC,WAAW,CAAC,CAAA;;GAPlC,gBAAgB,CA8D5B"}