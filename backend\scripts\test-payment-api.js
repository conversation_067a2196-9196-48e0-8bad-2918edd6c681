#!/usr/bin/env node

/**
 * Payment API Testing Script
 */

const axios = require('axios');

async function testPaymentAPI() {
  console.log('🔍 TESTING PAYMENT API');
  console.log('======================\n');

  try {
    // Step 1: Login to get JWT token
    console.log('🔍 Step 1: Login to get JWT token...');
    const loginResponse = await axios.post('http://localhost:3002/auth/login', {
      email: '<EMAIL>',
      password: 'FixedTest123!'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
      }
    });

    const token = loginResponse.data.token;
    console.log('✅ Login successful, token received');
    console.log('   User:', loginResponse.data.user.firstName, loginResponse.data.user.lastName);

    // Step 2: Test payment order creation
    console.log('\n🔍 Step 2: Creating payment order...');
    try {
      const paymentOrderResponse = await axios.post('http://localhost:3002/payments/create-order', {
        amount: 100,
        currency: 'INR',
        description: 'Test payment for API testing'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'Origin': 'http://localhost:4200'
        }
      });

      console.log('✅ Payment order created successfully!');
      console.log('   Order ID:', paymentOrderResponse.data.orderId);
      console.log('   Amount:', paymentOrderResponse.data.amount);
      console.log('   Currency:', paymentOrderResponse.data.currency);
      console.log('   Razorpay Key:', paymentOrderResponse.data.key);

      // Step 3: Test payment status
      console.log('\n🔍 Step 3: Checking payment status...');
      const statusResponse = await axios.get(`http://localhost:3002/payments/status/${paymentOrderResponse.data.orderId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Origin': 'http://localhost:4200'
        }
      });

      console.log('✅ Payment status retrieved successfully!');
      console.log('   Status:', statusResponse.data.payment.status);
      console.log('   Created At:', statusResponse.data.payment.createdAt);

      // Step 4: Test user payments list
      console.log('\n🔍 Step 4: Getting user payments...');
      const userPaymentsResponse = await axios.get('http://localhost:3002/payments/my-payments', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Origin': 'http://localhost:4200'
        }
      });

      console.log('✅ User payments retrieved successfully!');
      console.log('   Number of payments:', userPaymentsResponse.data.payments.length);
      userPaymentsResponse.data.payments.forEach((payment, index) => {
        console.log(`   Payment ${index + 1}: ${payment.amount} ${payment.currency} - ${payment.status}`);
      });

    } catch (paymentError) {
      console.log('❌ Payment API failed');
      console.log('   Status:', paymentError.response?.status);
      console.log('   Error:', paymentError.response?.data);
      console.log('   Message:', paymentError.message);
    }

    console.log('\n🎉 Payment API testing completed!');

  } catch (error) {
    console.error('❌ Payment API testing failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

if (require.main === module) {
  testPaymentAPI().catch(console.error);
}

module.exports = { testPaymentAPI };
