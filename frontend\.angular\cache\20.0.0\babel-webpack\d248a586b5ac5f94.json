{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-DDmgx3P4.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nclass MatPseudoCheckboxModule {\n  static ɵfac = function MatPseudoCheckboxModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatPseudoCheckboxModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatPseudoCheckboxModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatPseudoCheckbox],\n      exports: [MatPseudoCheckbox]\n    }]\n  }], null, null);\n})();\nexport { MatPseudoCheckboxModule as M };", "map": {"version": 3, "names": ["i0", "NgModule", "M", "MatPseudoCheckbox", "MatCommonModule", "MatPseudoCheckboxModule", "ɵfac", "MatPseudoCheckboxModule_Factory", "__ngFactoryType__", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "imports", "ngDevMode", "ɵsetClassMetadata", "args", "exports"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/material/fesm2022/pseudo-checkbox-module-4F8Up4PL.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-DDmgx3P4.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\n\nclass MatPseudoCheckboxModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPseudoCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPseudoCheckboxModule, imports: [MatCommonModule, MatPseudoCheckbox], exports: [MatPseudoCheckbox] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPseudoCheckboxModule, imports: [MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPseudoCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatPseudoCheckbox],\n                    exports: [MatPseudoCheckbox],\n                }]\n        }] });\n\nexport { MatPseudoCheckboxModule as M };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,gCAAgC;AACvE,SAASD,CAAC,IAAIE,eAAe,QAAQ,8BAA8B;AAEnE,MAAMC,uBAAuB,CAAC;EAC1B,OAAOC,IAAI,YAAAC,gCAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFH,uBAAuB;EAAA;EAC1H,OAAOI,IAAI,kBAD8ET,EAAE,CAAAU,gBAAA;IAAAC,IAAA,EACSN;EAAuB;EAC3H,OAAOO,IAAI,kBAF8EZ,EAAE,CAAAa,gBAAA;IAAAC,OAAA,GAE4CV,eAAe;EAAA;AAC1J;AACA;EAAA,QAAAW,SAAA,oBAAAA,SAAA,KAJ6Ff,EAAE,CAAAgB,iBAAA,CAIJX,uBAAuB,EAAc,CAAC;IACrHM,IAAI,EAAEV,QAAQ;IACdgB,IAAI,EAAE,CAAC;MACCH,OAAO,EAAE,CAACV,eAAe,EAAED,iBAAiB,CAAC;MAC7Ce,OAAO,EAAE,CAACf,iBAAiB;IAC/B,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASE,uBAAuB,IAAIH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}