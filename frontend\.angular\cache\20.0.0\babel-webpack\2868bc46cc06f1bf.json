{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { SIGNAL, producerUpdateValueVersion, signalSetFn, producerMarkClean, signalUpdateFn, producerAccessed, ERRORED, defaultEquals, UNSET, REACTIVE_NODE, COMPUTING, consumerBeforeComputation, consumerAfterComputation, runPostProducerCreatedFn, setActiveConsumer } from './signal-ePSl6jXn.mjs';\nfunction createLinkedSignal(sourceFn, computationFn, equalityFn) {\n  const node = Object.create(LINKED_SIGNAL_NODE);\n  node.source = sourceFn;\n  node.computation = computationFn;\n  if (equalityFn != undefined) {\n    node.equal = equalityFn;\n  }\n  const linkedSignalGetter = () => {\n    // Check if the value needs updating before returning it.\n    producerUpdateValueVersion(node);\n    // Record that someone looked at this signal.\n    producerAccessed(node);\n    if (node.value === ERRORED) {\n      throw node.error;\n    }\n    return node.value;\n  };\n  const getter = linkedSignalGetter;\n  getter[SIGNAL] = node;\n  if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n    const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n    getter.toString = () => `[LinkedSignal${debugName}: ${node.value}]`;\n  }\n  runPostProducerCreatedFn(node);\n  return getter;\n}\nfunction linkedSignalSetFn(node, newValue) {\n  producerUpdateValueVersion(node);\n  signalSetFn(node, newValue);\n  producerMarkClean(node);\n}\nfunction linkedSignalUpdateFn(node, updater) {\n  producerUpdateValueVersion(node);\n  signalUpdateFn(node, updater);\n  producerMarkClean(node);\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `LINKED_SIGNAL_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst LINKED_SIGNAL_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    value: UNSET,\n    dirty: true,\n    error: null,\n    equal: defaultEquals,\n    kind: 'linkedSignal',\n    producerMustRecompute(node) {\n      // Force a recomputation if there's no current value, or if the current value is in the\n      // process of being calculated (which should throw an error).\n      return node.value === UNSET || node.value === COMPUTING;\n    },\n    producerRecomputeValue(node) {\n      if (node.value === COMPUTING) {\n        // Our computation somehow led to a cyclic read of itself.\n        throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ? 'Detected cycle in computations.' : '');\n      }\n      const oldValue = node.value;\n      node.value = COMPUTING;\n      const prevConsumer = consumerBeforeComputation(node);\n      let newValue;\n      try {\n        const newSourceValue = node.source();\n        const prev = oldValue === UNSET || oldValue === ERRORED ? undefined : {\n          source: node.sourceValue,\n          value: oldValue\n        };\n        newValue = node.computation(newSourceValue, prev);\n        node.sourceValue = newSourceValue;\n      } catch (err) {\n        newValue = ERRORED;\n        node.error = err;\n      } finally {\n        consumerAfterComputation(node, prevConsumer);\n      }\n      if (oldValue !== UNSET && newValue !== ERRORED && node.equal(oldValue, newValue)) {\n        // No change to `valueVersion` - old and new values are\n        // semantically equivalent.\n        node.value = oldValue;\n        return;\n      }\n      node.value = newValue;\n      node.version++;\n    }\n  };\n})();\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nfunction untracked(nonReactiveReadsFn) {\n  const prevConsumer = setActiveConsumer(null);\n  // We are not trying to catch any particular errors here, just making sure that the consumers\n  // stack is restored in case of errors.\n  try {\n    return nonReactiveReadsFn();\n  } finally {\n    setActiveConsumer(prevConsumer);\n  }\n}\nexport { createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn, untracked };", "map": {"version": 3, "names": ["SIGNAL", "producerUpdateValueVersion", "signalSetFn", "producerMark<PERSON><PERSON>", "signalUpdateFn", "producerAccessed", "ERRORED", "defaultEquals", "UNSET", "REACTIVE_NODE", "COMPUTING", "consumerBeforeComputation", "consumerAfterComputation", "runPostProducerCreatedFn", "setActiveConsumer", "createLinkedSignal", "sourceFn", "computationFn", "equalityFn", "node", "Object", "create", "LINKED_SIGNAL_NODE", "source", "computation", "undefined", "equal", "linkedSignalGetter", "value", "error", "getter", "ngDevMode", "debugName", "toString", "linkedSignalSetFn", "newValue", "linkedSignalUpdateFn", "updater", "dirty", "kind", "producerMustRecompute", "producerRecomputeValue", "Error", "oldValue", "prevConsumer", "newSourceValue", "prev", "sourceValue", "err", "version", "untracked", "nonReactiveReadsFn"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/core/fesm2022/untracked-2ouAFbCz.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { SIGNAL, producerUpdateValueVersion, signalSetFn, producerMarkClean, signalUpdateFn, producerAccessed, ERRORED, defaultEquals, UNSET, REACTIVE_NODE, COMPUTING, consumerBeforeComputation, consumerAfterComputation, runPostProducerCreatedFn, setActiveConsumer } from './signal-ePSl6jXn.mjs';\n\nfunction createLinkedSignal(sourceFn, computationFn, equalityFn) {\n    const node = Object.create(LINKED_SIGNAL_NODE);\n    node.source = sourceFn;\n    node.computation = computationFn;\n    if (equalityFn != undefined) {\n        node.equal = equalityFn;\n    }\n    const linkedSignalGetter = () => {\n        // Check if the value needs updating before returning it.\n        producerUpdateValueVersion(node);\n        // Record that someone looked at this signal.\n        producerAccessed(node);\n        if (node.value === ERRORED) {\n            throw node.error;\n        }\n        return node.value;\n    };\n    const getter = linkedSignalGetter;\n    getter[SIGNAL] = node;\n    if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n        const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n        getter.toString = () => `[LinkedSignal${debugName}: ${node.value}]`;\n    }\n    runPostProducerCreatedFn(node);\n    return getter;\n}\nfunction linkedSignalSetFn(node, newValue) {\n    producerUpdateValueVersion(node);\n    signalSetFn(node, newValue);\n    producerMarkClean(node);\n}\nfunction linkedSignalUpdateFn(node, updater) {\n    producerUpdateValueVersion(node);\n    signalUpdateFn(node, updater);\n    producerMarkClean(node);\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `LINKED_SIGNAL_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst LINKED_SIGNAL_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        value: UNSET,\n        dirty: true,\n        error: null,\n        equal: defaultEquals,\n        kind: 'linkedSignal',\n        producerMustRecompute(node) {\n            // Force a recomputation if there's no current value, or if the current value is in the\n            // process of being calculated (which should throw an error).\n            return node.value === UNSET || node.value === COMPUTING;\n        },\n        producerRecomputeValue(node) {\n            if (node.value === COMPUTING) {\n                // Our computation somehow led to a cyclic read of itself.\n                throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ? 'Detected cycle in computations.' : '');\n            }\n            const oldValue = node.value;\n            node.value = COMPUTING;\n            const prevConsumer = consumerBeforeComputation(node);\n            let newValue;\n            try {\n                const newSourceValue = node.source();\n                const prev = oldValue === UNSET || oldValue === ERRORED\n                    ? undefined\n                    : {\n                        source: node.sourceValue,\n                        value: oldValue,\n                    };\n                newValue = node.computation(newSourceValue, prev);\n                node.sourceValue = newSourceValue;\n            }\n            catch (err) {\n                newValue = ERRORED;\n                node.error = err;\n            }\n            finally {\n                consumerAfterComputation(node, prevConsumer);\n            }\n            if (oldValue !== UNSET && newValue !== ERRORED && node.equal(oldValue, newValue)) {\n                // No change to `valueVersion` - old and new values are\n                // semantically equivalent.\n                node.value = oldValue;\n                return;\n            }\n            node.value = newValue;\n            node.version++;\n        },\n    };\n})();\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nfunction untracked(nonReactiveReadsFn) {\n    const prevConsumer = setActiveConsumer(null);\n    // We are not trying to catch any particular errors here, just making sure that the consumers\n    // stack is restored in case of errors.\n    try {\n        return nonReactiveReadsFn();\n    }\n    finally {\n        setActiveConsumer(prevConsumer);\n    }\n}\n\nexport { createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn, untracked };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAM,EAAEC,0BAA0B,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,aAAa,EAAEC,KAAK,EAAEC,aAAa,EAAEC,SAAS,EAAEC,yBAAyB,EAAEC,wBAAwB,EAAEC,wBAAwB,EAAEC,iBAAiB,QAAQ,uBAAuB;AAEvS,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,EAAE;EAC7D,MAAMC,IAAI,GAAGC,MAAM,CAACC,MAAM,CAACC,kBAAkB,CAAC;EAC9CH,IAAI,CAACI,MAAM,GAAGP,QAAQ;EACtBG,IAAI,CAACK,WAAW,GAAGP,aAAa;EAChC,IAAIC,UAAU,IAAIO,SAAS,EAAE;IACzBN,IAAI,CAACO,KAAK,GAAGR,UAAU;EAC3B;EACA,MAAMS,kBAAkB,GAAGA,CAAA,KAAM;IAC7B;IACA1B,0BAA0B,CAACkB,IAAI,CAAC;IAChC;IACAd,gBAAgB,CAACc,IAAI,CAAC;IACtB,IAAIA,IAAI,CAACS,KAAK,KAAKtB,OAAO,EAAE;MACxB,MAAMa,IAAI,CAACU,KAAK;IACpB;IACA,OAAOV,IAAI,CAACS,KAAK;EACrB,CAAC;EACD,MAAME,MAAM,GAAGH,kBAAkB;EACjCG,MAAM,CAAC9B,MAAM,CAAC,GAAGmB,IAAI;EACrB,IAAI,OAAOY,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;IAC/C,MAAMC,SAAS,GAAGb,IAAI,CAACa,SAAS,GAAG,IAAI,GAAGb,IAAI,CAACa,SAAS,GAAG,GAAG,GAAG,EAAE;IACnEF,MAAM,CAACG,QAAQ,GAAG,MAAM,gBAAgBD,SAAS,KAAKb,IAAI,CAACS,KAAK,GAAG;EACvE;EACAf,wBAAwB,CAACM,IAAI,CAAC;EAC9B,OAAOW,MAAM;AACjB;AACA,SAASI,iBAAiBA,CAACf,IAAI,EAAEgB,QAAQ,EAAE;EACvClC,0BAA0B,CAACkB,IAAI,CAAC;EAChCjB,WAAW,CAACiB,IAAI,EAAEgB,QAAQ,CAAC;EAC3BhC,iBAAiB,CAACgB,IAAI,CAAC;AAC3B;AACA,SAASiB,oBAAoBA,CAACjB,IAAI,EAAEkB,OAAO,EAAE;EACzCpC,0BAA0B,CAACkB,IAAI,CAAC;EAChCf,cAAc,CAACe,IAAI,EAAEkB,OAAO,CAAC;EAC7BlC,iBAAiB,CAACgB,IAAI,CAAC;AAC3B;AACA;AACA;AACA;AACA,MAAMG,kBAAkB,GAAG,eAAgB,CAAC,MAAM;EAC9C,OAAO;IACH,GAAGb,aAAa;IAChBmB,KAAK,EAAEpB,KAAK;IACZ8B,KAAK,EAAE,IAAI;IACXT,KAAK,EAAE,IAAI;IACXH,KAAK,EAAEnB,aAAa;IACpBgC,IAAI,EAAE,cAAc;IACpBC,qBAAqBA,CAACrB,IAAI,EAAE;MACxB;MACA;MACA,OAAOA,IAAI,CAACS,KAAK,KAAKpB,KAAK,IAAIW,IAAI,CAACS,KAAK,KAAKlB,SAAS;IAC3D,CAAC;IACD+B,sBAAsBA,CAACtB,IAAI,EAAE;MACzB,IAAIA,IAAI,CAACS,KAAK,KAAKlB,SAAS,EAAE;QAC1B;QACA,MAAM,IAAIgC,KAAK,CAAC,OAAOX,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,iCAAiC,GAAG,EAAE,CAAC;MAC3G;MACA,MAAMY,QAAQ,GAAGxB,IAAI,CAACS,KAAK;MAC3BT,IAAI,CAACS,KAAK,GAAGlB,SAAS;MACtB,MAAMkC,YAAY,GAAGjC,yBAAyB,CAACQ,IAAI,CAAC;MACpD,IAAIgB,QAAQ;MACZ,IAAI;QACA,MAAMU,cAAc,GAAG1B,IAAI,CAACI,MAAM,CAAC,CAAC;QACpC,MAAMuB,IAAI,GAAGH,QAAQ,KAAKnC,KAAK,IAAImC,QAAQ,KAAKrC,OAAO,GACjDmB,SAAS,GACT;UACEF,MAAM,EAAEJ,IAAI,CAAC4B,WAAW;UACxBnB,KAAK,EAAEe;QACX,CAAC;QACLR,QAAQ,GAAGhB,IAAI,CAACK,WAAW,CAACqB,cAAc,EAAEC,IAAI,CAAC;QACjD3B,IAAI,CAAC4B,WAAW,GAAGF,cAAc;MACrC,CAAC,CACD,OAAOG,GAAG,EAAE;QACRb,QAAQ,GAAG7B,OAAO;QAClBa,IAAI,CAACU,KAAK,GAAGmB,GAAG;MACpB,CAAC,SACO;QACJpC,wBAAwB,CAACO,IAAI,EAAEyB,YAAY,CAAC;MAChD;MACA,IAAID,QAAQ,KAAKnC,KAAK,IAAI2B,QAAQ,KAAK7B,OAAO,IAAIa,IAAI,CAACO,KAAK,CAACiB,QAAQ,EAAER,QAAQ,CAAC,EAAE;QAC9E;QACA;QACAhB,IAAI,CAACS,KAAK,GAAGe,QAAQ;QACrB;MACJ;MACAxB,IAAI,CAACS,KAAK,GAAGO,QAAQ;MACrBhB,IAAI,CAAC8B,OAAO,EAAE;IAClB;EACJ,CAAC;AACL,CAAC,EAAE,CAAC;;AAEJ;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,kBAAkB,EAAE;EACnC,MAAMP,YAAY,GAAG9B,iBAAiB,CAAC,IAAI,CAAC;EAC5C;EACA;EACA,IAAI;IACA,OAAOqC,kBAAkB,CAAC,CAAC;EAC/B,CAAC,SACO;IACJrC,iBAAiB,CAAC8B,YAAY,CAAC;EACnC;AACJ;AAEA,SAAS7B,kBAAkB,EAAEmB,iBAAiB,EAAEE,oBAAoB,EAAEc,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}