{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/common\";\nexport class ProfileComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.currentUser = null;\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.currentUserValue;\n  }\n  static #_ = this.ɵfac = function ProfileComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProfileComponent,\n    selectors: [[\"app-profile\"]],\n    standalone: false,\n    decls: 47,\n    vars: 8,\n    consts: [[1, \"profile-container\"], [1, \"container\"], [\"mat-button\", \"\", \"color\", \"primary\"], [\"mat-button\", \"\", \"color\", \"accent\"], [\"mat-button\", \"\"]],\n    template: function ProfileComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n        i0.ɵɵtext(3, \"Profile & Security Settings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"mat-card\")(5, \"mat-card-header\")(6, \"mat-card-title\");\n        i0.ɵɵtext(7, \"User Information\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"p\")(10, \"strong\");\n        i0.ɵɵtext(11, \"Name:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"p\")(14, \"strong\");\n        i0.ɵɵtext(15, \"Email:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"p\")(18, \"strong\");\n        i0.ɵɵtext(19, \"Phone:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"p\")(22, \"strong\");\n        i0.ɵɵtext(23, \"Member since:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(24);\n        i0.ɵɵpipe(25, \"date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"mat-card-actions\")(27, \"button\", 2)(28, \"mat-icon\");\n        i0.ɵɵtext(29, \"edit\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(30, \" Edit Profile \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(31, \"mat-card\")(32, \"mat-card-header\")(33, \"mat-card-title\");\n        i0.ɵɵtext(34, \"Security Settings\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(35, \"mat-card-content\")(36, \"p\");\n        i0.ɵɵtext(37, \"Manage your account security settings including two-factor authentication.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"mat-card-actions\")(39, \"button\", 3)(40, \"mat-icon\");\n        i0.ɵɵtext(41, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(42, \" Setup 2FA \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"button\", 4)(44, \"mat-icon\");\n        i0.ɵɵtext(45, \"lock\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(46, \" Change Password \");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵtextInterpolate2(\" \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \" \", ctx.currentUser == null ? null : ctx.currentUser.lastName);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.currentUser == null ? null : ctx.currentUser.email);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.phone) || \"Not provided\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(25, 5, ctx.currentUser == null ? null : ctx.currentUser.createdAt, \"mediumDate\"));\n      }\n    },\n    dependencies: [i2.MatCard, i2.MatCardActions, i2.MatCardContent, i2.MatCardHeader, i2.MatCardTitle, i3.MatButton, i4.MatIcon, i5.DatePipe],\n    styles: [\".profile-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: #f5f5f5;\\n  padding: 2rem;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 2rem;\\n}\\n\\nmat-card[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9wcm9maWxlL3Byb2ZpbGUuY29tcG9uZW50LnNjc3MiLCJ3ZWJwYWNrOi8vLi8uLi8uLi9Nb2R1bGFyJTIwYmFja2VuZCUyMHNlY3VyZSUyMHVzZXIlMjBzeXN0ZW0lMjBhbmQlMjBwYXltZW50L2Zyb250ZW5kL3NyYy9hcHAvbW9kdWxlcy9wcm9maWxlL3Byb2ZpbGUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtBQ0NGOztBREVBO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0FDQ0Y7O0FERUE7RUFDRSxXQUFBO0VBQ0EsbUJBQUE7QUNDRjs7QURFQTtFQUNFLHFCQUFBO0FDQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIucHJvZmlsZS1jb250YWluZXIge1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgYmFja2dyb3VuZDogI2Y1ZjVmNTtcbiAgcGFkZGluZzogMnJlbTtcbn1cblxuLmNvbnRhaW5lciB7XG4gIG1heC13aWR0aDogODAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xufVxuXG5oMSB7XG4gIGNvbG9yOiAjMzMzO1xuICBtYXJnaW4tYm90dG9tOiAycmVtO1xufVxuXG5tYXQtY2FyZCB7XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbn1cbiIsIi5wcm9maWxlLWNvbnRhaW5lciB7XG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICBiYWNrZ3JvdW5kOiAjZjVmNWY1O1xuICBwYWRkaW5nOiAycmVtO1xufVxuXG4uY29udGFpbmVyIHtcbiAgbWF4LXdpZHRoOiA4MDBweDtcbiAgbWFyZ2luOiAwIGF1dG87XG59XG5cbmgxIHtcbiAgY29sb3I6ICMzMzM7XG4gIG1hcmdpbi1ib3R0b206IDJyZW07XG59XG5cbm1hdC1jYXJkIHtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["ProfileComponent", "constructor", "authService", "currentUser", "ngOnInit", "currentUserValue", "_", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "firstName", "lastName", "ɵɵtextInterpolate1", "email", "phone", "ɵɵpipeBind2", "createdAt"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\modules\\profile\\profile.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\modules\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AuthService } from '../../services/auth.service';\nimport { User } from '../../models/user.model';\n\n@Component({\n  selector: 'app-profile',\n  templateUrl: './profile.component.html',\n  styleUrls: ['./profile.component.scss'],\n  standalone: false\n})\nexport class ProfileComponent implements OnInit {\n  currentUser: User | null = null;\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.currentUserValue;\n  }\n}\n", "<div class=\"profile-container\">\n  <div class=\"container\">\n    <h1>Profile & Security Settings</h1>\n    \n    <mat-card>\n      <mat-card-header>\n        <mat-card-title>User Information</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <p><strong>Name:</strong> {{ currentUser?.firstName }} {{ currentUser?.lastName }}</p>\n        <p><strong>Email:</strong> {{ currentUser?.email }}</p>\n        <p><strong>Phone:</strong> {{ currentUser?.phone || 'Not provided' }}</p>\n        <p><strong>Member since:</strong> {{ currentUser?.createdAt | date:'mediumDate' }}</p>\n      </mat-card-content>\n      <mat-card-actions>\n        <button mat-button color=\"primary\">\n          <mat-icon>edit</mat-icon>\n          Edit Profile\n        </button>\n      </mat-card-actions>\n    </mat-card>\n\n    <mat-card>\n      <mat-card-header>\n        <mat-card-title>Security Settings</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <p>Manage your account security settings including two-factor authentication.</p>\n      </mat-card-content>\n      <mat-card-actions>\n        <button mat-button color=\"accent\">\n          <mat-icon>security</mat-icon>\n          Setup 2FA\n        </button>\n        <button mat-button>\n          <mat-icon>lock</mat-icon>\n          Change Password\n        </button>\n      </mat-card-actions>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": ";;;;;;AAUA,OAAM,MAAOA,gBAAgB;EAG3BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAF/B,KAAAC,WAAW,GAAgB,IAAI;EAEgB;EAE/CC,QAAQA,CAAA;IACN,IAAI,CAACD,WAAW,GAAG,IAAI,CAACD,WAAW,CAACG,gBAAgB;EACtD;EAAC,QAAAC,CAAA,G;qCAPUN,gBAAgB,EAAAO,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhBX,gBAAgB;IAAAY,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRzBZ,EAFJ,CAAAc,cAAA,aAA+B,aACN,SACjB;QAAAd,EAAA,CAAAe,MAAA,kCAA2B;QAAAf,EAAA,CAAAgB,YAAA,EAAK;QAIhChB,EAFJ,CAAAc,cAAA,eAAU,sBACS,qBACC;QAAAd,EAAA,CAAAe,MAAA,uBAAgB;QAClCf,EADkC,CAAAgB,YAAA,EAAiB,EACjC;QAEbhB,EADL,CAAAc,cAAA,uBAAkB,QACb,cAAQ;QAAAd,EAAA,CAAAe,MAAA,aAAK;QAAAf,EAAA,CAAAgB,YAAA,EAAS;QAAChB,EAAA,CAAAe,MAAA,IAAwD;QAAAf,EAAA,CAAAgB,YAAA,EAAI;QACnFhB,EAAH,CAAAc,cAAA,SAAG,cAAQ;QAAAd,EAAA,CAAAe,MAAA,cAAM;QAAAf,EAAA,CAAAgB,YAAA,EAAS;QAAChB,EAAA,CAAAe,MAAA,IAAwB;QAAAf,EAAA,CAAAgB,YAAA,EAAI;QACpDhB,EAAH,CAAAc,cAAA,SAAG,cAAQ;QAAAd,EAAA,CAAAe,MAAA,cAAM;QAAAf,EAAA,CAAAgB,YAAA,EAAS;QAAChB,EAAA,CAAAe,MAAA,IAA0C;QAAAf,EAAA,CAAAgB,YAAA,EAAI;QACtEhB,EAAH,CAAAc,cAAA,SAAG,cAAQ;QAAAd,EAAA,CAAAe,MAAA,qBAAa;QAAAf,EAAA,CAAAgB,YAAA,EAAS;QAAChB,EAAA,CAAAe,MAAA,IAAgD;;QACpFf,EADoF,CAAAgB,YAAA,EAAI,EACrE;QAGfhB,EAFJ,CAAAc,cAAA,wBAAkB,iBACmB,gBACvB;QAAAd,EAAA,CAAAe,MAAA,YAAI;QAAAf,EAAA,CAAAgB,YAAA,EAAW;QACzBhB,EAAA,CAAAe,MAAA,sBACF;QAEJf,EAFI,CAAAgB,YAAA,EAAS,EACQ,EACV;QAIPhB,EAFJ,CAAAc,cAAA,gBAAU,uBACS,sBACC;QAAAd,EAAA,CAAAe,MAAA,yBAAiB;QACnCf,EADmC,CAAAgB,YAAA,EAAiB,EAClC;QAEhBhB,EADF,CAAAc,cAAA,wBAAkB,SACb;QAAAd,EAAA,CAAAe,MAAA,kFAA0E;QAC/Ef,EAD+E,CAAAgB,YAAA,EAAI,EAChE;QAGfhB,EAFJ,CAAAc,cAAA,wBAAkB,iBACkB,gBACtB;QAAAd,EAAA,CAAAe,MAAA,gBAAQ;QAAAf,EAAA,CAAAgB,YAAA,EAAW;QAC7BhB,EAAA,CAAAe,MAAA,mBACF;QAAAf,EAAA,CAAAgB,YAAA,EAAS;QAEPhB,EADF,CAAAc,cAAA,iBAAmB,gBACP;QAAAd,EAAA,CAAAe,MAAA,YAAI;QAAAf,EAAA,CAAAgB,YAAA,EAAW;QACzBhB,EAAA,CAAAe,MAAA,yBACF;QAIRf,EAJQ,CAAAgB,YAAA,EAAS,EACQ,EACV,EACP,EACF;;;QAhC4BhB,EAAA,CAAAiB,SAAA,IAAwD;QAAxDjB,EAAA,CAAAkB,kBAAA,MAAAL,GAAA,CAAAjB,WAAA,kBAAAiB,GAAA,CAAAjB,WAAA,CAAAuB,SAAA,OAAAN,GAAA,CAAAjB,WAAA,kBAAAiB,GAAA,CAAAjB,WAAA,CAAAwB,QAAA,CAAwD;QACvDpB,EAAA,CAAAiB,SAAA,GAAwB;QAAxBjB,EAAA,CAAAqB,kBAAA,MAAAR,GAAA,CAAAjB,WAAA,kBAAAiB,GAAA,CAAAjB,WAAA,CAAA0B,KAAA,CAAwB;QACxBtB,EAAA,CAAAiB,SAAA,GAA0C;QAA1CjB,EAAA,CAAAqB,kBAAA,OAAAR,GAAA,CAAAjB,WAAA,kBAAAiB,GAAA,CAAAjB,WAAA,CAAA2B,KAAA,oBAA0C;QACnCvB,EAAA,CAAAiB,SAAA,GAAgD;QAAhDjB,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAwB,WAAA,QAAAX,GAAA,CAAAjB,WAAA,kBAAAiB,GAAA,CAAAjB,WAAA,CAAA6B,SAAA,gBAAgD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}