{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.dirty || form && form.submitted));\n  }\n  static ɵfac = function ShowOnDirtyErrorStateMatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ShowOnDirtyErrorStateMatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ShowOnDirtyErrorStateMatcher,\n    factory: ShowOnDirtyErrorStateMatcher.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowOnDirtyErrorStateMatcher, [{\n    type: Injectable\n  }], null, null);\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.touched || form && form.submitted));\n  }\n  static ɵfac = function ErrorStateMatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ErrorStateMatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ErrorStateMatcher,\n    factory: ErrorStateMatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ErrorStateMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { ErrorStateMatcher as E, ShowOnDirtyErrorStateMatcher as S };", "map": {"version": 3, "names": ["i0", "Injectable", "ShowOnDirtyErrorStateMatcher", "isErrorState", "control", "form", "invalid", "dirty", "submitted", "ɵfac", "ShowOnDirtyErrorStateMatcher_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "ErrorStateMatcher", "touched", "ErrorStateMatcher_Factory", "providedIn", "args", "E", "S"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/material/fesm2022/error-options-DCNQlTOA.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.dirty || (form && form.submitted)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, decorators: [{\n            type: Injectable\n        }] });\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.touched || (form && form.submitted)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ErrorStateMatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ErrorStateMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nexport { ErrorStateMatcher as E, ShowOnDirtyErrorStateMatcher as S };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;;AAE1C;AACA,MAAMC,4BAA4B,CAAC;EAC/BC,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAE;IACxB,OAAO,CAAC,EAAED,OAAO,IAAIA,OAAO,CAACE,OAAO,KAAKF,OAAO,CAACG,KAAK,IAAKF,IAAI,IAAIA,IAAI,CAACG,SAAU,CAAC,CAAC;EACxF;EACA,OAAOC,IAAI,YAAAC,qCAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFT,4BAA4B;EAAA;EAC/H,OAAOU,KAAK,kBAD6EZ,EAAE,CAAAa,kBAAA;IAAAC,KAAA,EACYZ,4BAA4B;IAAAa,OAAA,EAA5Bb,4BAA4B,CAAAO;EAAA;AACvI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAH6FhB,EAAE,CAAAiB,iBAAA,CAGJf,4BAA4B,EAAc,CAAC;IAC1HgB,IAAI,EAAEjB;EACV,CAAC,CAAC;AAAA;AACV;AACA,MAAMkB,iBAAiB,CAAC;EACpBhB,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAE;IACxB,OAAO,CAAC,EAAED,OAAO,IAAIA,OAAO,CAACE,OAAO,KAAKF,OAAO,CAACgB,OAAO,IAAKf,IAAI,IAAIA,IAAI,CAACG,SAAU,CAAC,CAAC;EAC1F;EACA,OAAOC,IAAI,YAAAY,0BAAAV,iBAAA;IAAA,YAAAA,iBAAA,IAAwFQ,iBAAiB;EAAA;EACpH,OAAOP,KAAK,kBAZ6EZ,EAAE,CAAAa,kBAAA;IAAAC,KAAA,EAYYK,iBAAiB;IAAAJ,OAAA,EAAjBI,iBAAiB,CAAAV,IAAA;IAAAa,UAAA,EAAc;EAAM;AAChJ;AACA;EAAA,QAAAN,SAAA,oBAAAA,SAAA,KAd6FhB,EAAE,CAAAiB,iBAAA,CAcJE,iBAAiB,EAAc,CAAC;IAC/GD,IAAI,EAAEjB,UAAU;IAChBsB,IAAI,EAAE,CAAC;MAAED,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,SAASH,iBAAiB,IAAIK,CAAC,EAAEtB,4BAA4B,IAAIuB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}