"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecuritySequence = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const rest_1 = require("@loopback/rest");
const authentication_1 = require("@loopback/authentication");
const SequenceActions = rest_1.RestBindings.SequenceActions;
let SecuritySequence = class SecuritySequence {
    constructor(findRoute, parseParams, invoke, send, reject, authenticateRequest) {
        this.findRoute = findRoute;
        this.parseParams = parseParams;
        this.invoke = invoke;
        this.send = send;
        this.reject = reject;
        this.authenticateRequest = authenticateRequest;
    }
    async handle(context) {
        try {
            const { request, response } = context;
            // CORS headers
            const origin = request.headers.origin;
            const allowedOrigins = [
                'http://localhost:3001',
                'http://localhost:4200',
                process.env.FRONTEND_URL
            ].filter(Boolean);
            if (origin && allowedOrigins.includes(origin)) {
                response.setHeader('Access-Control-Allow-Origin', origin);
            }
            else if (!origin) {
                // For same-origin requests
                response.setHeader('Access-Control-Allow-Origin', 'http://localhost:3001');
            }
            response.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
            response.setHeader('Access-Control-Allow-Headers', 'Origin,X-Requested-With,Content-Type,Accept,Authorization,X-CSRF-Token');
            response.setHeader('Access-Control-Allow-Credentials', 'true');
            response.setHeader('Access-Control-Max-Age', '86400');
            // Handle preflight requests
            if (request.method === 'OPTIONS') {
                response.statusCode = 204;
                response.end();
                return;
            }
            // Security headers
            response.setHeader('X-Content-Type-Options', 'nosniff');
            response.setHeader('X-Frame-Options', 'DENY');
            response.setHeader('X-XSS-Protection', '1; mode=block');
            response.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
            response.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
            const route = this.findRoute(request);
            const args = await this.parseParams(request, route);
            // Authentication
            await this.authenticateRequest(request);
            const result = await this.invoke(route, args);
            this.send(response, result);
        }
        catch (err) {
            this.reject(context, err);
        }
    }
};
exports.SecuritySequence = SecuritySequence;
exports.SecuritySequence = SecuritySequence = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)(SequenceActions.FIND_ROUTE)),
    tslib_1.__param(1, (0, core_1.inject)(SequenceActions.PARSE_PARAMS)),
    tslib_1.__param(2, (0, core_1.inject)(SequenceActions.INVOKE_METHOD)),
    tslib_1.__param(3, (0, core_1.inject)(SequenceActions.SEND)),
    tslib_1.__param(4, (0, core_1.inject)(SequenceActions.REJECT)),
    tslib_1.__param(5, (0, core_1.inject)(authentication_1.AuthenticationBindings.AUTH_ACTION)),
    tslib_1.__metadata("design:paramtypes", [Function, Function, Function, Function, Function, Function])
], SecuritySequence);
//# sourceMappingURL=sequence.js.map