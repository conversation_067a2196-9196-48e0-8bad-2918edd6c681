{"ast": null, "code": "import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { refCount as higherOrderRefCount } from '../operators/refCount';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { hasLift } from '../util/lift';\nexport class ConnectableObservable extends Observable {\n  constructor(source, subjectFactory) {\n    super();\n    this.source = source;\n    this.subjectFactory = subjectFactory;\n    this._subject = null;\n    this._refCount = 0;\n    this._connection = null;\n    if (hasLift(source)) {\n      this.lift = source.lift;\n    }\n  }\n  _subscribe(subscriber) {\n    return this.getSubject().subscribe(subscriber);\n  }\n  getSubject() {\n    const subject = this._subject;\n    if (!subject || subject.isStopped) {\n      this._subject = this.subjectFactory();\n    }\n    return this._subject;\n  }\n  _teardown() {\n    this._refCount = 0;\n    const {\n      _connection\n    } = this;\n    this._subject = this._connection = null;\n    _connection === null || _connection === void 0 ? void 0 : _connection.unsubscribe();\n  }\n  connect() {\n    let connection = this._connection;\n    if (!connection) {\n      connection = this._connection = new Subscription();\n      const subject = this.getSubject();\n      connection.add(this.source.subscribe(createOperatorSubscriber(subject, undefined, () => {\n        this._teardown();\n        subject.complete();\n      }, err => {\n        this._teardown();\n        subject.error(err);\n      }, () => this._teardown())));\n      if (connection.closed) {\n        this._connection = null;\n        connection = Subscription.EMPTY;\n      }\n    }\n    return connection;\n  }\n  refCount() {\n    return higherOrderRefCount()(this);\n  }\n}", "map": {"version": 3, "names": ["Observable", "Subscription", "refCount", "higherOrderRefCount", "createOperatorSubscriber", "hasLift", "ConnectableObservable", "constructor", "source", "subjectFactory", "_subject", "_refCount", "_connection", "lift", "_subscribe", "subscriber", "getSubject", "subscribe", "subject", "isStopped", "_teardown", "unsubscribe", "connect", "connection", "add", "undefined", "complete", "err", "error", "closed", "EMPTY"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/rxjs/dist/esm/internal/observable/ConnectableObservable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { refCount as higherOrderRefCount } from '../operators/refCount';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { hasLift } from '../util/lift';\nexport class ConnectableObservable extends Observable {\n    constructor(source, subjectFactory) {\n        super();\n        this.source = source;\n        this.subjectFactory = subjectFactory;\n        this._subject = null;\n        this._refCount = 0;\n        this._connection = null;\n        if (hasLift(source)) {\n            this.lift = source.lift;\n        }\n    }\n    _subscribe(subscriber) {\n        return this.getSubject().subscribe(subscriber);\n    }\n    getSubject() {\n        const subject = this._subject;\n        if (!subject || subject.isStopped) {\n            this._subject = this.subjectFactory();\n        }\n        return this._subject;\n    }\n    _teardown() {\n        this._refCount = 0;\n        const { _connection } = this;\n        this._subject = this._connection = null;\n        _connection === null || _connection === void 0 ? void 0 : _connection.unsubscribe();\n    }\n    connect() {\n        let connection = this._connection;\n        if (!connection) {\n            connection = this._connection = new Subscription();\n            const subject = this.getSubject();\n            connection.add(this.source.subscribe(createOperatorSubscriber(subject, undefined, () => {\n                this._teardown();\n                subject.complete();\n            }, (err) => {\n                this._teardown();\n                subject.error(err);\n            }, () => this._teardown())));\n            if (connection.closed) {\n                this._connection = null;\n                connection = Subscription.EMPTY;\n            }\n        }\n        return connection;\n    }\n    refCount() {\n        return higherOrderRefCount()(this);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,IAAIC,mBAAmB,QAAQ,uBAAuB;AACvE,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,OAAO,QAAQ,cAAc;AACtC,OAAO,MAAMC,qBAAqB,SAASN,UAAU,CAAC;EAClDO,WAAWA,CAACC,MAAM,EAAEC,cAAc,EAAE;IAChC,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAIP,OAAO,CAACG,MAAM,CAAC,EAAE;MACjB,IAAI,CAACK,IAAI,GAAGL,MAAM,CAACK,IAAI;IAC3B;EACJ;EACAC,UAAUA,CAACC,UAAU,EAAE;IACnB,OAAO,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,SAAS,CAACF,UAAU,CAAC;EAClD;EACAC,UAAUA,CAAA,EAAG;IACT,MAAME,OAAO,GAAG,IAAI,CAACR,QAAQ;IAC7B,IAAI,CAACQ,OAAO,IAAIA,OAAO,CAACC,SAAS,EAAE;MAC/B,IAAI,CAACT,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACzC;IACA,OAAO,IAAI,CAACC,QAAQ;EACxB;EACAU,SAASA,CAAA,EAAG;IACR,IAAI,CAACT,SAAS,GAAG,CAAC;IAClB,MAAM;MAAEC;IAAY,CAAC,GAAG,IAAI;IAC5B,IAAI,CAACF,QAAQ,GAAG,IAAI,CAACE,WAAW,GAAG,IAAI;IACvCA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACS,WAAW,CAAC,CAAC;EACvF;EACAC,OAAOA,CAAA,EAAG;IACN,IAAIC,UAAU,GAAG,IAAI,CAACX,WAAW;IACjC,IAAI,CAACW,UAAU,EAAE;MACbA,UAAU,GAAG,IAAI,CAACX,WAAW,GAAG,IAAIX,YAAY,CAAC,CAAC;MAClD,MAAMiB,OAAO,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;MACjCO,UAAU,CAACC,GAAG,CAAC,IAAI,CAAChB,MAAM,CAACS,SAAS,CAACb,wBAAwB,CAACc,OAAO,EAAEO,SAAS,EAAE,MAAM;QACpF,IAAI,CAACL,SAAS,CAAC,CAAC;QAChBF,OAAO,CAACQ,QAAQ,CAAC,CAAC;MACtB,CAAC,EAAGC,GAAG,IAAK;QACR,IAAI,CAACP,SAAS,CAAC,CAAC;QAChBF,OAAO,CAACU,KAAK,CAACD,GAAG,CAAC;MACtB,CAAC,EAAE,MAAM,IAAI,CAACP,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,IAAIG,UAAU,CAACM,MAAM,EAAE;QACnB,IAAI,CAACjB,WAAW,GAAG,IAAI;QACvBW,UAAU,GAAGtB,YAAY,CAAC6B,KAAK;MACnC;IACJ;IACA,OAAOP,UAAU;EACrB;EACArB,QAAQA,CAAA,EAAG;IACP,OAAOC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC;EACtC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}