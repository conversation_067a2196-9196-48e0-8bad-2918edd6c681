{"ast": null, "code": "import { filter } from './filter';\nexport function skip(count) {\n  return filter((_, index) => count <= index);\n}", "map": {"version": 3, "names": ["filter", "skip", "count", "_", "index"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/rxjs/dist/esm/internal/operators/skip.js"], "sourcesContent": ["import { filter } from './filter';\nexport function skip(count) {\n    return filter((_, index) => count <= index);\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,UAAU;AACjC,OAAO,SAASC,IAAIA,CAACC,KAAK,EAAE;EACxB,OAAOF,MAAM,CAAC,CAACG,CAAC,EAAEC,KAAK,KAAKF,KAAK,IAAIE,KAAK,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}