import {inject} from '@loopback/core';
import {
  FindRoute,
  InvokeMethod,
  Parse<PERSON>ara<PERSON>,
  Reject,
  RequestContext,
  RestBindings,
  Send,
  SequenceHandler,
} from '@loopback/rest';
import {
  AuthenticationBindings,
  AuthenticateFn,
} from '@loopback/authentication';
import {
  AuthorizationBindings,
} from '@loopback/authorization';

const SequenceActions = RestBindings.SequenceActions;

export class SecuritySequence implements SequenceHandler {
  constructor(
    @inject(SequenceActions.FIND_ROUTE) protected findRoute: FindRoute,
    @inject(SequenceActions.PARSE_PARAMS) protected parseParams: ParseParams,
    @inject(SequenceActions.INVOKE_METHOD) protected invoke: InvokeMethod,
    @inject(SequenceActions.SEND) public send: Send,
    @inject(SequenceActions.REJECT) public reject: Reject,
    @inject(AuthenticationBindings.AUTH_ACTION)
    protected authenticateRequest: AuthenticateFn,
  ) {}

  async handle(context: RequestContext) {
    try {
      const {request, response} = context;

      // CORS headers
      const origin = request.headers.origin;
      const allowedOrigins = [
        'http://localhost:3001',
        'http://localhost:4200',
        process.env.FRONTEND_URL
      ].filter(Boolean);

      if (origin && allowedOrigins.includes(origin)) {
        response.setHeader('Access-Control-Allow-Origin', origin);
      } else if (!origin) {
        // For same-origin requests
        response.setHeader('Access-Control-Allow-Origin', 'http://localhost:3001');
      }

      response.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
      response.setHeader('Access-Control-Allow-Headers', 'Origin,X-Requested-With,Content-Type,Accept,Authorization,X-CSRF-Token');
      response.setHeader('Access-Control-Allow-Credentials', 'true');
      response.setHeader('Access-Control-Max-Age', '86400');

      // Handle preflight requests
      if (request.method === 'OPTIONS') {
        response.statusCode = 204;
        response.end();
        return;
      }

      // Security headers
      response.setHeader('X-Content-Type-Options', 'nosniff');
      response.setHeader('X-Frame-Options', 'DENY');
      response.setHeader('X-XSS-Protection', '1; mode=block');
      response.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
      response.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
      
      const route = this.findRoute(request);
      const args = await this.parseParams(request, route);
      
      // Authentication
      await this.authenticateRequest(request);
      
      const result = await this.invoke(route, args);
      this.send(response, result);
    } catch (err) {
      this.reject(context, err);
    }
  }
}
