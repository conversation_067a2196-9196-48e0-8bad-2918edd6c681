{"ast": null, "code": "import { createRepositionScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injector, ChangeDetectorRef, ElementRef, Renderer2, signal, EventEmitter, HostAttributeToken, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, Input, ViewChild, Output, Directive, NgModule } from '@angular/core';\nimport { ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _IdGenerator, LiveAnnouncer, removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { hasModifier<PERSON>ey, ENTER, SPACE, A, ESCAPE, DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW } from '@angular/cdk/keycodes';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, filter, map, takeUntil, take } from 'rxjs/operators';\nimport { NgClass } from '@angular/common';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-C9DZXojn.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition, c as MAT_OPTION_PARENT_COMPONENT, M as MatOption, d as MAT_OPTGROUP } from './option-BzhYL_xC.mjs';\nimport { E as ErrorStateMatcher } from './error-options-DCNQlTOA.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { M as MatOptionModule } from './index-DwiL-HGk.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatFormFieldModule } from './module-DzZHEh7B.mjs';\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nconst _c0 = [\"trigger\"];\nconst _c1 = [\"panel\"];\nconst _c2 = [[[\"mat-select-trigger\"]], \"*\"];\nconst _c3 = [\"mat-select-trigger\", \"*\"];\nfunction MatSelect_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.placeholder);\n  }\n}\nfunction MatSelect_Conditional_5_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction MatSelect_Conditional_5_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.triggerValue);\n  }\n}\nfunction MatSelect_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵconditionalCreate(1, MatSelect_Conditional_5_Conditional_1_Template, 1, 0)(2, MatSelect_Conditional_5_Conditional_2_Template, 2, 1, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.customTrigger ? 1 : 2);\n  }\n}\nfunction MatSelect_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12, 1);\n    i0.ɵɵlistener(\"keydown\", function MatSelect_ng_template_10_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    });\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(i0.ɵɵinterpolate1(\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open \", ctx_r1._getPanelTheme()));\n    i0.ɵɵclassProp(\"mat-select-panel-animations-enabled\", !ctx_r1._animationsDisabled);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.panelClass);\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"-panel\")(\"aria-multiselectable\", ctx_r1.multiple)(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1._getPanelAriaLabelledby());\n  }\n}\nfunction getMatSelectDynamicMultipleError() {\n  return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n  return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n  return Error('`compareWith` must be a function.');\n}\n\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(_overlay) {\n  const injector = inject(Injector);\n  return () => createRepositionScrollStrategy(injector);\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_SELECT_SCROLL_STRATEGY,\n  deps: [],\n  useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n  source;\n  value;\n  constructor(/** Reference to the select that emitted the change event. */\n  source, /** Current value of the select that emitted the event. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\nclass MatSelect {\n  _viewportRuler = inject(ViewportRuler);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _idGenerator = inject(_IdGenerator);\n  _renderer = inject(Renderer2);\n  _parentFormField = inject(MAT_FORM_FIELD, {\n    optional: true\n  });\n  ngControl = inject(NgControl, {\n    self: true,\n    optional: true\n  });\n  _liveAnnouncer = inject(LiveAnnouncer);\n  _defaultOptions = inject(MAT_SELECT_CONFIG, {\n    optional: true\n  });\n  _animationsDisabled = _animationsDisabled();\n  _initialized = new Subject();\n  _cleanupDetach;\n  /** All of the defined select options. */\n  options;\n  // TODO(crisbeto): this is only necessary for the non-MDC select, but it's technically a\n  // public API so we have to keep it. It should be deprecated and removed eventually.\n  /** All of the defined groups of options. */\n  optionGroups;\n  /** User-supplied override of the trigger element. */\n  customTrigger;\n  /**\n   * This position config ensures that the top \"start\" corner of the overlay\n   * is aligned with with the top \"start\" of the origin by default (overlapping\n   * the trigger completely). If the panel cannot fit below the trigger, it\n   * will fall back to a position above the trigger.\n   */\n  _positions = [{\n    originX: 'start',\n    originY: 'bottom',\n    overlayX: 'start',\n    overlayY: 'top'\n  }, {\n    originX: 'end',\n    originY: 'bottom',\n    overlayX: 'end',\n    overlayY: 'top'\n  }, {\n    originX: 'start',\n    originY: 'top',\n    overlayX: 'start',\n    overlayY: 'bottom',\n    panelClass: 'mat-mdc-select-panel-above'\n  }, {\n    originX: 'end',\n    originY: 'top',\n    overlayX: 'end',\n    overlayY: 'bottom',\n    panelClass: 'mat-mdc-select-panel-above'\n  }];\n  /** Scrolls a particular option into the view. */\n  _scrollOptionIntoView(index) {\n    const option = this.options.toArray()[index];\n    if (option) {\n      const panel = this.panel.nativeElement;\n      const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n      const element = option._getHostElement();\n      if (index === 0 && labelCount === 1) {\n        // If we've got one group label before the option and we're at the top option,\n        // scroll the list to the top. This is better UX than scrolling the list to the\n        // top of the option, because it allows the user to read the top group's label.\n        panel.scrollTop = 0;\n      } else {\n        panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n      }\n    }\n  }\n  /** Called when the panel has been opened and the overlay has settled on its final position. */\n  _positioningSettled() {\n    this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n  }\n  /** Creates a change event object that should be emitted by the select. */\n  _getChangeEvent(value) {\n    return new MatSelectChange(this, value);\n  }\n  /** Factory function used to create a scroll strategy for this select. */\n  _scrollStrategyFactory = inject(MAT_SELECT_SCROLL_STRATEGY);\n  /** Whether or not the overlay panel is open. */\n  _panelOpen = false;\n  /** Comparison function to specify which option is displayed. Defaults to object equality. */\n  _compareWith = (o1, o2) => o1 === o2;\n  /** Unique id for this input. */\n  _uid = this._idGenerator.getId('mat-select-');\n  /** Current `aria-labelledby` value for the select trigger. */\n  _triggerAriaLabelledBy = null;\n  /**\n   * Keeps track of the previous form control assigned to the select.\n   * Used to detect if it has changed.\n   */\n  _previousControl;\n  /** Emits whenever the component is destroyed. */\n  _destroy = new Subject();\n  /** Tracks the error state of the select. */\n  _errorStateTracker;\n  /**\n   * Emits whenever the component state changes and should cause the parent\n   * form-field to update. Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  stateChanges = new Subject();\n  /**\n   * Disable the automatic labeling to avoid issues like #27241.\n   * @docs-private\n   */\n  disableAutomaticLabeling = true;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  userAriaDescribedBy;\n  /** Deals with the selection logic. */\n  _selectionModel;\n  /** Manages keyboard events for options in the panel. */\n  _keyManager;\n  /** Ideal origin for the overlay panel. */\n  _preferredOverlayOrigin;\n  /** Width of the overlay panel. */\n  _overlayWidth;\n  /** `View -> model callback called when value changes` */\n  _onChange = () => {};\n  /** `View -> model callback called when select has been touched` */\n  _onTouched = () => {};\n  /** ID for the DOM node containing the select's value. */\n  _valueId = this._idGenerator.getId('mat-select-value-');\n  /** Strategy that will be used to handle scrolling while the select panel is open. */\n  _scrollStrategy;\n  _overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n  /** Whether the select is focused. */\n  get focused() {\n    return this._focused || this._panelOpen;\n  }\n  _focused = false;\n  /** A name for this control that can be used by `mat-form-field`. */\n  controlType = 'mat-select';\n  /** Trigger that opens the select. */\n  trigger;\n  /** Panel containing the select options. */\n  panel;\n  /** Overlay pane containing the options. */\n  _overlayDir;\n  /** Classes to be passed to the select panel. Supports the same syntax as `ngClass`. */\n  panelClass;\n  /** Whether the select is disabled. */\n  disabled = false;\n  /** Whether ripples in the select are disabled. */\n  get disableRipple() {\n    return this._disableRipple();\n  }\n  set disableRipple(value) {\n    this._disableRipple.set(value);\n  }\n  _disableRipple = signal(false);\n  /** Tab index of the select. */\n  tabIndex = 0;\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncParentProperties();\n  }\n  _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n  /** Placeholder to be shown if no value has been selected. */\n  get placeholder() {\n    return this._placeholder;\n  }\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  _placeholder;\n  /** Whether the component is required. */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = value;\n    this.stateChanges.next();\n  }\n  _required;\n  /** Whether the user should be allowed to select multiple options. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectDynamicMultipleError();\n    }\n    this._multiple = value;\n  }\n  _multiple = false;\n  /** Whether to center the active option over the trigger. */\n  disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n  /**\n   * Function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  get compareWith() {\n    return this._compareWith;\n  }\n  set compareWith(fn) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectNonFunctionValueError();\n    }\n    this._compareWith = fn;\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n  /** Value of the select control. */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    const hasAssigned = this._assignValue(newValue);\n    if (hasAssigned) {\n      this._onChange(newValue);\n    }\n  }\n  _value;\n  /** Aria label of the select. */\n  ariaLabel = '';\n  /** Input that can be used to specify the `aria-labelledby` attribute. */\n  ariaLabelledby;\n  /** Object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n  typeaheadDebounceInterval;\n  /**\n   * Function used to sort the values in a select in multiple mode.\n   * Follows the same logic as `Array.prototype.sort`.\n   */\n  sortComparator;\n  /** Unique id of the element. */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n    this.stateChanges.next();\n  }\n  _id;\n  /** Whether the select is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  /**\n   * Width of the panel. If set to `auto`, the panel will match the trigger width.\n   * If set to null or an empty string, the panel will grow to match the longest option's text.\n   */\n  panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined' ? this._defaultOptions.panelWidth : 'auto';\n  /**\n   * By default selecting an option with a `null` or `undefined` value will reset the select's\n   * value. Enable this option if the reset behavior doesn't match your requirements and instead\n   * the nullable options should become selected. The value of this input can be controlled app-wide\n   * using the `MAT_SELECT_CONFIG` injection token.\n   */\n  canSelectNullableOptions = this._defaultOptions?.canSelectNullableOptions ?? false;\n  /** Combined stream of all of the child options' change events. */\n  optionSelectionChanges = defer(() => {\n    const options = this.options;\n    if (options) {\n      return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n    }\n    return this._initialized.pipe(switchMap(() => this.optionSelectionChanges));\n  });\n  /** Event emitted when the select panel has been toggled. */\n  openedChange = new EventEmitter();\n  /** Event emitted when the select has been opened. */\n  _openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n  /** Event emitted when the select has been closed. */\n  _closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n  /** Event emitted when the selected value has been changed by the user. */\n  selectionChange = new EventEmitter();\n  /**\n   * Event that emits whenever the raw value of the select changes. This is here primarily\n   * to facilitate the two-way binding for the `value` input.\n   * @docs-private\n   */\n  valueChange = new EventEmitter();\n  constructor() {\n    const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n    const parentForm = inject(NgForm, {\n      optional: true\n    });\n    const parentFormGroup = inject(FormGroupDirective, {\n      optional: true\n    });\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    if (this.ngControl) {\n      // Note: we provide the value accessor through here, instead of\n      // the `providers` to avoid running into a circular import.\n      this.ngControl.valueAccessor = this;\n    }\n    // Note that we only want to set this when the defaults pass it in, otherwise it should\n    // stay as `undefined` so that it falls back to the default in the key manager.\n    if (this._defaultOptions?.typeaheadDebounceInterval != null) {\n      this.typeaheadDebounceInterval = this._defaultOptions.typeaheadDebounceInterval;\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n    this._scrollStrategy = this._scrollStrategyFactory();\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n  }\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple);\n    this.stateChanges.next();\n    this._viewportRuler.change().pipe(takeUntil(this._destroy)).subscribe(() => {\n      if (this.panelOpen) {\n        this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n        this._changeDetectorRef.detectChanges();\n      }\n    });\n  }\n  ngAfterContentInit() {\n    this._initialized.next();\n    this._initialized.complete();\n    this._initKeyManager();\n    this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n      event.added.forEach(option => option.select());\n      event.removed.forEach(option => option.deselect());\n    });\n    this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n      this._resetOptions();\n      this._initializeSelection();\n    });\n  }\n  ngDoCheck() {\n    const newAriaLabelledby = this._getTriggerAriaLabelledby();\n    const ngControl = this.ngControl;\n    // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n    // is computed as a result of a content query which can cause this binding to trigger a\n    // \"changed after checked\" error.\n    if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n      const element = this._elementRef.nativeElement;\n      this._triggerAriaLabelledBy = newAriaLabelledby;\n      if (newAriaLabelledby) {\n        element.setAttribute('aria-labelledby', newAriaLabelledby);\n      } else {\n        element.removeAttribute('aria-labelledby');\n      }\n    }\n    if (ngControl) {\n      // The disabled state might go out of sync if the form group is swapped out. See #17860.\n      if (this._previousControl !== ngControl.control) {\n        if (this._previousControl !== undefined && ngControl.disabled !== null && ngControl.disabled !== this.disabled) {\n          this.disabled = ngControl.disabled;\n        }\n        this._previousControl = ngControl.control;\n      }\n      this.updateErrorState();\n    }\n  }\n  ngOnChanges(changes) {\n    // Updating the disabled state is handled by the input, but we need to additionally let\n    // the parent form field know to run change detection when the disabled state changes.\n    if (changes['disabled'] || changes['userAriaDescribedBy']) {\n      this.stateChanges.next();\n    }\n    if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n      this._keyManager.withTypeAhead(this.typeaheadDebounceInterval);\n    }\n  }\n  ngOnDestroy() {\n    this._cleanupDetach?.();\n    this._keyManager?.destroy();\n    this._destroy.next();\n    this._destroy.complete();\n    this.stateChanges.complete();\n    this._clearFromModal();\n  }\n  /** Toggles the overlay panel open or closed. */\n  toggle() {\n    this.panelOpen ? this.close() : this.open();\n  }\n  /** Opens the overlay panel. */\n  open() {\n    if (!this._canOpen()) {\n      return;\n    }\n    // It's important that we read this as late as possible, because doing so earlier will\n    // return a different element since it's based on queries in the form field which may\n    // not have run yet. Also this needs to be assigned before we measure the overlay width.\n    if (this._parentFormField) {\n      this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n    }\n    this._cleanupDetach?.();\n    this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n    this._applyModalPanelOwnership();\n    this._panelOpen = true;\n    this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n      this._changeDetectorRef.detectChanges();\n      this._positioningSettled();\n    });\n    this._overlayDir.attachOverlay();\n    this._keyManager.withHorizontalOrientation(null);\n    this._highlightCorrectOption();\n    this._changeDetectorRef.markForCheck();\n    // Required for the MDC form field to pick up when the overlay has been opened.\n    this.stateChanges.next();\n    // Simulate the animation event before we moved away from `@angular/animations`.\n    Promise.resolve().then(() => this.openedChange.emit(true));\n  }\n  /**\n   * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n   * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n   * panel. Track the modal we have changed so we can undo the changes on destroy.\n   */\n  _trackedModal = null;\n  /**\n   * If the autocomplete trigger is inside of an `aria-modal` element, connect\n   * that modal to the options panel with `aria-owns`.\n   *\n   * For some browser + screen reader combinations, when navigation is inside\n   * of an `aria-modal` element, the screen reader treats everything outside\n   * of that modal as hidden or invisible.\n   *\n   * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n   * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n   * from reaching the panel.\n   *\n   * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n   * the options panel. This effectively communicates to assistive technology that the\n   * options panel is part of the same interaction as the modal.\n   *\n   * At time of this writing, this issue is present in VoiceOver.\n   * See https://github.com/angular/components/issues/20694\n   */\n  _applyModalPanelOwnership() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    if (!modal) {\n      // Most commonly, the autocomplete trigger is not inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n    addAriaReferencedId(modal, 'aria-owns', panelId);\n    this._trackedModal = modal;\n  }\n  /** Clears the reference to the listbox overlay element from the modal it was added to. */\n  _clearFromModal() {\n    if (!this._trackedModal) {\n      // Most commonly, the autocomplete trigger is not used inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    this._trackedModal = null;\n  }\n  /** Closes the overlay panel and focuses the host element. */\n  close() {\n    if (this._panelOpen) {\n      this._panelOpen = false;\n      this._exitAndDetach();\n      this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n      this._changeDetectorRef.markForCheck();\n      this._onTouched();\n      // Required for the MDC form field to pick up when the overlay has been closed.\n      this.stateChanges.next();\n      // Simulate the animation event before we moved away from `@angular/animations`.\n      Promise.resolve().then(() => this.openedChange.emit(false));\n    }\n  }\n  /** Triggers the exit animation and detaches the overlay at the end. */\n  _exitAndDetach() {\n    if (this._animationsDisabled || !this.panel) {\n      this._detachOverlay();\n      return;\n    }\n    this._cleanupDetach?.();\n    this._cleanupDetach = () => {\n      cleanupEvent();\n      clearTimeout(exitFallbackTimer);\n      this._cleanupDetach = undefined;\n    };\n    const panel = this.panel.nativeElement;\n    const cleanupEvent = this._renderer.listen(panel, 'animationend', event => {\n      if (event.animationName === '_mat-select-exit') {\n        this._cleanupDetach?.();\n        this._detachOverlay();\n      }\n    });\n    // Since closing the overlay depends on the animation, we have a fallback in case the panel\n    // doesn't animate. This can happen in some internal tests that do `* {animation: none}`.\n    const exitFallbackTimer = setTimeout(() => {\n      this._cleanupDetach?.();\n      this._detachOverlay();\n    }, 200);\n    panel.classList.add('mat-select-panel-exit');\n  }\n  /** Detaches the current overlay directive. */\n  _detachOverlay() {\n    this._overlayDir.detachOverlay();\n    // Some of the overlay detachment logic depends on change detection.\n    // Mark for check to ensure that things get picked up in a timely manner.\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Sets the select's value. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param value New value to be written to the model.\n   */\n  writeValue(value) {\n    this._assignValue(value);\n  }\n  /**\n   * Saves a callback function to be invoked when the select's value\n   * changes from user input. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the value changes.\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Saves a callback function to be invoked when the select is blurred\n   * by the user. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the component has been touched.\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Disables the select. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param isDisabled Sets whether the component is disabled.\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n  /** Whether or not the overlay panel is open. */\n  get panelOpen() {\n    return this._panelOpen;\n  }\n  /** The currently selected option. */\n  get selected() {\n    return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n  }\n  /** The value displayed in the trigger. */\n  get triggerValue() {\n    if (this.empty) {\n      return '';\n    }\n    if (this._multiple) {\n      const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n      if (this._isRtl()) {\n        selectedOptions.reverse();\n      }\n      // TODO(crisbeto): delimiter should be configurable for proper localization.\n      return selectedOptions.join(', ');\n    }\n    return this._selectionModel.selected[0].viewValue;\n  }\n  /** Refreshes the error state of the select. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** Whether the element is in RTL mode. */\n  _isRtl() {\n    return this._dir ? this._dir.value === 'rtl' : false;\n  }\n  /** Handles all keydown events on the select. */\n  _handleKeydown(event) {\n    if (!this.disabled) {\n      this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n    }\n  }\n  /** Handles keyboard events while the select is closed. */\n  _handleClosedKeydown(event) {\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW || keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW;\n    const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n    const manager = this._keyManager;\n    // Open the select on ALT + arrow key to match the native <select>\n    if (!manager.isTyping() && isOpenKey && !hasModifierKey(event) || (this.multiple || event.altKey) && isArrowKey) {\n      event.preventDefault(); // prevents the page from scrolling down when pressing space\n      this.open();\n    } else if (!this.multiple) {\n      const previouslySelectedOption = this.selected;\n      manager.onKeydown(event);\n      const selectedOption = this.selected;\n      // Since the value has changed, we need to announce it ourselves.\n      if (selectedOption && previouslySelectedOption !== selectedOption) {\n        // We set a duration on the live announcement, because we want the live element to be\n        // cleared after a while so that users can't navigate to it using the arrow keys.\n        this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n      }\n    }\n  }\n  /** Handles keyboard events when the selected is open. */\n  _handleOpenKeydown(event) {\n    const manager = this._keyManager;\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n    const isTyping = manager.isTyping();\n    if (isArrowKey && event.altKey) {\n      // Close the select on ALT + arrow key to match the native <select>\n      event.preventDefault();\n      this.close();\n      // Don't do anything in this case if the user is typing,\n      // because the typing sequence can include the space key.\n    } else if (!isTyping && (keyCode === ENTER || keyCode === SPACE) && manager.activeItem && !hasModifierKey(event)) {\n      event.preventDefault();\n      manager.activeItem._selectViaInteraction();\n    } else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n      event.preventDefault();\n      const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n      this.options.forEach(option => {\n        if (!option.disabled) {\n          hasDeselectedOptions ? option.select() : option.deselect();\n        }\n      });\n    } else {\n      const previouslyFocusedIndex = manager.activeItemIndex;\n      manager.onKeydown(event);\n      if (this._multiple && isArrowKey && event.shiftKey && manager.activeItem && manager.activeItemIndex !== previouslyFocusedIndex) {\n        manager.activeItem._selectViaInteraction();\n      }\n    }\n  }\n  /** Handles keyboard events coming from the overlay. */\n  _handleOverlayKeydown(event) {\n    // TODO(crisbeto): prior to #30363 this was being handled inside the overlay directive, but we\n    // need control over the animation timing so we do it manually. We should remove the `keydown`\n    // listener from `.mat-mdc-select-panel` and handle all the events here. That may cause\n    // further test breakages so it's left for a follow-up.\n    if (event.keyCode === ESCAPE && !hasModifierKey(event)) {\n      event.preventDefault();\n      this.close();\n    }\n  }\n  _onFocus() {\n    if (!this.disabled) {\n      this._focused = true;\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n   * \"blur\" to the panel when it opens, causing a false positive.\n   */\n  _onBlur() {\n    this._focused = false;\n    this._keyManager?.cancelTypeahead();\n    if (!this.disabled && !this.panelOpen) {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n  }\n  /** Returns the theme to be used on the panel. */\n  _getPanelTheme() {\n    return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n  }\n  /** Whether the select has a value. */\n  get empty() {\n    return !this._selectionModel || this._selectionModel.isEmpty();\n  }\n  _initializeSelection() {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl) {\n        this._value = this.ngControl.value;\n      }\n      this._setSelectionByValue(this._value);\n      this.stateChanges.next();\n    });\n  }\n  /**\n   * Sets the selected option based on a value. If no option can be\n   * found with the designated value, the select trigger is cleared.\n   */\n  _setSelectionByValue(value) {\n    this.options.forEach(option => option.setInactiveStyles());\n    this._selectionModel.clear();\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectNonArrayValueError();\n      }\n      value.forEach(currentValue => this._selectOptionByValue(currentValue));\n      this._sortValues();\n    } else {\n      const correspondingOption = this._selectOptionByValue(value);\n      // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what option the user interacted with last.\n      if (correspondingOption) {\n        this._keyManager.updateActiveItem(correspondingOption);\n      } else if (!this.panelOpen) {\n        // Otherwise reset the highlighted option. Note that we only want to do this while\n        // closed, because doing it while open can shift the user's focus unnecessarily.\n        this._keyManager.updateActiveItem(-1);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Finds and selects and option based on its value.\n   * @returns Option that has the corresponding value.\n   */\n  _selectOptionByValue(value) {\n    const correspondingOption = this.options.find(option => {\n      // Skip options that are already in the model. This allows us to handle cases\n      // where the same primitive value is selected multiple times.\n      if (this._selectionModel.isSelected(option)) {\n        return false;\n      }\n      try {\n        // Treat null as a special reset value.\n        return (option.value != null || this.canSelectNullableOptions) && this._compareWith(option.value, value);\n      } catch (error) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          // Notify developers of errors in their comparator.\n          console.warn(error);\n        }\n        return false;\n      }\n    });\n    if (correspondingOption) {\n      this._selectionModel.select(correspondingOption);\n    }\n    return correspondingOption;\n  }\n  /** Assigns a specific value to the select. Returns whether the value has changed. */\n  _assignValue(newValue) {\n    // Always re-assign an array, because it might have been mutated.\n    if (newValue !== this._value || this._multiple && Array.isArray(newValue)) {\n      if (this.options) {\n        this._setSelectionByValue(newValue);\n      }\n      this._value = newValue;\n      return true;\n    }\n    return false;\n  }\n  // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n  // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n  // recommendation.\n  //\n  // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n  // makes a few exceptions for compound widgets.\n  //\n  // From [Developing a Keyboard Interface](\n  // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n  //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n  //   Listbox...\"\n  //\n  // The user can focus disabled options using the keyboard, but the user cannot click disabled\n  // options.\n  _skipPredicate = option => {\n    if (this.panelOpen) {\n      // Support keyboard focusing disabled options in an ARIA listbox.\n      return false;\n    }\n    // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n    // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n    // closed.\n    return option.disabled;\n  };\n  /** Gets how wide the overlay panel should be. */\n  _getOverlayWidth(preferredOrigin) {\n    if (this.panelWidth === 'auto') {\n      const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin ? preferredOrigin.elementRef : preferredOrigin || this._elementRef;\n      return refToMeasure.nativeElement.getBoundingClientRect().width;\n    }\n    return this.panelWidth === null ? '' : this.panelWidth;\n  }\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties() {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n  _initKeyManager() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withTypeAhead(this.typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr').withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(['shiftKey']).skipPredicate(this._skipPredicate);\n    this._keyManager.tabOut.subscribe(() => {\n      if (this.panelOpen) {\n        // Select the active item when tabbing away. This is consistent with how the native\n        // select behaves. Note that we only want to do this in single selection mode.\n        if (!this.multiple && this._keyManager.activeItem) {\n          this._keyManager.activeItem._selectViaInteraction();\n        }\n        // Restore focus to the trigger before closing. Ensures that the focus\n        // position won't be lost if the user got focus into the overlay.\n        this.focus();\n        this.close();\n      }\n    });\n    this._keyManager.change.subscribe(() => {\n      if (this._panelOpen && this.panel) {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n      } else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n        this._keyManager.activeItem._selectViaInteraction();\n      }\n    });\n  }\n  /** Drops current option subscriptions and IDs and resets from scratch. */\n  _resetOptions() {\n    const changedOrDestroyed = merge(this.options.changes, this._destroy);\n    this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n      this._onSelect(event.source, event.isUserInput);\n      if (event.isUserInput && !this.multiple && this._panelOpen) {\n        this.close();\n        this.focus();\n      }\n    });\n    // Listen to changes in the internal state of the options and react accordingly.\n    // Handles cases like the labels of the selected options changing.\n    merge(...this.options.map(option => option._stateChanges)).pipe(takeUntil(changedOrDestroyed)).subscribe(() => {\n      // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n      // be the result of an expression changing. We have to use `detectChanges` in order\n      // to avoid \"changed after checked\" errors (see #14793).\n      this._changeDetectorRef.detectChanges();\n      this.stateChanges.next();\n    });\n  }\n  /** Invoked when an option is clicked. */\n  _onSelect(option, isUserInput) {\n    const wasSelected = this._selectionModel.isSelected(option);\n    if (!this.canSelectNullableOptions && option.value == null && !this._multiple) {\n      option.deselect();\n      this._selectionModel.clear();\n      if (this.value != null) {\n        this._propagateChanges(option.value);\n      }\n    } else {\n      if (wasSelected !== option.selected) {\n        option.selected ? this._selectionModel.select(option) : this._selectionModel.deselect(option);\n      }\n      if (isUserInput) {\n        this._keyManager.setActiveItem(option);\n      }\n      if (this.multiple) {\n        this._sortValues();\n        if (isUserInput) {\n          // In case the user selected the option with their mouse, we\n          // want to restore focus back to the trigger, in order to\n          // prevent the select keyboard controls from clashing with\n          // the ones from `mat-option`.\n          this.focus();\n        }\n      }\n    }\n    if (wasSelected !== this._selectionModel.isSelected(option)) {\n      this._propagateChanges();\n    }\n    this.stateChanges.next();\n  }\n  /** Sorts the selected values in the selected based on their order in the panel. */\n  _sortValues() {\n    if (this.multiple) {\n      const options = this.options.toArray();\n      this._selectionModel.sort((a, b) => {\n        return this.sortComparator ? this.sortComparator(a, b, options) : options.indexOf(a) - options.indexOf(b);\n      });\n      this.stateChanges.next();\n    }\n  }\n  /** Emits change event to set the model value. */\n  _propagateChanges(fallbackValue) {\n    let valueToEmit;\n    if (this.multiple) {\n      valueToEmit = this.selected.map(option => option.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : fallbackValue;\n    }\n    this._value = valueToEmit;\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Highlights the selected item. If no option is selected, it will highlight\n   * the first *enabled* option.\n   */\n  _highlightCorrectOption() {\n    if (this._keyManager) {\n      if (this.empty) {\n        // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n        // because it activates the first option that passes the skip predicate, rather than the\n        // first *enabled* option.\n        let firstEnabledOptionIndex = -1;\n        for (let index = 0; index < this.options.length; index++) {\n          const option = this.options.get(index);\n          if (!option.disabled) {\n            firstEnabledOptionIndex = index;\n            break;\n          }\n        }\n        this._keyManager.setActiveItem(firstEnabledOptionIndex);\n      } else {\n        this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n      }\n    }\n  }\n  /** Whether the panel is allowed to open. */\n  _canOpen() {\n    return !this._panelOpen && !this.disabled && this.options?.length > 0 && !!this._overlayDir;\n  }\n  /** Focuses the select element. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Gets the aria-labelledby for the select panel. */\n  _getPanelAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelId = this._parentFormField?.getLabelId() || null;\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  /** Determines the `aria-activedescendant` to be set on the host. */\n  _getAriaActiveDescendant() {\n    if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n      return this._keyManager.activeItem.id;\n    }\n    return null;\n  }\n  /** Gets the aria-labelledby of the select component trigger. */\n  _getTriggerAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    let value = this._parentFormField?.getLabelId() || '';\n    if (this.ariaLabelledby) {\n      value += ' ' + this.ariaLabelledby;\n    }\n    // The value should not be used for the trigger's aria-labelledby,\n    // but this currently \"breaks\" accessibility tests since they complain\n    // there is no aria-labelledby. This is because they are not setting an\n    // appropriate label on the form field or select.\n    // TODO: remove this conditional after fixing clients by ensuring their\n    // selects have a label applied.\n    if (!value) {\n      value = this._valueId;\n    }\n    return value;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get describedByIds() {\n    const element = this._elementRef.nativeElement;\n    const existingDescribedBy = element.getAttribute('aria-describedby');\n    return existingDescribedBy?.split(' ') || [];\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    this.focus();\n    this.open();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    // Since the panel doesn't overlap the trigger, we\n    // want the label to only float when there's a value.\n    return this.panelOpen || !this.empty || this.focused && !!this.placeholder;\n  }\n  static ɵfac = function MatSelect_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelect)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSelect,\n    selectors: [[\"mat-select\"]],\n    contentQueries: function MatSelect_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MAT_SELECT_TRIGGER, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customTrigger = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n      }\n    },\n    viewQuery: function MatSelect_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(CdkConnectedOverlay, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trigger = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._overlayDir = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"combobox\", \"aria-haspopup\", \"listbox\", 1, \"mat-mdc-select\"],\n    hostVars: 19,\n    hostBindings: function MatSelect_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function MatSelect_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        })(\"focus\", function MatSelect_focus_HostBindingHandler() {\n          return ctx._onFocus();\n        })(\"blur\", function MatSelect_blur_HostBindingHandler() {\n          return ctx._onBlur();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx.panelOpen ? ctx.id + \"-panel\" : null)(\"aria-expanded\", ctx.panelOpen)(\"aria-label\", ctx.ariaLabel || null)(\"aria-required\", ctx.required.toString())(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-activedescendant\", ctx._getAriaActiveDescendant());\n        i0.ɵɵclassProp(\"mat-mdc-select-disabled\", ctx.disabled)(\"mat-mdc-select-invalid\", ctx.errorState)(\"mat-mdc-select-required\", ctx.required)(\"mat-mdc-select-empty\", ctx.empty)(\"mat-mdc-select-multiple\", ctx.multiple);\n      }\n    },\n    inputs: {\n      userAriaDescribedBy: [0, \"aria-describedby\", \"userAriaDescribedBy\"],\n      panelClass: \"panelClass\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n      hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n      placeholder: \"placeholder\",\n      required: [2, \"required\", \"required\", booleanAttribute],\n      multiple: [2, \"multiple\", \"multiple\", booleanAttribute],\n      disableOptionCentering: [2, \"disableOptionCentering\", \"disableOptionCentering\", booleanAttribute],\n      compareWith: \"compareWith\",\n      value: \"value\",\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      errorStateMatcher: \"errorStateMatcher\",\n      typeaheadDebounceInterval: [2, \"typeaheadDebounceInterval\", \"typeaheadDebounceInterval\", numberAttribute],\n      sortComparator: \"sortComparator\",\n      id: \"id\",\n      panelWidth: \"panelWidth\",\n      canSelectNullableOptions: [2, \"canSelectNullableOptions\", \"canSelectNullableOptions\", booleanAttribute]\n    },\n    outputs: {\n      openedChange: \"openedChange\",\n      _openedStream: \"opened\",\n      _closedStream: \"closed\",\n      selectionChange: \"selectionChange\",\n      valueChange: \"valueChange\"\n    },\n    exportAs: [\"matSelect\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatFormFieldControl,\n      useExisting: MatSelect\n    }, {\n      provide: MAT_OPTION_PARENT_COMPONENT,\n      useExisting: MatSelect\n    }]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c3,\n    decls: 11,\n    vars: 9,\n    consts: [[\"fallbackOverlayOrigin\", \"cdkOverlayOrigin\", \"trigger\", \"\"], [\"panel\", \"\"], [\"cdk-overlay-origin\", \"\", 1, \"mat-mdc-select-trigger\", 3, \"click\"], [1, \"mat-mdc-select-value\"], [1, \"mat-mdc-select-placeholder\", \"mat-mdc-select-min-line\"], [1, \"mat-mdc-select-value-text\"], [1, \"mat-mdc-select-arrow-wrapper\"], [1, \"mat-mdc-select-arrow\"], [\"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M7 10l5 5 5-5z\"], [\"cdk-connected-overlay\", \"\", \"cdkConnectedOverlayLockPosition\", \"\", \"cdkConnectedOverlayHasBackdrop\", \"\", \"cdkConnectedOverlayBackdropClass\", \"cdk-overlay-transparent-backdrop\", 3, \"detach\", \"backdropClick\", \"overlayKeydown\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayFlexibleDimensions\"], [1, \"mat-mdc-select-min-line\"], [\"role\", \"listbox\", \"tabindex\", \"-1\", 3, \"keydown\", \"ngClass\"]],\n    template: function MatSelect_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c2);\n        i0.ɵɵelementStart(0, \"div\", 2, 0);\n        i0.ɵɵlistener(\"click\", function MatSelect_Template_div_click_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.open());\n        });\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵconditionalCreate(4, MatSelect_Conditional_4_Template, 2, 1, \"span\", 4)(5, MatSelect_Conditional_5_Template, 3, 1, \"span\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(8, \"svg\", 8);\n        i0.ɵɵelement(9, \"path\", 9);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(10, MatSelect_ng_template_10_Template, 3, 10, \"ng-template\", 10);\n        i0.ɵɵlistener(\"detach\", function MatSelect_Template_ng_template_detach_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.close());\n        })(\"backdropClick\", function MatSelect_Template_ng_template_backdropClick_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.close());\n        })(\"overlayKeydown\", function MatSelect_Template_ng_template_overlayKeydown_10_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleOverlayKeydown($event));\n        });\n      }\n      if (rf & 2) {\n        const fallbackOverlayOrigin_r4 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵattribute(\"id\", ctx._valueId);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.empty ? 4 : 5);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"cdkConnectedOverlayDisableClose\", true)(\"cdkConnectedOverlayPanelClass\", ctx._overlayPanelClass)(\"cdkConnectedOverlayScrollStrategy\", ctx._scrollStrategy)(\"cdkConnectedOverlayOrigin\", ctx._preferredOverlayOrigin || fallbackOverlayOrigin_r4)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayWidth\", ctx._overlayWidth)(\"cdkConnectedOverlayFlexibleDimensions\", true);\n      }\n    },\n    dependencies: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n    styles: [\"@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelect, [{\n    type: Component,\n    args: [{\n      selector: 'mat-select',\n      exportAs: 'matSelect',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'role': 'combobox',\n        'aria-haspopup': 'listbox',\n        'class': 'mat-mdc-select',\n        '[attr.id]': 'id',\n        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n        '[attr.aria-expanded]': 'panelOpen',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.aria-required]': 'required.toString()',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n        '[class.mat-mdc-select-disabled]': 'disabled',\n        '[class.mat-mdc-select-invalid]': 'errorState',\n        '[class.mat-mdc-select-required]': 'required',\n        '[class.mat-mdc-select-empty]': 'empty',\n        '[class.mat-mdc-select-multiple]': 'multiple',\n        '(keydown)': '_handleKeydown($event)',\n        '(focus)': '_onFocus()',\n        '(blur)': '_onBlur()'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }],\n      imports: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n      template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayDisableClose]=\\\"true\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  [cdkConnectedOverlayFlexibleDimensions]=\\\"true\\\"\\n  (detach)=\\\"close()\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (overlayKeydown)=\\\"_handleOverlayKeydown($event)\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [class.mat-select-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}\\n\"]\n    }]\n  }], () => [], {\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }],\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    customTrigger: [{\n      type: ContentChild,\n      args: [MAT_SELECT_TRIGGER]\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    trigger: [{\n      type: ViewChild,\n      args: ['trigger']\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    _overlayDir: [{\n      type: ViewChild,\n      args: [CdkConnectedOverlay]\n    }],\n    panelClass: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableOptionCentering: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    typeaheadDebounceInterval: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    sortComparator: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    panelWidth: [{\n      type: Input\n    }],\n    canSelectNullableOptions: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n  static ɵfac = function MatSelectTrigger_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelectTrigger)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSelectTrigger,\n    selectors: [[\"mat-select-trigger\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_SELECT_TRIGGER,\n      useExisting: MatSelectTrigger\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectTrigger, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-select-trigger',\n      providers: [{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }]\n    }]\n  }], null, null);\n})();\nclass MatSelectModule {\n  static ɵfac = function MatSelectModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelectModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatSelectModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n    imports: [OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatFormFieldModule, MatOptionModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger],\n      exports: [CdkScrollableModule, MatFormFieldModule, MatSelect, MatSelectTrigger, MatOptionModule, MatCommonModule],\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { MatSelectModule as M, MAT_SELECT_SCROLL_STRATEGY as a, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY as b, MAT_SELECT_CONFIG as c, MAT_SELECT_SCROLL_STRATEGY_PROVIDER as d, MAT_SELECT_TRIGGER as e, MatSelectChange as f, MatSelect as g, MatSelectTrigger as h };", "map": {"version": 3, "names": ["createRepositionScrollStrategy", "CdkConnectedOverlay", "CdkOverlayOrigin", "OverlayModule", "i0", "InjectionToken", "inject", "Injector", "ChangeDetectorRef", "ElementRef", "Renderer2", "signal", "EventEmitter", "HostAttributeToken", "booleanAttribute", "numberAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChildren", "ContentChild", "Input", "ViewChild", "Output", "Directive", "NgModule", "ViewportRuler", "CdkScrollableModule", "_IdGenerator", "LiveAnnouncer", "removeAriaReferencedId", "addAriaReferencedId", "ActiveDescendantKeyManager", "Directionality", "SelectionModel", "hasModifierKey", "ENTER", "SPACE", "A", "ESCAPE", "DOWN_ARROW", "UP_ARROW", "LEFT_ARROW", "RIGHT_ARROW", "NgControl", "Validators", "NgForm", "FormGroupDirective", "Subject", "defer", "merge", "startWith", "switchMap", "filter", "map", "takeUntil", "take", "Ng<PERSON><PERSON>", "h", "MAT_FORM_FIELD", "k", "MatFormFieldControl", "_", "_animationsDisabled", "_countGroupLabelsBeforeOption", "b", "_getOptionScrollPosition", "c", "MAT_OPTION_PARENT_COMPONENT", "M", "MatOption", "d", "MAT_OPTGROUP", "E", "ErrorStateMatcher", "_ErrorStateTracker", "MatOptionModule", "MatCommonModule", "MatFormFieldModule", "_c0", "_c1", "_c2", "_c3", "MatSelect_Conditional_4_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r1", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate", "placeholder", "MatSelect_Conditional_5_Conditional_1_Template", "ɵɵprojection", "MatSelect_Conditional_5_Conditional_2_Template", "triggerValue", "MatSelect_Conditional_5_Template", "ɵɵconditionalCreate", "ɵɵconditional", "customTrigger", "MatSelect_ng_template_10_Template", "_r3", "ɵɵgetCurrentView", "ɵɵlistener", "MatSelect_ng_template_10_Template_div_keydown_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "_handleKeydown", "ɵɵclassMap", "ɵɵinterpolate1", "_getPanelTheme", "ɵɵclassProp", "ɵɵproperty", "panelClass", "ɵɵattribute", "id", "multiple", "aria<PERSON><PERSON><PERSON>", "_getPanelAriaLabe<PERSON>by", "getMatSelectDynamicMultipleError", "Error", "getMatSelectNonArrayValueError", "getMatSelectNonFunctionValueError", "MAT_SELECT_SCROLL_STRATEGY", "providedIn", "factory", "injector", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY", "_overlay", "MAT_SELECT_CONFIG", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "MAT_SELECT_TRIGGER", "MatSelectChange", "source", "value", "constructor", "MatSelect", "_viewportRuler", "_changeDetectorRef", "_elementRef", "_dir", "optional", "_idGenerator", "_renderer", "_parentFormField", "ngControl", "self", "_liveAnnouncer", "_defaultOptions", "_initialized", "_cleanupDetach", "options", "optionGroups", "_positions", "originX", "originY", "overlayX", "overlayY", "_scrollOptionIntoView", "index", "option", "toArray", "panel", "nativeElement", "labelCount", "element", "_getHostElement", "scrollTop", "offsetTop", "offsetHeight", "_positioningSettled", "_keyManager", "activeItemIndex", "_getChangeEvent", "_scrollStrategyFactory", "_panelOpen", "_compareWith", "o1", "o2", "_uid", "getId", "_triggerAriaLabelledBy", "_previousControl", "_destroy", "_errorStateTracker", "stateChanges", "disableAutomaticLabeling", "userAriaDescribedBy", "_selectionModel", "_preferredOverlayOrigin", "_overlayWidth", "_onChange", "_onTouched", "_valueId", "_scrollStrategy", "_overlayPanelClass", "overlayPanelClass", "focused", "_focused", "controlType", "trigger", "_overlayDir", "disabled", "disable<PERSON><PERSON><PERSON>", "_disableRipple", "set", "tabIndex", "hideSingleSelectionIndicator", "_hideSingleSelectionIndicator", "_syncParentProperties", "_placeholder", "next", "required", "_required", "control", "hasValidator", "_multiple", "ngDevMode", "disableOptionCentering", "compareWith", "fn", "_initializeSelection", "_value", "newValue", "hasAssigned", "_assignValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errorStateMatcher", "matcher", "typeaheadDebounceInterval", "sortComparator", "_id", "errorState", "panelWidth", "canSelectNullableOptions", "optionSelectionChanges", "changes", "pipe", "onSelectionChange", "openedChange", "_openedStream", "o", "_closedStream", "selectionChange", "valueChange", "defaultErrorStateMatcher", "parentForm", "parentFormGroup", "valueAccessor", "parseInt", "ngOnInit", "change", "subscribe", "panelOpen", "_getOverlayWidth", "detectChanges", "ngAfterContentInit", "complete", "_initKeyManager", "changed", "event", "added", "for<PERSON>ach", "select", "removed", "deselect", "_resetOptions", "ngDoCheck", "newAria<PERSON><PERSON><PERSON><PERSON>", "_getTriggerAriaLabe<PERSON>by", "setAttribute", "removeAttribute", "undefined", "updateErrorState", "ngOnChanges", "withTypeAhead", "ngOnDestroy", "destroy", "_clearFromModal", "toggle", "close", "open", "_canOpen", "getConnectedOverlayOrigin", "_applyModalPanelOwnership", "positionChange", "attachOverlay", "withHorizontalOrientation", "_highlightCorrectOption", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "then", "emit", "_trackedModal", "modal", "closest", "panelId", "_exitAndDetach", "_isRtl", "_detachOverlay", "cleanupEvent", "clearTimeout", "exitFallbackTimer", "listen", "animationName", "setTimeout", "classList", "add", "detachOverlay", "writeValue", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "selected", "empty", "selectedOptions", "viewValue", "reverse", "join", "_handleOpenKeydown", "_handleClosedKeydown", "keyCode", "isArrowKey", "isOpenKey", "manager", "isTyping", "altKey", "preventDefault", "previouslySelectedOption", "onKeydown", "selectedOption", "announce", "activeItem", "_selectViaInteraction", "ctrl<PERSON>ey", "hasDeselectedOptions", "some", "opt", "previouslyFocusedIndex", "shift<PERSON>ey", "_handleOverlayKeydown", "_onFocus", "_onBlur", "cancelTypeahead", "color", "isEmpty", "_setSelectionByValue", "setInactiveStyles", "clear", "Array", "isArray", "currentValue", "_selectOptionByValue", "_sortValues", "correspondingOption", "updateActiveItem", "find", "isSelected", "error", "console", "warn", "_skipPredicate", "preferred<PERSON><PERSON>in", "refToMeasure", "elementRef", "getBoundingClientRect", "width", "withVerticalOrientation", "withHomeAndEnd", "withPageUpDown", "withAllowedModifierKeys", "skipPredicate", "tabOut", "focus", "changedOrDestroyed", "_onSelect", "isUserInput", "_stateChanges", "wasSelected", "_propagateChanges", "setActiveItem", "sort", "a", "indexOf", "fallback<PERSON><PERSON><PERSON>", "valueToEmit", "firstEnabledOptionIndex", "length", "get", "labelId", "getLabelId", "labelExpression", "_getAriaActiveDescendant", "describedByIds", "existingDescribedBy", "getAttribute", "split", "setDescribedByIds", "ids", "onContainerClick", "shouldLabelFloat", "ɵfac", "MatSelect_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "MatSelect_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "MatSelect_Query", "ɵɵviewQuery", "hostAttrs", "hostVars", "hostBindings", "MatSelect_HostBindings", "MatSelect_keydown_HostBindingHandler", "MatSelect_focus_HostBindingHandler", "MatSelect_blur_HostBindingHandler", "toString", "inputs", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "useExisting", "ɵɵNgOnChangesFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatSelect_Template", "_r1", "ɵɵprojectionDef", "MatSelect_Template_div_click_0_listener", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵtemplate", "MatSelect_Template_ng_template_detach_10_listener", "MatSelect_Template_ng_template_backdropClick_10_listener", "MatSelect_Template_ng_template_overlayKeydown_10_listener", "fallbackOverlayOrigin_r4", "ɵɵreference", "dependencies", "styles", "encapsulation", "changeDetection", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "host", "providers", "imports", "descendants", "transform", "MatSelectTrigger", "MatSelectTrigger_Factory", "ɵdir", "ɵɵdefineDirective", "MatSelectModule", "MatSelectModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "e", "f", "g"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/material/fesm2022/module-BDiw_nWS.mjs"], "sourcesContent": ["import { createRepositionScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injector, ChangeDetectorRef, ElementRef, Renderer2, signal, EventEmitter, HostAttributeToken, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, Input, ViewChild, Output, Directive, NgModule } from '@angular/core';\nimport { ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _IdGenerator, LiveAnnouncer, removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { hasModifier<PERSON>ey, ENTER, SPACE, A, ESCAPE, DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW } from '@angular/cdk/keycodes';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, filter, map, takeUntil, take } from 'rxjs/operators';\nimport { NgClass } from '@angular/common';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-C9DZXojn.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition, c as MAT_OPTION_PARENT_COMPONENT, M as MatOption, d as MAT_OPTGROUP } from './option-BzhYL_xC.mjs';\nimport { E as ErrorStateMatcher } from './error-options-DCNQlTOA.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { M as MatOptionModule } from './index-DwiL-HGk.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatFormFieldModule } from './module-DzZHEh7B.mjs';\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n    return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n    return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n    return Error('`compareWith` must be a function.');\n}\n\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const injector = inject(Injector);\n        return () => createRepositionScrollStrategy(injector);\n    },\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(_overlay) {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n    provide: MAT_SELECT_SCROLL_STRATEGY,\n    deps: [],\n    useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n    source;\n    value;\n    constructor(\n    /** Reference to the select that emitted the change event. */\n    source, \n    /** Current value of the select that emitted the event. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\nclass MatSelect {\n    _viewportRuler = inject(ViewportRuler);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _dir = inject(Directionality, { optional: true });\n    _idGenerator = inject(_IdGenerator);\n    _renderer = inject(Renderer2);\n    _parentFormField = inject(MAT_FORM_FIELD, { optional: true });\n    ngControl = inject(NgControl, { self: true, optional: true });\n    _liveAnnouncer = inject(LiveAnnouncer);\n    _defaultOptions = inject(MAT_SELECT_CONFIG, { optional: true });\n    _animationsDisabled = _animationsDisabled();\n    _initialized = new Subject();\n    _cleanupDetach;\n    /** All of the defined select options. */\n    options;\n    // TODO(crisbeto): this is only necessary for the non-MDC select, but it's technically a\n    // public API so we have to keep it. It should be deprecated and removed eventually.\n    /** All of the defined groups of options. */\n    optionGroups;\n    /** User-supplied override of the trigger element. */\n    customTrigger;\n    /**\n     * This position config ensures that the top \"start\" corner of the overlay\n     * is aligned with with the top \"start\" of the origin by default (overlapping\n     * the trigger completely). If the panel cannot fit below the trigger, it\n     * will fall back to a position above the trigger.\n     */\n    _positions = [\n        {\n            originX: 'start',\n            originY: 'bottom',\n            overlayX: 'start',\n            overlayY: 'top',\n        },\n        {\n            originX: 'end',\n            originY: 'bottom',\n            overlayX: 'end',\n            overlayY: 'top',\n        },\n        {\n            originX: 'start',\n            originY: 'top',\n            overlayX: 'start',\n            overlayY: 'bottom',\n            panelClass: 'mat-mdc-select-panel-above',\n        },\n        {\n            originX: 'end',\n            originY: 'top',\n            overlayX: 'end',\n            overlayY: 'bottom',\n            panelClass: 'mat-mdc-select-panel-above',\n        },\n    ];\n    /** Scrolls a particular option into the view. */\n    _scrollOptionIntoView(index) {\n        const option = this.options.toArray()[index];\n        if (option) {\n            const panel = this.panel.nativeElement;\n            const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n            const element = option._getHostElement();\n            if (index === 0 && labelCount === 1) {\n                // If we've got one group label before the option and we're at the top option,\n                // scroll the list to the top. This is better UX than scrolling the list to the\n                // top of the option, because it allows the user to read the top group's label.\n                panel.scrollTop = 0;\n            }\n            else {\n                panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n            }\n        }\n    }\n    /** Called when the panel has been opened and the overlay has settled on its final position. */\n    _positioningSettled() {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n    }\n    /** Creates a change event object that should be emitted by the select. */\n    _getChangeEvent(value) {\n        return new MatSelectChange(this, value);\n    }\n    /** Factory function used to create a scroll strategy for this select. */\n    _scrollStrategyFactory = inject(MAT_SELECT_SCROLL_STRATEGY);\n    /** Whether or not the overlay panel is open. */\n    _panelOpen = false;\n    /** Comparison function to specify which option is displayed. Defaults to object equality. */\n    _compareWith = (o1, o2) => o1 === o2;\n    /** Unique id for this input. */\n    _uid = this._idGenerator.getId('mat-select-');\n    /** Current `aria-labelledby` value for the select trigger. */\n    _triggerAriaLabelledBy = null;\n    /**\n     * Keeps track of the previous form control assigned to the select.\n     * Used to detect if it has changed.\n     */\n    _previousControl;\n    /** Emits whenever the component is destroyed. */\n    _destroy = new Subject();\n    /** Tracks the error state of the select. */\n    _errorStateTracker;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    stateChanges = new Subject();\n    /**\n     * Disable the automatic labeling to avoid issues like #27241.\n     * @docs-private\n     */\n    disableAutomaticLabeling = true;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    userAriaDescribedBy;\n    /** Deals with the selection logic. */\n    _selectionModel;\n    /** Manages keyboard events for options in the panel. */\n    _keyManager;\n    /** Ideal origin for the overlay panel. */\n    _preferredOverlayOrigin;\n    /** Width of the overlay panel. */\n    _overlayWidth;\n    /** `View -> model callback called when value changes` */\n    _onChange = () => { };\n    /** `View -> model callback called when select has been touched` */\n    _onTouched = () => { };\n    /** ID for the DOM node containing the select's value. */\n    _valueId = this._idGenerator.getId('mat-select-value-');\n    /** Strategy that will be used to handle scrolling while the select panel is open. */\n    _scrollStrategy;\n    _overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n    /** Whether the select is focused. */\n    get focused() {\n        return this._focused || this._panelOpen;\n    }\n    _focused = false;\n    /** A name for this control that can be used by `mat-form-field`. */\n    controlType = 'mat-select';\n    /** Trigger that opens the select. */\n    trigger;\n    /** Panel containing the select options. */\n    panel;\n    /** Overlay pane containing the options. */\n    _overlayDir;\n    /** Classes to be passed to the select panel. Supports the same syntax as `ngClass`. */\n    panelClass;\n    /** Whether the select is disabled. */\n    disabled = false;\n    /** Whether ripples in the select are disabled. */\n    get disableRipple() {\n        return this._disableRipple();\n    }\n    set disableRipple(value) {\n        this._disableRipple.set(value);\n    }\n    _disableRipple = signal(false);\n    /** Tab index of the select. */\n    tabIndex = 0;\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = value;\n        this._syncParentProperties();\n    }\n    _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    /** Placeholder to be shown if no value has been selected. */\n    get placeholder() {\n        return this._placeholder;\n    }\n    set placeholder(value) {\n        this._placeholder = value;\n        this.stateChanges.next();\n    }\n    _placeholder;\n    /** Whether the component is required. */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = value;\n        this.stateChanges.next();\n    }\n    _required;\n    /** Whether the user should be allowed to select multiple options. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectDynamicMultipleError();\n        }\n        this._multiple = value;\n    }\n    _multiple = false;\n    /** Whether to center the active option over the trigger. */\n    disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n    /**\n     * Function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    get compareWith() {\n        return this._compareWith;\n    }\n    set compareWith(fn) {\n        if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectNonFunctionValueError();\n        }\n        this._compareWith = fn;\n        if (this._selectionModel) {\n            // A different comparator means the selection could change.\n            this._initializeSelection();\n        }\n    }\n    /** Value of the select control. */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        const hasAssigned = this._assignValue(newValue);\n        if (hasAssigned) {\n            this._onChange(newValue);\n        }\n    }\n    _value;\n    /** Aria label of the select. */\n    ariaLabel = '';\n    /** Input that can be used to specify the `aria-labelledby` attribute. */\n    ariaLabelledby;\n    /** Object used to control when error messages are shown. */\n    get errorStateMatcher() {\n        return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n        this._errorStateTracker.matcher = value;\n    }\n    /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n    typeaheadDebounceInterval;\n    /**\n     * Function used to sort the values in a select in multiple mode.\n     * Follows the same logic as `Array.prototype.sort`.\n     */\n    sortComparator;\n    /** Unique id of the element. */\n    get id() {\n        return this._id;\n    }\n    set id(value) {\n        this._id = value || this._uid;\n        this.stateChanges.next();\n    }\n    _id;\n    /** Whether the select is in an error state. */\n    get errorState() {\n        return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n        this._errorStateTracker.errorState = value;\n    }\n    /**\n     * Width of the panel. If set to `auto`, the panel will match the trigger width.\n     * If set to null or an empty string, the panel will grow to match the longest option's text.\n     */\n    panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined'\n        ? this._defaultOptions.panelWidth\n        : 'auto';\n    /**\n     * By default selecting an option with a `null` or `undefined` value will reset the select's\n     * value. Enable this option if the reset behavior doesn't match your requirements and instead\n     * the nullable options should become selected. The value of this input can be controlled app-wide\n     * using the `MAT_SELECT_CONFIG` injection token.\n     */\n    canSelectNullableOptions = this._defaultOptions?.canSelectNullableOptions ?? false;\n    /** Combined stream of all of the child options' change events. */\n    optionSelectionChanges = defer(() => {\n        const options = this.options;\n        if (options) {\n            return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n        }\n        return this._initialized.pipe(switchMap(() => this.optionSelectionChanges));\n    });\n    /** Event emitted when the select panel has been toggled. */\n    openedChange = new EventEmitter();\n    /** Event emitted when the select has been opened. */\n    _openedStream = this.openedChange.pipe(filter(o => o), map(() => { }));\n    /** Event emitted when the select has been closed. */\n    _closedStream = this.openedChange.pipe(filter(o => !o), map(() => { }));\n    /** Event emitted when the selected value has been changed by the user. */\n    selectionChange = new EventEmitter();\n    /**\n     * Event that emits whenever the raw value of the select changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n    valueChange = new EventEmitter();\n    constructor() {\n        const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n        const parentForm = inject(NgForm, { optional: true });\n        const parentFormGroup = inject(FormGroupDirective, { optional: true });\n        const tabIndex = inject(new HostAttributeToken('tabindex'), { optional: true });\n        if (this.ngControl) {\n            // Note: we provide the value accessor through here, instead of\n            // the `providers` to avoid running into a circular import.\n            this.ngControl.valueAccessor = this;\n        }\n        // Note that we only want to set this when the defaults pass it in, otherwise it should\n        // stay as `undefined` so that it falls back to the default in the key manager.\n        if (this._defaultOptions?.typeaheadDebounceInterval != null) {\n            this.typeaheadDebounceInterval = this._defaultOptions.typeaheadDebounceInterval;\n        }\n        this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n        this._scrollStrategy = this._scrollStrategyFactory();\n        this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n        // Force setter to be called in case id was not specified.\n        this.id = this.id;\n    }\n    ngOnInit() {\n        this._selectionModel = new SelectionModel(this.multiple);\n        this.stateChanges.next();\n        this._viewportRuler\n            .change()\n            .pipe(takeUntil(this._destroy))\n            .subscribe(() => {\n            if (this.panelOpen) {\n                this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n                this._changeDetectorRef.detectChanges();\n            }\n        });\n    }\n    ngAfterContentInit() {\n        this._initialized.next();\n        this._initialized.complete();\n        this._initKeyManager();\n        this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n            event.added.forEach(option => option.select());\n            event.removed.forEach(option => option.deselect());\n        });\n        this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n            this._resetOptions();\n            this._initializeSelection();\n        });\n    }\n    ngDoCheck() {\n        const newAriaLabelledby = this._getTriggerAriaLabelledby();\n        const ngControl = this.ngControl;\n        // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n        // is computed as a result of a content query which can cause this binding to trigger a\n        // \"changed after checked\" error.\n        if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n            const element = this._elementRef.nativeElement;\n            this._triggerAriaLabelledBy = newAriaLabelledby;\n            if (newAriaLabelledby) {\n                element.setAttribute('aria-labelledby', newAriaLabelledby);\n            }\n            else {\n                element.removeAttribute('aria-labelledby');\n            }\n        }\n        if (ngControl) {\n            // The disabled state might go out of sync if the form group is swapped out. See #17860.\n            if (this._previousControl !== ngControl.control) {\n                if (this._previousControl !== undefined &&\n                    ngControl.disabled !== null &&\n                    ngControl.disabled !== this.disabled) {\n                    this.disabled = ngControl.disabled;\n                }\n                this._previousControl = ngControl.control;\n            }\n            this.updateErrorState();\n        }\n    }\n    ngOnChanges(changes) {\n        // Updating the disabled state is handled by the input, but we need to additionally let\n        // the parent form field know to run change detection when the disabled state changes.\n        if (changes['disabled'] || changes['userAriaDescribedBy']) {\n            this.stateChanges.next();\n        }\n        if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n            this._keyManager.withTypeAhead(this.typeaheadDebounceInterval);\n        }\n    }\n    ngOnDestroy() {\n        this._cleanupDetach?.();\n        this._keyManager?.destroy();\n        this._destroy.next();\n        this._destroy.complete();\n        this.stateChanges.complete();\n        this._clearFromModal();\n    }\n    /** Toggles the overlay panel open or closed. */\n    toggle() {\n        this.panelOpen ? this.close() : this.open();\n    }\n    /** Opens the overlay panel. */\n    open() {\n        if (!this._canOpen()) {\n            return;\n        }\n        // It's important that we read this as late as possible, because doing so earlier will\n        // return a different element since it's based on queries in the form field which may\n        // not have run yet. Also this needs to be assigned before we measure the overlay width.\n        if (this._parentFormField) {\n            this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n        }\n        this._cleanupDetach?.();\n        this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n        this._applyModalPanelOwnership();\n        this._panelOpen = true;\n        this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n            this._changeDetectorRef.detectChanges();\n            this._positioningSettled();\n        });\n        this._overlayDir.attachOverlay();\n        this._keyManager.withHorizontalOrientation(null);\n        this._highlightCorrectOption();\n        this._changeDetectorRef.markForCheck();\n        // Required for the MDC form field to pick up when the overlay has been opened.\n        this.stateChanges.next();\n        // Simulate the animation event before we moved away from `@angular/animations`.\n        Promise.resolve().then(() => this.openedChange.emit(true));\n    }\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    _trackedModal = null;\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        if (!modal) {\n            // Most commonly, the autocomplete trigger is not inside a modal.\n            return;\n        }\n        const panelId = `${this.id}-panel`;\n        if (this._trackedModal) {\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n        addAriaReferencedId(modal, 'aria-owns', panelId);\n        this._trackedModal = modal;\n    }\n    /** Clears the reference to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n        if (!this._trackedModal) {\n            // Most commonly, the autocomplete trigger is not used inside a modal.\n            return;\n        }\n        const panelId = `${this.id}-panel`;\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        this._trackedModal = null;\n    }\n    /** Closes the overlay panel and focuses the host element. */\n    close() {\n        if (this._panelOpen) {\n            this._panelOpen = false;\n            this._exitAndDetach();\n            this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n            this._changeDetectorRef.markForCheck();\n            this._onTouched();\n            // Required for the MDC form field to pick up when the overlay has been closed.\n            this.stateChanges.next();\n            // Simulate the animation event before we moved away from `@angular/animations`.\n            Promise.resolve().then(() => this.openedChange.emit(false));\n        }\n    }\n    /** Triggers the exit animation and detaches the overlay at the end. */\n    _exitAndDetach() {\n        if (this._animationsDisabled || !this.panel) {\n            this._detachOverlay();\n            return;\n        }\n        this._cleanupDetach?.();\n        this._cleanupDetach = () => {\n            cleanupEvent();\n            clearTimeout(exitFallbackTimer);\n            this._cleanupDetach = undefined;\n        };\n        const panel = this.panel.nativeElement;\n        const cleanupEvent = this._renderer.listen(panel, 'animationend', (event) => {\n            if (event.animationName === '_mat-select-exit') {\n                this._cleanupDetach?.();\n                this._detachOverlay();\n            }\n        });\n        // Since closing the overlay depends on the animation, we have a fallback in case the panel\n        // doesn't animate. This can happen in some internal tests that do `* {animation: none}`.\n        const exitFallbackTimer = setTimeout(() => {\n            this._cleanupDetach?.();\n            this._detachOverlay();\n        }, 200);\n        panel.classList.add('mat-select-panel-exit');\n    }\n    /** Detaches the current overlay directive. */\n    _detachOverlay() {\n        this._overlayDir.detachOverlay();\n        // Some of the overlay detachment logic depends on change detection.\n        // Mark for check to ensure that things get picked up in a timely manner.\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Sets the select's value. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param value New value to be written to the model.\n     */\n    writeValue(value) {\n        this._assignValue(value);\n    }\n    /**\n     * Saves a callback function to be invoked when the select's value\n     * changes from user input. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the value changes.\n     */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /**\n     * Saves a callback function to be invoked when the select is blurred\n     * by the user. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the component has been touched.\n     */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /**\n     * Disables the select. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param isDisabled Sets whether the component is disabled.\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n    }\n    /** Whether or not the overlay panel is open. */\n    get panelOpen() {\n        return this._panelOpen;\n    }\n    /** The currently selected option. */\n    get selected() {\n        return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The value displayed in the trigger. */\n    get triggerValue() {\n        if (this.empty) {\n            return '';\n        }\n        if (this._multiple) {\n            const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n            if (this._isRtl()) {\n                selectedOptions.reverse();\n            }\n            // TODO(crisbeto): delimiter should be configurable for proper localization.\n            return selectedOptions.join(', ');\n        }\n        return this._selectionModel.selected[0].viewValue;\n    }\n    /** Refreshes the error state of the select. */\n    updateErrorState() {\n        this._errorStateTracker.updateErrorState();\n    }\n    /** Whether the element is in RTL mode. */\n    _isRtl() {\n        return this._dir ? this._dir.value === 'rtl' : false;\n    }\n    /** Handles all keydown events on the select. */\n    _handleKeydown(event) {\n        if (!this.disabled) {\n            this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n        }\n    }\n    /** Handles keyboard events while the select is closed. */\n    _handleClosedKeydown(event) {\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW ||\n            keyCode === UP_ARROW ||\n            keyCode === LEFT_ARROW ||\n            keyCode === RIGHT_ARROW;\n        const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n        const manager = this._keyManager;\n        // Open the select on ALT + arrow key to match the native <select>\n        if ((!manager.isTyping() && isOpenKey && !hasModifierKey(event)) ||\n            ((this.multiple || event.altKey) && isArrowKey)) {\n            event.preventDefault(); // prevents the page from scrolling down when pressing space\n            this.open();\n        }\n        else if (!this.multiple) {\n            const previouslySelectedOption = this.selected;\n            manager.onKeydown(event);\n            const selectedOption = this.selected;\n            // Since the value has changed, we need to announce it ourselves.\n            if (selectedOption && previouslySelectedOption !== selectedOption) {\n                // We set a duration on the live announcement, because we want the live element to be\n                // cleared after a while so that users can't navigate to it using the arrow keys.\n                this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n            }\n        }\n    }\n    /** Handles keyboard events when the selected is open. */\n    _handleOpenKeydown(event) {\n        const manager = this._keyManager;\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n        const isTyping = manager.isTyping();\n        if (isArrowKey && event.altKey) {\n            // Close the select on ALT + arrow key to match the native <select>\n            event.preventDefault();\n            this.close();\n            // Don't do anything in this case if the user is typing,\n            // because the typing sequence can include the space key.\n        }\n        else if (!isTyping &&\n            (keyCode === ENTER || keyCode === SPACE) &&\n            manager.activeItem &&\n            !hasModifierKey(event)) {\n            event.preventDefault();\n            manager.activeItem._selectViaInteraction();\n        }\n        else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n            event.preventDefault();\n            const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n            this.options.forEach(option => {\n                if (!option.disabled) {\n                    hasDeselectedOptions ? option.select() : option.deselect();\n                }\n            });\n        }\n        else {\n            const previouslyFocusedIndex = manager.activeItemIndex;\n            manager.onKeydown(event);\n            if (this._multiple &&\n                isArrowKey &&\n                event.shiftKey &&\n                manager.activeItem &&\n                manager.activeItemIndex !== previouslyFocusedIndex) {\n                manager.activeItem._selectViaInteraction();\n            }\n        }\n    }\n    /** Handles keyboard events coming from the overlay. */\n    _handleOverlayKeydown(event) {\n        // TODO(crisbeto): prior to #30363 this was being handled inside the overlay directive, but we\n        // need control over the animation timing so we do it manually. We should remove the `keydown`\n        // listener from `.mat-mdc-select-panel` and handle all the events here. That may cause\n        // further test breakages so it's left for a follow-up.\n        if (event.keyCode === ESCAPE && !hasModifierKey(event)) {\n            event.preventDefault();\n            this.close();\n        }\n    }\n    _onFocus() {\n        if (!this.disabled) {\n            this._focused = true;\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n     * \"blur\" to the panel when it opens, causing a false positive.\n     */\n    _onBlur() {\n        this._focused = false;\n        this._keyManager?.cancelTypeahead();\n        if (!this.disabled && !this.panelOpen) {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n            this.stateChanges.next();\n        }\n    }\n    /** Returns the theme to be used on the panel. */\n    _getPanelTheme() {\n        return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n    }\n    /** Whether the select has a value. */\n    get empty() {\n        return !this._selectionModel || this._selectionModel.isEmpty();\n    }\n    _initializeSelection() {\n        // Defer setting the value in order to avoid the \"Expression\n        // has changed after it was checked\" errors from Angular.\n        Promise.resolve().then(() => {\n            if (this.ngControl) {\n                this._value = this.ngControl.value;\n            }\n            this._setSelectionByValue(this._value);\n            this.stateChanges.next();\n        });\n    }\n    /**\n     * Sets the selected option based on a value. If no option can be\n     * found with the designated value, the select trigger is cleared.\n     */\n    _setSelectionByValue(value) {\n        this.options.forEach(option => option.setInactiveStyles());\n        this._selectionModel.clear();\n        if (this.multiple && value) {\n            if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getMatSelectNonArrayValueError();\n            }\n            value.forEach((currentValue) => this._selectOptionByValue(currentValue));\n            this._sortValues();\n        }\n        else {\n            const correspondingOption = this._selectOptionByValue(value);\n            // Shift focus to the active item. Note that we shouldn't do this in multiple\n            // mode, because we don't know what option the user interacted with last.\n            if (correspondingOption) {\n                this._keyManager.updateActiveItem(correspondingOption);\n            }\n            else if (!this.panelOpen) {\n                // Otherwise reset the highlighted option. Note that we only want to do this while\n                // closed, because doing it while open can shift the user's focus unnecessarily.\n                this._keyManager.updateActiveItem(-1);\n            }\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Finds and selects and option based on its value.\n     * @returns Option that has the corresponding value.\n     */\n    _selectOptionByValue(value) {\n        const correspondingOption = this.options.find((option) => {\n            // Skip options that are already in the model. This allows us to handle cases\n            // where the same primitive value is selected multiple times.\n            if (this._selectionModel.isSelected(option)) {\n                return false;\n            }\n            try {\n                // Treat null as a special reset value.\n                return ((option.value != null || this.canSelectNullableOptions) &&\n                    this._compareWith(option.value, value));\n            }\n            catch (error) {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    // Notify developers of errors in their comparator.\n                    console.warn(error);\n                }\n                return false;\n            }\n        });\n        if (correspondingOption) {\n            this._selectionModel.select(correspondingOption);\n        }\n        return correspondingOption;\n    }\n    /** Assigns a specific value to the select. Returns whether the value has changed. */\n    _assignValue(newValue) {\n        // Always re-assign an array, because it might have been mutated.\n        if (newValue !== this._value || (this._multiple && Array.isArray(newValue))) {\n            if (this.options) {\n                this._setSelectionByValue(newValue);\n            }\n            this._value = newValue;\n            return true;\n        }\n        return false;\n    }\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    _skipPredicate = (option) => {\n        if (this.panelOpen) {\n            // Support keyboard focusing disabled options in an ARIA listbox.\n            return false;\n        }\n        // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n        // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n        // closed.\n        return option.disabled;\n    };\n    /** Gets how wide the overlay panel should be. */\n    _getOverlayWidth(preferredOrigin) {\n        if (this.panelWidth === 'auto') {\n            const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin\n                ? preferredOrigin.elementRef\n                : preferredOrigin || this._elementRef;\n            return refToMeasure.nativeElement.getBoundingClientRect().width;\n        }\n        return this.panelWidth === null ? '' : this.panelWidth;\n    }\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n        if (this.options) {\n            for (const option of this.options) {\n                option._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n    _initKeyManager() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options)\n            .withTypeAhead(this.typeaheadDebounceInterval)\n            .withVerticalOrientation()\n            .withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr')\n            .withHomeAndEnd()\n            .withPageUpDown()\n            .withAllowedModifierKeys(['shiftKey'])\n            .skipPredicate(this._skipPredicate);\n        this._keyManager.tabOut.subscribe(() => {\n            if (this.panelOpen) {\n                // Select the active item when tabbing away. This is consistent with how the native\n                // select behaves. Note that we only want to do this in single selection mode.\n                if (!this.multiple && this._keyManager.activeItem) {\n                    this._keyManager.activeItem._selectViaInteraction();\n                }\n                // Restore focus to the trigger before closing. Ensures that the focus\n                // position won't be lost if the user got focus into the overlay.\n                this.focus();\n                this.close();\n            }\n        });\n        this._keyManager.change.subscribe(() => {\n            if (this._panelOpen && this.panel) {\n                this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n            }\n            else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n                this._keyManager.activeItem._selectViaInteraction();\n            }\n        });\n    }\n    /** Drops current option subscriptions and IDs and resets from scratch. */\n    _resetOptions() {\n        const changedOrDestroyed = merge(this.options.changes, this._destroy);\n        this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n            this._onSelect(event.source, event.isUserInput);\n            if (event.isUserInput && !this.multiple && this._panelOpen) {\n                this.close();\n                this.focus();\n            }\n        });\n        // Listen to changes in the internal state of the options and react accordingly.\n        // Handles cases like the labels of the selected options changing.\n        merge(...this.options.map(option => option._stateChanges))\n            .pipe(takeUntil(changedOrDestroyed))\n            .subscribe(() => {\n            // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n            // be the result of an expression changing. We have to use `detectChanges` in order\n            // to avoid \"changed after checked\" errors (see #14793).\n            this._changeDetectorRef.detectChanges();\n            this.stateChanges.next();\n        });\n    }\n    /** Invoked when an option is clicked. */\n    _onSelect(option, isUserInput) {\n        const wasSelected = this._selectionModel.isSelected(option);\n        if (!this.canSelectNullableOptions && option.value == null && !this._multiple) {\n            option.deselect();\n            this._selectionModel.clear();\n            if (this.value != null) {\n                this._propagateChanges(option.value);\n            }\n        }\n        else {\n            if (wasSelected !== option.selected) {\n                option.selected\n                    ? this._selectionModel.select(option)\n                    : this._selectionModel.deselect(option);\n            }\n            if (isUserInput) {\n                this._keyManager.setActiveItem(option);\n            }\n            if (this.multiple) {\n                this._sortValues();\n                if (isUserInput) {\n                    // In case the user selected the option with their mouse, we\n                    // want to restore focus back to the trigger, in order to\n                    // prevent the select keyboard controls from clashing with\n                    // the ones from `mat-option`.\n                    this.focus();\n                }\n            }\n        }\n        if (wasSelected !== this._selectionModel.isSelected(option)) {\n            this._propagateChanges();\n        }\n        this.stateChanges.next();\n    }\n    /** Sorts the selected values in the selected based on their order in the panel. */\n    _sortValues() {\n        if (this.multiple) {\n            const options = this.options.toArray();\n            this._selectionModel.sort((a, b) => {\n                return this.sortComparator\n                    ? this.sortComparator(a, b, options)\n                    : options.indexOf(a) - options.indexOf(b);\n            });\n            this.stateChanges.next();\n        }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges(fallbackValue) {\n        let valueToEmit;\n        if (this.multiple) {\n            valueToEmit = this.selected.map(option => option.value);\n        }\n        else {\n            valueToEmit = this.selected ? this.selected.value : fallbackValue;\n        }\n        this._value = valueToEmit;\n        this.valueChange.emit(valueToEmit);\n        this._onChange(valueToEmit);\n        this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Highlights the selected item. If no option is selected, it will highlight\n     * the first *enabled* option.\n     */\n    _highlightCorrectOption() {\n        if (this._keyManager) {\n            if (this.empty) {\n                // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n                // because it activates the first option that passes the skip predicate, rather than the\n                // first *enabled* option.\n                let firstEnabledOptionIndex = -1;\n                for (let index = 0; index < this.options.length; index++) {\n                    const option = this.options.get(index);\n                    if (!option.disabled) {\n                        firstEnabledOptionIndex = index;\n                        break;\n                    }\n                }\n                this._keyManager.setActiveItem(firstEnabledOptionIndex);\n            }\n            else {\n                this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n            }\n        }\n    }\n    /** Whether the panel is allowed to open. */\n    _canOpen() {\n        return !this._panelOpen && !this.disabled && this.options?.length > 0 && !!this._overlayDir;\n    }\n    /** Focuses the select element. */\n    focus(options) {\n        this._elementRef.nativeElement.focus(options);\n    }\n    /** Gets the aria-labelledby for the select panel. */\n    _getPanelAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId() || null;\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Determines the `aria-activedescendant` to be set on the host. */\n    _getAriaActiveDescendant() {\n        if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n            return this._keyManager.activeItem.id;\n        }\n        return null;\n    }\n    /** Gets the aria-labelledby of the select component trigger. */\n    _getTriggerAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        let value = this._parentFormField?.getLabelId() || '';\n        if (this.ariaLabelledby) {\n            value += ' ' + this.ariaLabelledby;\n        }\n        // The value should not be used for the trigger's aria-labelledby,\n        // but this currently \"breaks\" accessibility tests since they complain\n        // there is no aria-labelledby. This is because they are not setting an\n        // appropriate label on the form field or select.\n        // TODO: remove this conditional after fixing clients by ensuring their\n        // selects have a label applied.\n        if (!value) {\n            value = this._valueId;\n        }\n        return value;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get describedByIds() {\n        const element = this._elementRef.nativeElement;\n        const existingDescribedBy = element.getAttribute('aria-describedby');\n        return existingDescribedBy?.split(' ') || [];\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        if (ids.length) {\n            this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            this._elementRef.nativeElement.removeAttribute('aria-describedby');\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n        this.focus();\n        this.open();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        // Since the panel doesn't overlap the trigger, we\n        // want the label to only float when there's a value.\n        return this.panelOpen || !this.empty || (this.focused && !!this.placeholder);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSelect, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatSelect, isStandalone: true, selector: \"mat-select\", inputs: { userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"], panelClass: \"panelClass\", disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))], hideSingleSelectionIndicator: [\"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute], placeholder: \"placeholder\", required: [\"required\", \"required\", booleanAttribute], multiple: [\"multiple\", \"multiple\", booleanAttribute], disableOptionCentering: [\"disableOptionCentering\", \"disableOptionCentering\", booleanAttribute], compareWith: \"compareWith\", value: \"value\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], errorStateMatcher: \"errorStateMatcher\", typeaheadDebounceInterval: [\"typeaheadDebounceInterval\", \"typeaheadDebounceInterval\", numberAttribute], sortComparator: \"sortComparator\", id: \"id\", panelWidth: \"panelWidth\", canSelectNullableOptions: [\"canSelectNullableOptions\", \"canSelectNullableOptions\", booleanAttribute] }, outputs: { openedChange: \"openedChange\", _openedStream: \"opened\", _closedStream: \"closed\", selectionChange: \"selectionChange\", valueChange: \"valueChange\" }, host: { attributes: { \"role\": \"combobox\", \"aria-haspopup\": \"listbox\" }, listeners: { \"keydown\": \"_handleKeydown($event)\", \"focus\": \"_onFocus()\", \"blur\": \"_onBlur()\" }, properties: { \"attr.id\": \"id\", \"attr.tabindex\": \"disabled ? -1 : tabIndex\", \"attr.aria-controls\": \"panelOpen ? id + \\\"-panel\\\" : null\", \"attr.aria-expanded\": \"panelOpen\", \"attr.aria-label\": \"ariaLabel || null\", \"attr.aria-required\": \"required.toString()\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-invalid\": \"errorState\", \"attr.aria-activedescendant\": \"_getAriaActiveDescendant()\", \"class.mat-mdc-select-disabled\": \"disabled\", \"class.mat-mdc-select-invalid\": \"errorState\", \"class.mat-mdc-select-required\": \"required\", \"class.mat-mdc-select-empty\": \"empty\", \"class.mat-mdc-select-multiple\": \"multiple\" }, classAttribute: \"mat-mdc-select\" }, providers: [\n            { provide: MatFormFieldControl, useExisting: MatSelect },\n            { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n        ], queries: [{ propertyName: \"customTrigger\", first: true, predicate: MAT_SELECT_TRIGGER, descendants: true }, { propertyName: \"options\", predicate: MatOption, descendants: true }, { propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }], viewQueries: [{ propertyName: \"trigger\", first: true, predicate: [\"trigger\"], descendants: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }, { propertyName: \"_overlayDir\", first: true, predicate: CdkConnectedOverlay, descendants: true }], exportAs: [\"matSelect\"], usesOnChanges: true, ngImport: i0, template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayDisableClose]=\\\"true\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  [cdkConnectedOverlayFlexibleDimensions]=\\\"true\\\"\\n  (detach)=\\\"close()\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (overlayKeydown)=\\\"_handleOverlayKeydown($event)\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [class.mat-select-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\"@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}\\n\"], dependencies: [{ kind: \"directive\", type: CdkOverlayOrigin, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"] }, { kind: \"directive\", type: CdkConnectedOverlay, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: [\"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPositionStrategy\", \"cdkConnectedOverlayOffsetX\", \"cdkConnectedOverlayOffsetY\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayHeight\", \"cdkConnectedOverlayMinWidth\", \"cdkConnectedOverlayMinHeight\", \"cdkConnectedOverlayBackdropClass\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayViewportMargin\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayTransformOriginOn\", \"cdkConnectedOverlayHasBackdrop\", \"cdkConnectedOverlayLockPosition\", \"cdkConnectedOverlayFlexibleDimensions\", \"cdkConnectedOverlayGrowAfterOpen\", \"cdkConnectedOverlayPush\", \"cdkConnectedOverlayDisposeOnNavigation\"], outputs: [\"backdropClick\", \"positionChange\", \"attach\", \"detach\", \"overlayKeydown\", \"overlayOutsideClick\"], exportAs: [\"cdkConnectedOverlay\"] }, { kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSelect, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-select', exportAs: 'matSelect', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'role': 'combobox',\n                        'aria-haspopup': 'listbox',\n                        'class': 'mat-mdc-select',\n                        '[attr.id]': 'id',\n                        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n                        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n                        '[attr.aria-expanded]': 'panelOpen',\n                        '[attr.aria-label]': 'ariaLabel || null',\n                        '[attr.aria-required]': 'required.toString()',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-invalid]': 'errorState',\n                        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n                        '[class.mat-mdc-select-disabled]': 'disabled',\n                        '[class.mat-mdc-select-invalid]': 'errorState',\n                        '[class.mat-mdc-select-required]': 'required',\n                        '[class.mat-mdc-select-empty]': 'empty',\n                        '[class.mat-mdc-select-multiple]': 'multiple',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(focus)': '_onFocus()',\n                        '(blur)': '_onBlur()',\n                    }, providers: [\n                        { provide: MatFormFieldControl, useExisting: MatSelect },\n                        { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n                    ], imports: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass], template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayDisableClose]=\\\"true\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  [cdkConnectedOverlayFlexibleDimensions]=\\\"true\\\"\\n  (detach)=\\\"close()\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (overlayKeydown)=\\\"_handleOverlayKeydown($event)\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [class.mat-select-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\"@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }], optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], customTrigger: [{\n                type: ContentChild,\n                args: [MAT_SELECT_TRIGGER]\n            }], userAriaDescribedBy: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], trigger: [{\n                type: ViewChild,\n                args: ['trigger']\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], _overlayDir: [{\n                type: ViewChild,\n                args: [CdkConnectedOverlay]\n            }], panelClass: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], hideSingleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], placeholder: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], multiple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableOptionCentering: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], compareWith: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], errorStateMatcher: [{\n                type: Input\n            }], typeaheadDebounceInterval: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], sortComparator: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], panelWidth: [{\n                type: Input\n            }], canSelectNullableOptions: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], openedChange: [{\n                type: Output\n            }], _openedStream: [{\n                type: Output,\n                args: ['opened']\n            }], _closedStream: [{\n                type: Output,\n                args: ['closed']\n            }], selectionChange: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }] } });\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSelectTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSelectTrigger, isStandalone: true, selector: \"mat-select-trigger\", providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSelectTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-select-trigger',\n                    providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }],\n                }]\n        }] });\n\nclass MatSelectModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSelectModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSelectModule, imports: [OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger], exports: [CdkScrollableModule,\n            MatFormFieldModule,\n            MatSelect,\n            MatSelectTrigger,\n            MatOptionModule,\n            MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSelectModule, providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER], imports: [OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule,\n            MatFormFieldModule,\n            MatOptionModule,\n            MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSelectModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger],\n                    exports: [\n                        CdkScrollableModule,\n                        MatFormFieldModule,\n                        MatSelect,\n                        MatSelectTrigger,\n                        MatOptionModule,\n                        MatCommonModule,\n                    ],\n                    providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\nexport { MatSelectModule as M, MAT_SELECT_SCROLL_STRATEGY as a, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY as b, MAT_SELECT_CONFIG as c, MAT_SELECT_SCROLL_STRATEGY_PROVIDER as d, MAT_SELECT_TRIGGER as e, MatSelectChange as f, MatSelect as g, MatSelectTrigger as h };\n"], "mappings": "AAAA,SAASA,8BAA8B,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,sBAAsB;AAC3H,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC5T,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC3E,SAASC,YAAY,EAAEC,aAAa,EAAEC,sBAAsB,EAAEC,mBAAmB,EAAEC,0BAA0B,QAAQ,mBAAmB;AACxI,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,cAAc,EAAEC,KAAK,EAAEC,KAAK,EAAEC,CAAC,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,QAAQ,uBAAuB;AAC9H,SAASC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,QAAQ,gBAAgB;AAClF,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC5C,SAASC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AACnF,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,mBAAmB,QAAQ,2BAA2B;AACzF,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASD,CAAC,IAAIE,6BAA6B,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,2BAA2B,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,YAAY,QAAQ,uBAAuB;AAC9K,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,8BAA8B;AACrE,SAASZ,CAAC,IAAIa,kBAAkB,QAAQ,4BAA4B;AACpE,SAASN,CAAC,IAAIO,eAAe,QAAQ,sBAAsB;AAC3D,SAASP,CAAC,IAAIQ,eAAe,QAAQ,8BAA8B;AACnE,SAASR,CAAC,IAAIS,kBAAkB,QAAQ,uBAAuB;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AAJA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,iCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA2mC6FhF,EAAE,CAAAkF,cAAA,aAIi0B,CAAC;IAJp0BlF,EAAE,CAAAmF,MAAA,EAIg1B,CAAC;IAJn1BnF,EAAE,CAAAoF,YAAA,CAIu1B,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAJ11BrF,EAAE,CAAAsF,aAAA;IAAFtF,EAAE,CAAAuF,SAAA,CAIg1B,CAAC;IAJn1BvF,EAAE,CAAAwF,iBAAA,CAAAH,MAAA,CAAAI,WAIg1B,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJn1BhF,EAAE,CAAA2F,YAAA,EAI0/B,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ7/BhF,EAAE,CAAAkF,cAAA,cAIikC,CAAC;IAJpkClF,EAAE,CAAAmF,MAAA,EAIilC,CAAC;IAJplCnF,EAAE,CAAAoF,YAAA,CAIwlC,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAJ3lCrF,EAAE,CAAAsF,aAAA;IAAFtF,EAAE,CAAAuF,SAAA,CAIilC,CAAC;IAJplCvF,EAAE,CAAAwF,iBAAA,CAAAH,MAAA,CAAAQ,YAIilC,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJplChF,EAAE,CAAAkF,cAAA,aAIw5B,CAAC;IAJ35BlF,EAAE,CAAA+F,mBAAA,IAAAL,8CAAA,MAIu7B,CAAC,IAAAE,8CAAA,kBAAqF,CAAC;IAJhhC5F,EAAE,CAAAoF,YAAA,CAIknC,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAJrnCrF,EAAE,CAAAsF,aAAA;IAAFtF,EAAE,CAAAuF,SAAA,CAImmC,CAAC;IAJtmCvF,EAAE,CAAAgG,aAAA,CAAAX,MAAA,CAAAY,aAAA,QAImmC,CAAC;EAAA;AAAA;AAAA,SAAAC,kCAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAmB,GAAA,GAJtmCnG,EAAE,CAAAoG,gBAAA;IAAFpG,EAAE,CAAAkF,cAAA,gBAI8pF,CAAC;IAJjqFlF,EAAE,CAAAqG,UAAA,qBAAAC,yDAAAC,MAAA;MAAFvG,EAAE,CAAAwG,aAAA,CAAAL,GAAA;MAAA,MAAAd,MAAA,GAAFrF,EAAE,CAAAsF,aAAA;MAAA,OAAFtF,EAAE,CAAAyG,WAAA,CAIsoFpB,MAAA,CAAAqB,cAAA,CAAAH,MAAqB,CAAC;IAAA,CAAC,CAAC;IAJhqFvG,EAAE,CAAA2F,YAAA,KAI6rF,CAAC;IAJhsF3F,EAAE,CAAAoF,YAAA,CAIusF,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAJ1sFrF,EAAE,CAAAsF,aAAA;IAAFtF,EAAE,CAAA2G,UAAA,CAAF3G,EAAE,CAAA4G,cAAA,kEAAAvB,MAAA,CAAAwB,cAAA,EAIq1E,EAAC;IAJx1E7G,EAAE,CAAA8G,WAAA,yCAAAzB,MAAA,CAAA1B,mBAI+5E,CAAC;IAJl6E3D,EAAE,CAAA+G,UAAA,YAAA1B,MAAA,CAAA2B,UAImnF,CAAC;IAJtnFhH,EAAE,CAAAiH,WAAA,OAAA5B,MAAA,CAAA6B,EAAA,qCAAA7B,MAAA,CAAA8B,QAAA,gBAAA9B,MAAA,CAAA+B,SAAA,6BAAA/B,MAAA,CAAAgC,uBAAA;EAAA;AAAA;AAtmC/F,SAASC,gCAAgCA,CAAA,EAAG;EACxC,OAAOC,KAAK,CAAC,+DAA+D,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,8BAA8BA,CAAA,EAAG;EACtC,OAAOD,KAAK,CAAC,oDAAoD,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iCAAiCA,CAAA,EAAG;EACzC,OAAOF,KAAK,CAAC,mCAAmC,CAAC;AACrD;;AAEA;AACA,MAAMG,0BAA0B,GAAG,IAAIzH,cAAc,CAAC,4BAA4B,EAAE;EAChF0H,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,QAAQ,GAAG3H,MAAM,CAACC,QAAQ,CAAC;IACjC,OAAO,MAAMP,8BAA8B,CAACiI,QAAQ,CAAC;EACzD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASC,2CAA2CA,CAACC,QAAQ,EAAE;EAC3D,MAAMF,QAAQ,GAAG3H,MAAM,CAACC,QAAQ,CAAC;EACjC,OAAO,MAAMP,8BAA8B,CAACiI,QAAQ,CAAC;AACzD;AACA;AACA,MAAMG,iBAAiB,GAAG,IAAI/H,cAAc,CAAC,mBAAmB,CAAC;AACjE;AACA;AACA;AACA;AACA;AACA,MAAMgI,mCAAmC,GAAG;EACxCC,OAAO,EAAER,0BAA0B;EACnCS,IAAI,EAAE,EAAE;EACRC,UAAU,EAAEN;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMO,kBAAkB,GAAG,IAAIpI,cAAc,CAAC,kBAAkB,CAAC;AACjE;AACA,MAAMqI,eAAe,CAAC;EAClBC,MAAM;EACNC,KAAK;EACLC,WAAWA,CACX;EACAF,MAAM,EACN;EACAC,KAAK,EAAE;IACH,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA,MAAME,SAAS,CAAC;EACZC,cAAc,GAAGzI,MAAM,CAACoB,aAAa,CAAC;EACtCsH,kBAAkB,GAAG1I,MAAM,CAACE,iBAAiB,CAAC;EAC9CyI,WAAW,GAAG3I,MAAM,CAACG,UAAU,CAAC;EAChCyI,IAAI,GAAG5I,MAAM,CAAC2B,cAAc,EAAE;IAAEkH,QAAQ,EAAE;EAAK,CAAC,CAAC;EACjDC,YAAY,GAAG9I,MAAM,CAACsB,YAAY,CAAC;EACnCyH,SAAS,GAAG/I,MAAM,CAACI,SAAS,CAAC;EAC7B4I,gBAAgB,GAAGhJ,MAAM,CAACqD,cAAc,EAAE;IAAEwF,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC7DI,SAAS,GAAGjJ,MAAM,CAACsC,SAAS,EAAE;IAAE4G,IAAI,EAAE,IAAI;IAAEL,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC7DM,cAAc,GAAGnJ,MAAM,CAACuB,aAAa,CAAC;EACtC6H,eAAe,GAAGpJ,MAAM,CAAC8H,iBAAiB,EAAE;IAAEe,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC/DpF,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;EAC3C4F,YAAY,GAAG,IAAI3G,OAAO,CAAC,CAAC;EAC5B4G,cAAc;EACd;EACAC,OAAO;EACP;EACA;EACA;EACAC,YAAY;EACZ;EACAzD,aAAa;EACb;AACJ;AACA;AACA;AACA;AACA;EACI0D,UAAU,GAAG,CACT;IACIC,OAAO,EAAE,OAAO;IAChBC,OAAO,EAAE,QAAQ;IACjBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE;EACd,CAAC,EACD;IACIH,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,QAAQ;IACjBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE;EACd,CAAC,EACD;IACIH,OAAO,EAAE,OAAO;IAChBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,QAAQ;IAClB/C,UAAU,EAAE;EAChB,CAAC,EACD;IACI4C,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,QAAQ;IAClB/C,UAAU,EAAE;EAChB,CAAC,CACJ;EACD;EACAgD,qBAAqBA,CAACC,KAAK,EAAE;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACT,OAAO,CAACU,OAAO,CAAC,CAAC,CAACF,KAAK,CAAC;IAC5C,IAAIC,MAAM,EAAE;MACR,MAAME,KAAK,GAAG,IAAI,CAACA,KAAK,CAACC,aAAa;MACtC,MAAMC,UAAU,GAAG1G,6BAA6B,CAACqG,KAAK,EAAE,IAAI,CAACR,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MACxF,MAAMa,OAAO,GAAGL,MAAM,CAACM,eAAe,CAAC,CAAC;MACxC,IAAIP,KAAK,KAAK,CAAC,IAAIK,UAAU,KAAK,CAAC,EAAE;QACjC;QACA;QACA;QACAF,KAAK,CAACK,SAAS,GAAG,CAAC;MACvB,CAAC,MACI;QACDL,KAAK,CAACK,SAAS,GAAG3G,wBAAwB,CAACyG,OAAO,CAACG,SAAS,EAAEH,OAAO,CAACI,YAAY,EAAEP,KAAK,CAACK,SAAS,EAAEL,KAAK,CAACO,YAAY,CAAC;MAC5H;IACJ;EACJ;EACA;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACZ,qBAAqB,CAAC,IAAI,CAACa,WAAW,CAACC,eAAe,IAAI,CAAC,CAAC;EACrE;EACA;EACAC,eAAeA,CAACvC,KAAK,EAAE;IACnB,OAAO,IAAIF,eAAe,CAAC,IAAI,EAAEE,KAAK,CAAC;EAC3C;EACA;EACAwC,sBAAsB,GAAG9K,MAAM,CAACwH,0BAA0B,CAAC;EAC3D;EACAuD,UAAU,GAAG,KAAK;EAClB;EACAC,YAAY,GAAGA,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,KAAKC,EAAE;EACpC;EACAC,IAAI,GAAG,IAAI,CAACrC,YAAY,CAACsC,KAAK,CAAC,aAAa,CAAC;EAC7C;EACAC,sBAAsB,GAAG,IAAI;EAC7B;AACJ;AACA;AACA;EACIC,gBAAgB;EAChB;EACAC,QAAQ,GAAG,IAAI7I,OAAO,CAAC,CAAC;EACxB;EACA8I,kBAAkB;EAClB;AACJ;AACA;AACA;AACA;EACIC,YAAY,GAAG,IAAI/I,OAAO,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;EACIgJ,wBAAwB,GAAG,IAAI;EAC/B;AACJ;AACA;AACA;EACIC,mBAAmB;EACnB;EACAC,eAAe;EACf;EACAjB,WAAW;EACX;EACAkB,uBAAuB;EACvB;EACAC,aAAa;EACb;EACAC,SAAS,GAAGA,CAAA,KAAM,CAAE,CAAC;EACrB;EACAC,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;EACtB;EACAC,QAAQ,GAAG,IAAI,CAACnD,YAAY,CAACsC,KAAK,CAAC,mBAAmB,CAAC;EACvD;EACAc,eAAe;EACfC,kBAAkB,GAAG,IAAI,CAAC/C,eAAe,EAAEgD,iBAAiB,IAAI,EAAE;EAClE;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACvB,UAAU;EAC3C;EACAuB,QAAQ,GAAG,KAAK;EAChB;EACAC,WAAW,GAAG,YAAY;EAC1B;EACAC,OAAO;EACP;EACAtC,KAAK;EACL;EACAuC,WAAW;EACX;EACA3F,UAAU;EACV;EACA4F,QAAQ,GAAG,KAAK;EAChB;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc,CAAC,CAAC;EAChC;EACA,IAAID,aAAaA,CAACrE,KAAK,EAAE;IACrB,IAAI,CAACsE,cAAc,CAACC,GAAG,CAACvE,KAAK,CAAC;EAClC;EACAsE,cAAc,GAAGvM,MAAM,CAAC,KAAK,CAAC;EAC9B;EACAyM,QAAQ,GAAG,CAAC;EACZ;EACA,IAAIC,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAACC,6BAA6B;EAC7C;EACA,IAAID,4BAA4BA,CAACzE,KAAK,EAAE;IACpC,IAAI,CAAC0E,6BAA6B,GAAG1E,KAAK;IAC1C,IAAI,CAAC2E,qBAAqB,CAAC,CAAC;EAChC;EACAD,6BAA6B,GAAG,IAAI,CAAC5D,eAAe,EAAE2D,4BAA4B,IAAI,KAAK;EAC3F;EACA,IAAIxH,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC2H,YAAY;EAC5B;EACA,IAAI3H,WAAWA,CAAC+C,KAAK,EAAE;IACnB,IAAI,CAAC4E,YAAY,GAAG5E,KAAK;IACzB,IAAI,CAACmD,YAAY,CAAC0B,IAAI,CAAC,CAAC;EAC5B;EACAD,YAAY;EACZ;EACA,IAAIE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACpE,SAAS,EAAEqE,OAAO,EAAEC,YAAY,CAAChL,UAAU,CAAC6K,QAAQ,CAAC,IAAI,KAAK;EAChG;EACA,IAAIA,QAAQA,CAAC9E,KAAK,EAAE;IAChB,IAAI,CAAC+E,SAAS,GAAG/E,KAAK;IACtB,IAAI,CAACmD,YAAY,CAAC0B,IAAI,CAAC,CAAC;EAC5B;EACAE,SAAS;EACT;EACA,IAAIpG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACuG,SAAS;EACzB;EACA,IAAIvG,QAAQA,CAACqB,KAAK,EAAE;IAChB,IAAI,IAAI,CAACsD,eAAe,KAAK,OAAO6B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzE,MAAMrG,gCAAgC,CAAC,CAAC;IAC5C;IACA,IAAI,CAACoG,SAAS,GAAGlF,KAAK;EAC1B;EACAkF,SAAS,GAAG,KAAK;EACjB;EACAE,sBAAsB,GAAG,IAAI,CAACtE,eAAe,EAAEsE,sBAAsB,IAAI,KAAK;EAC9E;AACJ;AACA;AACA;AACA;EACI,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC3C,YAAY;EAC5B;EACA,IAAI2C,WAAWA,CAACC,EAAE,EAAE;IAChB,IAAI,OAAOA,EAAE,KAAK,UAAU,KAAK,OAAOH,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E,MAAMlG,iCAAiC,CAAC,CAAC;IAC7C;IACA,IAAI,CAACyD,YAAY,GAAG4C,EAAE;IACtB,IAAI,IAAI,CAAChC,eAAe,EAAE;MACtB;MACA,IAAI,CAACiC,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;EACA,IAAIvF,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACwF,MAAM;EACtB;EACA,IAAIxF,KAAKA,CAACyF,QAAQ,EAAE;IAChB,MAAMC,WAAW,GAAG,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC;IAC/C,IAAIC,WAAW,EAAE;MACb,IAAI,CAACjC,SAAS,CAACgC,QAAQ,CAAC;IAC5B;EACJ;EACAD,MAAM;EACN;EACA5G,SAAS,GAAG,EAAE;EACd;EACAgH,cAAc;EACd;EACA,IAAIC,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC3C,kBAAkB,CAAC4C,OAAO;EAC1C;EACA,IAAID,iBAAiBA,CAAC7F,KAAK,EAAE;IACzB,IAAI,CAACkD,kBAAkB,CAAC4C,OAAO,GAAG9F,KAAK;EAC3C;EACA;EACA+F,yBAAyB;EACzB;AACJ;AACA;AACA;EACIC,cAAc;EACd;EACA,IAAItH,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACuH,GAAG;EACnB;EACA,IAAIvH,EAAEA,CAACsB,KAAK,EAAE;IACV,IAAI,CAACiG,GAAG,GAAGjG,KAAK,IAAI,IAAI,CAAC6C,IAAI;IAC7B,IAAI,CAACM,YAAY,CAAC0B,IAAI,CAAC,CAAC;EAC5B;EACAoB,GAAG;EACH;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAChD,kBAAkB,CAACgD,UAAU;EAC7C;EACA,IAAIA,UAAUA,CAAClG,KAAK,EAAE;IAClB,IAAI,CAACkD,kBAAkB,CAACgD,UAAU,GAAGlG,KAAK;EAC9C;EACA;AACJ;AACA;AACA;EACImG,UAAU,GAAG,IAAI,CAACrF,eAAe,IAAI,OAAO,IAAI,CAACA,eAAe,CAACqF,UAAU,KAAK,WAAW,GACrF,IAAI,CAACrF,eAAe,CAACqF,UAAU,GAC/B,MAAM;EACZ;AACJ;AACA;AACA;AACA;AACA;EACIC,wBAAwB,GAAG,IAAI,CAACtF,eAAe,EAAEsF,wBAAwB,IAAI,KAAK;EAClF;EACAC,sBAAsB,GAAGhM,KAAK,CAAC,MAAM;IACjC,MAAM4G,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,EAAE;MACT,OAAOA,OAAO,CAACqF,OAAO,CAACC,IAAI,CAAChM,SAAS,CAAC0G,OAAO,CAAC,EAAEzG,SAAS,CAAC,MAAMF,KAAK,CAAC,GAAG2G,OAAO,CAACvG,GAAG,CAACgH,MAAM,IAAIA,MAAM,CAAC8E,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC/H;IACA,OAAO,IAAI,CAACzF,YAAY,CAACwF,IAAI,CAAC/L,SAAS,CAAC,MAAM,IAAI,CAAC6L,sBAAsB,CAAC,CAAC;EAC/E,CAAC,CAAC;EACF;EACAI,YAAY,GAAG,IAAIzO,YAAY,CAAC,CAAC;EACjC;EACA0O,aAAa,GAAG,IAAI,CAACD,YAAY,CAACF,IAAI,CAAC9L,MAAM,CAACkM,CAAC,IAAIA,CAAC,CAAC,EAAEjM,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;EACtE;EACAkM,aAAa,GAAG,IAAI,CAACH,YAAY,CAACF,IAAI,CAAC9L,MAAM,CAACkM,CAAC,IAAI,CAACA,CAAC,CAAC,EAAEjM,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;EACvE;EACAmM,eAAe,GAAG,IAAI7O,YAAY,CAAC,CAAC;EACpC;AACJ;AACA;AACA;AACA;EACI8O,WAAW,GAAG,IAAI9O,YAAY,CAAC,CAAC;EAChCiI,WAAWA,CAAA,EAAG;IACV,MAAM8G,wBAAwB,GAAGrP,MAAM,CAACoE,iBAAiB,CAAC;IAC1D,MAAMkL,UAAU,GAAGtP,MAAM,CAACwC,MAAM,EAAE;MAAEqG,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrD,MAAM0G,eAAe,GAAGvP,MAAM,CAACyC,kBAAkB,EAAE;MAAEoG,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtE,MAAMiE,QAAQ,GAAG9M,MAAM,CAAC,IAAIO,kBAAkB,CAAC,UAAU,CAAC,EAAE;MAAEsI,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC/E,IAAI,IAAI,CAACI,SAAS,EAAE;MAChB;MACA;MACA,IAAI,CAACA,SAAS,CAACuG,aAAa,GAAG,IAAI;IACvC;IACA;IACA;IACA,IAAI,IAAI,CAACpG,eAAe,EAAEiF,yBAAyB,IAAI,IAAI,EAAE;MACzD,IAAI,CAACA,yBAAyB,GAAG,IAAI,CAACjF,eAAe,CAACiF,yBAAyB;IACnF;IACA,IAAI,CAAC7C,kBAAkB,GAAG,IAAInH,kBAAkB,CAACgL,wBAAwB,EAAE,IAAI,CAACpG,SAAS,EAAEsG,eAAe,EAAED,UAAU,EAAE,IAAI,CAAC7D,YAAY,CAAC;IAC1I,IAAI,CAACS,eAAe,GAAG,IAAI,CAACpB,sBAAsB,CAAC,CAAC;IACpD,IAAI,CAACgC,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG2C,QAAQ,CAAC3C,QAAQ,CAAC,IAAI,CAAC;IAC9D;IACA,IAAI,CAAC9F,EAAE,GAAG,IAAI,CAACA,EAAE;EACrB;EACA0I,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC9D,eAAe,GAAG,IAAIhK,cAAc,CAAC,IAAI,CAACqF,QAAQ,CAAC;IACxD,IAAI,CAACwE,YAAY,CAAC0B,IAAI,CAAC,CAAC;IACxB,IAAI,CAAC1E,cAAc,CACdkH,MAAM,CAAC,CAAC,CACRd,IAAI,CAAC5L,SAAS,CAAC,IAAI,CAACsI,QAAQ,CAAC,CAAC,CAC9BqE,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAACC,SAAS,EAAE;QAChB,IAAI,CAAC/D,aAAa,GAAG,IAAI,CAACgE,gBAAgB,CAAC,IAAI,CAACjE,uBAAuB,CAAC;QACxE,IAAI,CAACnD,kBAAkB,CAACqH,aAAa,CAAC,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC3G,YAAY,CAAC8D,IAAI,CAAC,CAAC;IACxB,IAAI,CAAC9D,YAAY,CAAC4G,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACtE,eAAe,CAACuE,OAAO,CAACtB,IAAI,CAAC5L,SAAS,CAAC,IAAI,CAACsI,QAAQ,CAAC,CAAC,CAACqE,SAAS,CAACQ,KAAK,IAAI;MAC3EA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACtG,MAAM,IAAIA,MAAM,CAACuG,MAAM,CAAC,CAAC,CAAC;MAC9CH,KAAK,CAACI,OAAO,CAACF,OAAO,CAACtG,MAAM,IAAIA,MAAM,CAACyG,QAAQ,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,IAAI,CAAClH,OAAO,CAACqF,OAAO,CAACC,IAAI,CAAChM,SAAS,CAAC,IAAI,CAAC,EAAEI,SAAS,CAAC,IAAI,CAACsI,QAAQ,CAAC,CAAC,CAACqE,SAAS,CAAC,MAAM;MACjF,IAAI,CAACc,aAAa,CAAC,CAAC;MACpB,IAAI,CAAC7C,oBAAoB,CAAC,CAAC;IAC/B,CAAC,CAAC;EACN;EACA8C,SAASA,CAAA,EAAG;IACR,MAAMC,iBAAiB,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAC1D,MAAM5H,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC;IACA;IACA;IACA,IAAI2H,iBAAiB,KAAK,IAAI,CAACvF,sBAAsB,EAAE;MACnD,MAAMhB,OAAO,GAAG,IAAI,CAAC1B,WAAW,CAACwB,aAAa;MAC9C,IAAI,CAACkB,sBAAsB,GAAGuF,iBAAiB;MAC/C,IAAIA,iBAAiB,EAAE;QACnBvG,OAAO,CAACyG,YAAY,CAAC,iBAAiB,EAAEF,iBAAiB,CAAC;MAC9D,CAAC,MACI;QACDvG,OAAO,CAAC0G,eAAe,CAAC,iBAAiB,CAAC;MAC9C;IACJ;IACA,IAAI9H,SAAS,EAAE;MACX;MACA,IAAI,IAAI,CAACqC,gBAAgB,KAAKrC,SAAS,CAACqE,OAAO,EAAE;QAC7C,IAAI,IAAI,CAAChC,gBAAgB,KAAK0F,SAAS,IACnC/H,SAAS,CAACyD,QAAQ,KAAK,IAAI,IAC3BzD,SAAS,CAACyD,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;UACtC,IAAI,CAACA,QAAQ,GAAGzD,SAAS,CAACyD,QAAQ;QACtC;QACA,IAAI,CAACpB,gBAAgB,GAAGrC,SAAS,CAACqE,OAAO;MAC7C;MACA,IAAI,CAAC2D,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAC,WAAWA,CAACtC,OAAO,EAAE;IACjB;IACA;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,qBAAqB,CAAC,EAAE;MACvD,IAAI,CAACnD,YAAY,CAAC0B,IAAI,CAAC,CAAC;IAC5B;IACA,IAAIyB,OAAO,CAAC,2BAA2B,CAAC,IAAI,IAAI,CAACjE,WAAW,EAAE;MAC1D,IAAI,CAACA,WAAW,CAACwG,aAAa,CAAC,IAAI,CAAC9C,yBAAyB,CAAC;IAClE;EACJ;EACA+C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9H,cAAc,GAAG,CAAC;IACvB,IAAI,CAACqB,WAAW,EAAE0G,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC9F,QAAQ,CAAC4B,IAAI,CAAC,CAAC;IACpB,IAAI,CAAC5B,QAAQ,CAAC0E,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACxE,YAAY,CAACwE,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACqB,eAAe,CAAC,CAAC;EAC1B;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC1B,SAAS,GAAG,IAAI,CAAC2B,KAAK,CAAC,CAAC,GAAG,IAAI,CAACC,IAAI,CAAC,CAAC;EAC/C;EACA;EACAA,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;MAClB;IACJ;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC1I,gBAAgB,EAAE;MACvB,IAAI,CAAC6C,uBAAuB,GAAG,IAAI,CAAC7C,gBAAgB,CAAC2I,yBAAyB,CAAC,CAAC;IACpF;IACA,IAAI,CAACrI,cAAc,GAAG,CAAC;IACvB,IAAI,CAACwC,aAAa,GAAG,IAAI,CAACgE,gBAAgB,CAAC,IAAI,CAACjE,uBAAuB,CAAC;IACxE,IAAI,CAAC+F,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAAC7G,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC0B,WAAW,CAACoF,cAAc,CAAChD,IAAI,CAAC3L,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0M,SAAS,CAAC,MAAM;MAC1D,IAAI,CAAClH,kBAAkB,CAACqH,aAAa,CAAC,CAAC;MACvC,IAAI,CAACrF,mBAAmB,CAAC,CAAC;IAC9B,CAAC,CAAC;IACF,IAAI,CAAC+B,WAAW,CAACqF,aAAa,CAAC,CAAC;IAChC,IAAI,CAACnH,WAAW,CAACoH,yBAAyB,CAAC,IAAI,CAAC;IAChD,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACtJ,kBAAkB,CAACuJ,YAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAACxG,YAAY,CAAC0B,IAAI,CAAC,CAAC;IACxB;IACA+E,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACrD,YAAY,CAACsD,IAAI,CAAC,IAAI,CAAC,CAAC;EAC9D;EACA;AACJ;AACA;AACA;AACA;EACIC,aAAa,GAAG,IAAI;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIV,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMW,KAAK,GAAG,IAAI,CAAC5J,WAAW,CAACwB,aAAa,CAACqI,OAAO,CAAC,mDAAmD,CAAC;IACzG,IAAI,CAACD,KAAK,EAAE;MACR;MACA;IACJ;IACA,MAAME,OAAO,GAAG,GAAG,IAAI,CAACzL,EAAE,QAAQ;IAClC,IAAI,IAAI,CAACsL,aAAa,EAAE;MACpB9Q,sBAAsB,CAAC,IAAI,CAAC8Q,aAAa,EAAE,WAAW,EAAEG,OAAO,CAAC;IACpE;IACAhR,mBAAmB,CAAC8Q,KAAK,EAAE,WAAW,EAAEE,OAAO,CAAC;IAChD,IAAI,CAACH,aAAa,GAAGC,KAAK;EAC9B;EACA;EACAjB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACgB,aAAa,EAAE;MACrB;MACA;IACJ;IACA,MAAMG,OAAO,GAAG,GAAG,IAAI,CAACzL,EAAE,QAAQ;IAClCxF,sBAAsB,CAAC,IAAI,CAAC8Q,aAAa,EAAE,WAAW,EAAEG,OAAO,CAAC;IAChE,IAAI,CAACH,aAAa,GAAG,IAAI;EAC7B;EACA;EACAd,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACzG,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC2H,cAAc,CAAC,CAAC;MACrB,IAAI,CAAC/H,WAAW,CAACoH,yBAAyB,CAAC,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;MACzE,IAAI,CAACjK,kBAAkB,CAACuJ,YAAY,CAAC,CAAC;MACtC,IAAI,CAACjG,UAAU,CAAC,CAAC;MACjB;MACA,IAAI,CAACP,YAAY,CAAC0B,IAAI,CAAC,CAAC;MACxB;MACA+E,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACrD,YAAY,CAACsD,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/D;EACJ;EACA;EACAK,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACjP,mBAAmB,IAAI,CAAC,IAAI,CAACyG,KAAK,EAAE;MACzC,IAAI,CAAC0I,cAAc,CAAC,CAAC;MACrB;IACJ;IACA,IAAI,CAACtJ,cAAc,GAAG,CAAC;IACvB,IAAI,CAACA,cAAc,GAAG,MAAM;MACxBuJ,YAAY,CAAC,CAAC;MACdC,YAAY,CAACC,iBAAiB,CAAC;MAC/B,IAAI,CAACzJ,cAAc,GAAG0H,SAAS;IACnC,CAAC;IACD,MAAM9G,KAAK,GAAG,IAAI,CAACA,KAAK,CAACC,aAAa;IACtC,MAAM0I,YAAY,GAAG,IAAI,CAAC9J,SAAS,CAACiK,MAAM,CAAC9I,KAAK,EAAE,cAAc,EAAGkG,KAAK,IAAK;MACzE,IAAIA,KAAK,CAAC6C,aAAa,KAAK,kBAAkB,EAAE;QAC5C,IAAI,CAAC3J,cAAc,GAAG,CAAC;QACvB,IAAI,CAACsJ,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF;IACA;IACA,MAAMG,iBAAiB,GAAGG,UAAU,CAAC,MAAM;MACvC,IAAI,CAAC5J,cAAc,GAAG,CAAC;MACvB,IAAI,CAACsJ,cAAc,CAAC,CAAC;IACzB,CAAC,EAAE,GAAG,CAAC;IACP1I,KAAK,CAACiJ,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;EAChD;EACA;EACAR,cAAcA,CAAA,EAAG;IACb,IAAI,CAACnG,WAAW,CAAC4G,aAAa,CAAC,CAAC;IAChC;IACA;IACA,IAAI,CAAC3K,kBAAkB,CAACuJ,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqB,UAAUA,CAAChL,KAAK,EAAE;IACd,IAAI,CAAC2F,YAAY,CAAC3F,KAAK,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiL,gBAAgBA,CAAC3F,EAAE,EAAE;IACjB,IAAI,CAAC7B,SAAS,GAAG6B,EAAE;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI4F,iBAAiBA,CAAC5F,EAAE,EAAE;IAClB,IAAI,CAAC5B,UAAU,GAAG4B,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6F,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAChH,QAAQ,GAAGgH,UAAU;IAC1B,IAAI,CAAChL,kBAAkB,CAACuJ,YAAY,CAAC,CAAC;IACtC,IAAI,CAACxG,YAAY,CAAC0B,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAI0C,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC9E,UAAU;EAC1B;EACA;EACA,IAAI4I,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC1M,QAAQ,GAAG,IAAI,CAAC2E,eAAe,EAAE+H,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC/H,eAAe,EAAE+H,QAAQ,CAAC,CAAC,CAAC;EACnG;EACA;EACA,IAAIhO,YAAYA,CAAA,EAAG;IACf,IAAI,IAAI,CAACiO,KAAK,EAAE;MACZ,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAACpG,SAAS,EAAE;MAChB,MAAMqG,eAAe,GAAG,IAAI,CAACjI,eAAe,CAAC+H,QAAQ,CAAC3Q,GAAG,CAACgH,MAAM,IAAIA,MAAM,CAAC8J,SAAS,CAAC;MACrF,IAAI,IAAI,CAACnB,MAAM,CAAC,CAAC,EAAE;QACfkB,eAAe,CAACE,OAAO,CAAC,CAAC;MAC7B;MACA;MACA,OAAOF,eAAe,CAACG,IAAI,CAAC,IAAI,CAAC;IACrC;IACA,OAAO,IAAI,CAACpI,eAAe,CAAC+H,QAAQ,CAAC,CAAC,CAAC,CAACG,SAAS;EACrD;EACA;EACA7C,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACzF,kBAAkB,CAACyF,gBAAgB,CAAC,CAAC;EAC9C;EACA;EACA0B,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC/J,IAAI,GAAG,IAAI,CAACA,IAAI,CAACN,KAAK,KAAK,KAAK,GAAG,KAAK;EACxD;EACA;EACA9B,cAAcA,CAAC4J,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC1D,QAAQ,EAAE;MAChB,IAAI,CAACmD,SAAS,GAAG,IAAI,CAACoE,kBAAkB,CAAC7D,KAAK,CAAC,GAAG,IAAI,CAAC8D,oBAAoB,CAAC9D,KAAK,CAAC;IACtF;EACJ;EACA;EACA8D,oBAAoBA,CAAC9D,KAAK,EAAE;IACxB,MAAM+D,OAAO,GAAG/D,KAAK,CAAC+D,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAKjS,UAAU,IACrCiS,OAAO,KAAKhS,QAAQ,IACpBgS,OAAO,KAAK/R,UAAU,IACtB+R,OAAO,KAAK9R,WAAW;IAC3B,MAAMgS,SAAS,GAAGF,OAAO,KAAKrS,KAAK,IAAIqS,OAAO,KAAKpS,KAAK;IACxD,MAAMuS,OAAO,GAAG,IAAI,CAAC3J,WAAW;IAChC;IACA,IAAK,CAAC2J,OAAO,CAACC,QAAQ,CAAC,CAAC,IAAIF,SAAS,IAAI,CAACxS,cAAc,CAACuO,KAAK,CAAC,IAC1D,CAAC,IAAI,CAACnJ,QAAQ,IAAImJ,KAAK,CAACoE,MAAM,KAAKJ,UAAW,EAAE;MACjDhE,KAAK,CAACqE,cAAc,CAAC,CAAC,CAAC,CAAC;MACxB,IAAI,CAAChD,IAAI,CAAC,CAAC;IACf,CAAC,MACI,IAAI,CAAC,IAAI,CAACxK,QAAQ,EAAE;MACrB,MAAMyN,wBAAwB,GAAG,IAAI,CAACf,QAAQ;MAC9CW,OAAO,CAACK,SAAS,CAACvE,KAAK,CAAC;MACxB,MAAMwE,cAAc,GAAG,IAAI,CAACjB,QAAQ;MACpC;MACA,IAAIiB,cAAc,IAAIF,wBAAwB,KAAKE,cAAc,EAAE;QAC/D;QACA;QACA,IAAI,CAACzL,cAAc,CAAC0L,QAAQ,CAACD,cAAc,CAACd,SAAS,EAAE,KAAK,CAAC;MACjE;IACJ;EACJ;EACA;EACAG,kBAAkBA,CAAC7D,KAAK,EAAE;IACtB,MAAMkE,OAAO,GAAG,IAAI,CAAC3J,WAAW;IAChC,MAAMwJ,OAAO,GAAG/D,KAAK,CAAC+D,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAKjS,UAAU,IAAIiS,OAAO,KAAKhS,QAAQ;IACjE,MAAMoS,QAAQ,GAAGD,OAAO,CAACC,QAAQ,CAAC,CAAC;IACnC,IAAIH,UAAU,IAAIhE,KAAK,CAACoE,MAAM,EAAE;MAC5B;MACApE,KAAK,CAACqE,cAAc,CAAC,CAAC;MACtB,IAAI,CAACjD,KAAK,CAAC,CAAC;MACZ;MACA;IACJ,CAAC,MACI,IAAI,CAAC+C,QAAQ,KACbJ,OAAO,KAAKrS,KAAK,IAAIqS,OAAO,KAAKpS,KAAK,CAAC,IACxCuS,OAAO,CAACQ,UAAU,IAClB,CAACjT,cAAc,CAACuO,KAAK,CAAC,EAAE;MACxBA,KAAK,CAACqE,cAAc,CAAC,CAAC;MACtBH,OAAO,CAACQ,UAAU,CAACC,qBAAqB,CAAC,CAAC;IAC9C,CAAC,MACI,IAAI,CAACR,QAAQ,IAAI,IAAI,CAAC/G,SAAS,IAAI2G,OAAO,KAAKnS,CAAC,IAAIoO,KAAK,CAAC4E,OAAO,EAAE;MACpE5E,KAAK,CAACqE,cAAc,CAAC,CAAC;MACtB,MAAMQ,oBAAoB,GAAG,IAAI,CAAC1L,OAAO,CAAC2L,IAAI,CAACC,GAAG,IAAI,CAACA,GAAG,CAACzI,QAAQ,IAAI,CAACyI,GAAG,CAACxB,QAAQ,CAAC;MACrF,IAAI,CAACpK,OAAO,CAAC+G,OAAO,CAACtG,MAAM,IAAI;QAC3B,IAAI,CAACA,MAAM,CAAC0C,QAAQ,EAAE;UAClBuI,oBAAoB,GAAGjL,MAAM,CAACuG,MAAM,CAAC,CAAC,GAAGvG,MAAM,CAACyG,QAAQ,CAAC,CAAC;QAC9D;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,MAAM2E,sBAAsB,GAAGd,OAAO,CAAC1J,eAAe;MACtD0J,OAAO,CAACK,SAAS,CAACvE,KAAK,CAAC;MACxB,IAAI,IAAI,CAAC5C,SAAS,IACd4G,UAAU,IACVhE,KAAK,CAACiF,QAAQ,IACdf,OAAO,CAACQ,UAAU,IAClBR,OAAO,CAAC1J,eAAe,KAAKwK,sBAAsB,EAAE;QACpDd,OAAO,CAACQ,UAAU,CAACC,qBAAqB,CAAC,CAAC;MAC9C;IACJ;EACJ;EACA;EACAO,qBAAqBA,CAAClF,KAAK,EAAE;IACzB;IACA;IACA;IACA;IACA,IAAIA,KAAK,CAAC+D,OAAO,KAAKlS,MAAM,IAAI,CAACJ,cAAc,CAACuO,KAAK,CAAC,EAAE;MACpDA,KAAK,CAACqE,cAAc,CAAC,CAAC;MACtB,IAAI,CAACjD,KAAK,CAAC,CAAC;IAChB;EACJ;EACA+D,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAC7I,QAAQ,EAAE;MAChB,IAAI,CAACJ,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACb,YAAY,CAAC0B,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACIqI,OAAOA,CAAA,EAAG;IACN,IAAI,CAAClJ,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC3B,WAAW,EAAE8K,eAAe,CAAC,CAAC;IACnC,IAAI,CAAC,IAAI,CAAC/I,QAAQ,IAAI,CAAC,IAAI,CAACmD,SAAS,EAAE;MACnC,IAAI,CAAC7D,UAAU,CAAC,CAAC;MACjB,IAAI,CAACtD,kBAAkB,CAACuJ,YAAY,CAAC,CAAC;MACtC,IAAI,CAACxG,YAAY,CAAC0B,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACAxG,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACqC,gBAAgB,GAAG,OAAO,IAAI,CAACA,gBAAgB,CAAC0M,KAAK,EAAE,GAAG,EAAE;EAC5E;EACA;EACA,IAAI9B,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAAChI,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC+J,OAAO,CAAC,CAAC;EAClE;EACA9H,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACAqE,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,IAAI,CAACnJ,SAAS,EAAE;QAChB,IAAI,CAAC6E,MAAM,GAAG,IAAI,CAAC7E,SAAS,CAACX,KAAK;MACtC;MACA,IAAI,CAACsN,oBAAoB,CAAC,IAAI,CAAC9H,MAAM,CAAC;MACtC,IAAI,CAACrC,YAAY,CAAC0B,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIyI,oBAAoBA,CAACtN,KAAK,EAAE;IACxB,IAAI,CAACiB,OAAO,CAAC+G,OAAO,CAACtG,MAAM,IAAIA,MAAM,CAAC6L,iBAAiB,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACjK,eAAe,CAACkK,KAAK,CAAC,CAAC;IAC5B,IAAI,IAAI,CAAC7O,QAAQ,IAAIqB,KAAK,EAAE;MACxB,IAAI,CAACyN,KAAK,CAACC,OAAO,CAAC1N,KAAK,CAAC,KAAK,OAAOmF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC1E,MAAMnG,8BAA8B,CAAC,CAAC;MAC1C;MACAgB,KAAK,CAACgI,OAAO,CAAE2F,YAAY,IAAK,IAAI,CAACC,oBAAoB,CAACD,YAAY,CAAC,CAAC;MACxE,IAAI,CAACE,WAAW,CAAC,CAAC;IACtB,CAAC,MACI;MACD,MAAMC,mBAAmB,GAAG,IAAI,CAACF,oBAAoB,CAAC5N,KAAK,CAAC;MAC5D;MACA;MACA,IAAI8N,mBAAmB,EAAE;QACrB,IAAI,CAACzL,WAAW,CAAC0L,gBAAgB,CAACD,mBAAmB,CAAC;MAC1D,CAAC,MACI,IAAI,CAAC,IAAI,CAACvG,SAAS,EAAE;QACtB;QACA;QACA,IAAI,CAAClF,WAAW,CAAC0L,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACzC;IACJ;IACA,IAAI,CAAC3N,kBAAkB,CAACuJ,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIiE,oBAAoBA,CAAC5N,KAAK,EAAE;IACxB,MAAM8N,mBAAmB,GAAG,IAAI,CAAC7M,OAAO,CAAC+M,IAAI,CAAEtM,MAAM,IAAK;MACtD;MACA;MACA,IAAI,IAAI,CAAC4B,eAAe,CAAC2K,UAAU,CAACvM,MAAM,CAAC,EAAE;QACzC,OAAO,KAAK;MAChB;MACA,IAAI;QACA;QACA,OAAQ,CAACA,MAAM,CAAC1B,KAAK,IAAI,IAAI,IAAI,IAAI,CAACoG,wBAAwB,KAC1D,IAAI,CAAC1D,YAAY,CAAChB,MAAM,CAAC1B,KAAK,EAAEA,KAAK,CAAC;MAC9C,CAAC,CACD,OAAOkO,KAAK,EAAE;QACV,IAAI,OAAO/I,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C;UACAgJ,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC;QACvB;QACA,OAAO,KAAK;MAChB;IACJ,CAAC,CAAC;IACF,IAAIJ,mBAAmB,EAAE;MACrB,IAAI,CAACxK,eAAe,CAAC2E,MAAM,CAAC6F,mBAAmB,CAAC;IACpD;IACA,OAAOA,mBAAmB;EAC9B;EACA;EACAnI,YAAYA,CAACF,QAAQ,EAAE;IACnB;IACA,IAAIA,QAAQ,KAAK,IAAI,CAACD,MAAM,IAAK,IAAI,CAACN,SAAS,IAAIuI,KAAK,CAACC,OAAO,CAACjI,QAAQ,CAAE,EAAE;MACzE,IAAI,IAAI,CAACxE,OAAO,EAAE;QACd,IAAI,CAACqM,oBAAoB,CAAC7H,QAAQ,CAAC;MACvC;MACA,IAAI,CAACD,MAAM,GAAGC,QAAQ;MACtB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA4I,cAAc,GAAI3M,MAAM,IAAK;IACzB,IAAI,IAAI,CAAC6F,SAAS,EAAE;MAChB;MACA,OAAO,KAAK;IAChB;IACA;IACA;IACA;IACA,OAAO7F,MAAM,CAAC0C,QAAQ;EAC1B,CAAC;EACD;EACAoD,gBAAgBA,CAAC8G,eAAe,EAAE;IAC9B,IAAI,IAAI,CAACnI,UAAU,KAAK,MAAM,EAAE;MAC5B,MAAMoI,YAAY,GAAGD,eAAe,YAAYhX,gBAAgB,GAC1DgX,eAAe,CAACE,UAAU,GAC1BF,eAAe,IAAI,IAAI,CAACjO,WAAW;MACzC,OAAOkO,YAAY,CAAC1M,aAAa,CAAC4M,qBAAqB,CAAC,CAAC,CAACC,KAAK;IACnE;IACA,OAAO,IAAI,CAACvI,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAACA,UAAU;EAC1D;EACA;EACAxB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC1D,OAAO,EAAE;MACd,KAAK,MAAMS,MAAM,IAAI,IAAI,CAACT,OAAO,EAAE;QAC/BS,MAAM,CAACtB,kBAAkB,CAACuJ,YAAY,CAAC,CAAC;MAC5C;IACJ;EACJ;EACA;EACA/B,eAAeA,CAAA,EAAG;IACd,IAAI,CAACvF,WAAW,GAAG,IAAIjJ,0BAA0B,CAAC,IAAI,CAAC6H,OAAO,CAAC,CAC1D4H,aAAa,CAAC,IAAI,CAAC9C,yBAAyB,CAAC,CAC7C4I,uBAAuB,CAAC,CAAC,CACzBlF,yBAAyB,CAAC,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,CACxDuE,cAAc,CAAC,CAAC,CAChBC,cAAc,CAAC,CAAC,CAChBC,uBAAuB,CAAC,CAAC,UAAU,CAAC,CAAC,CACrCC,aAAa,CAAC,IAAI,CAACV,cAAc,CAAC;IACvC,IAAI,CAAChM,WAAW,CAAC2M,MAAM,CAAC1H,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAACC,SAAS,EAAE;QAChB;QACA;QACA,IAAI,CAAC,IAAI,CAAC5I,QAAQ,IAAI,IAAI,CAAC0D,WAAW,CAACmK,UAAU,EAAE;UAC/C,IAAI,CAACnK,WAAW,CAACmK,UAAU,CAACC,qBAAqB,CAAC,CAAC;QACvD;QACA;QACA;QACA,IAAI,CAACwC,KAAK,CAAC,CAAC;QACZ,IAAI,CAAC/F,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF,IAAI,CAAC7G,WAAW,CAACgF,MAAM,CAACC,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAAC7E,UAAU,IAAI,IAAI,CAACb,KAAK,EAAE;QAC/B,IAAI,CAACJ,qBAAqB,CAAC,IAAI,CAACa,WAAW,CAACC,eAAe,IAAI,CAAC,CAAC;MACrE,CAAC,MACI,IAAI,CAAC,IAAI,CAACG,UAAU,IAAI,CAAC,IAAI,CAAC9D,QAAQ,IAAI,IAAI,CAAC0D,WAAW,CAACmK,UAAU,EAAE;QACxE,IAAI,CAACnK,WAAW,CAACmK,UAAU,CAACC,qBAAqB,CAAC,CAAC;MACvD;IACJ,CAAC,CAAC;EACN;EACA;EACArE,aAAaA,CAAA,EAAG;IACZ,MAAM8G,kBAAkB,GAAG5U,KAAK,CAAC,IAAI,CAAC2G,OAAO,CAACqF,OAAO,EAAE,IAAI,CAACrD,QAAQ,CAAC;IACrE,IAAI,CAACoD,sBAAsB,CAACE,IAAI,CAAC5L,SAAS,CAACuU,kBAAkB,CAAC,CAAC,CAAC5H,SAAS,CAACQ,KAAK,IAAI;MAC/E,IAAI,CAACqH,SAAS,CAACrH,KAAK,CAAC/H,MAAM,EAAE+H,KAAK,CAACsH,WAAW,CAAC;MAC/C,IAAItH,KAAK,CAACsH,WAAW,IAAI,CAAC,IAAI,CAACzQ,QAAQ,IAAI,IAAI,CAAC8D,UAAU,EAAE;QACxD,IAAI,CAACyG,KAAK,CAAC,CAAC;QACZ,IAAI,CAAC+F,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF;IACA;IACA3U,KAAK,CAAC,GAAG,IAAI,CAAC2G,OAAO,CAACvG,GAAG,CAACgH,MAAM,IAAIA,MAAM,CAAC2N,aAAa,CAAC,CAAC,CACrD9I,IAAI,CAAC5L,SAAS,CAACuU,kBAAkB,CAAC,CAAC,CACnC5H,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAAClH,kBAAkB,CAACqH,aAAa,CAAC,CAAC;MACvC,IAAI,CAACtE,YAAY,CAAC0B,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;EACAsK,SAASA,CAACzN,MAAM,EAAE0N,WAAW,EAAE;IAC3B,MAAME,WAAW,GAAG,IAAI,CAAChM,eAAe,CAAC2K,UAAU,CAACvM,MAAM,CAAC;IAC3D,IAAI,CAAC,IAAI,CAAC0E,wBAAwB,IAAI1E,MAAM,CAAC1B,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAACkF,SAAS,EAAE;MAC3ExD,MAAM,CAACyG,QAAQ,CAAC,CAAC;MACjB,IAAI,CAAC7E,eAAe,CAACkK,KAAK,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACxN,KAAK,IAAI,IAAI,EAAE;QACpB,IAAI,CAACuP,iBAAiB,CAAC7N,MAAM,CAAC1B,KAAK,CAAC;MACxC;IACJ,CAAC,MACI;MACD,IAAIsP,WAAW,KAAK5N,MAAM,CAAC2J,QAAQ,EAAE;QACjC3J,MAAM,CAAC2J,QAAQ,GACT,IAAI,CAAC/H,eAAe,CAAC2E,MAAM,CAACvG,MAAM,CAAC,GACnC,IAAI,CAAC4B,eAAe,CAAC6E,QAAQ,CAACzG,MAAM,CAAC;MAC/C;MACA,IAAI0N,WAAW,EAAE;QACb,IAAI,CAAC/M,WAAW,CAACmN,aAAa,CAAC9N,MAAM,CAAC;MAC1C;MACA,IAAI,IAAI,CAAC/C,QAAQ,EAAE;QACf,IAAI,CAACkP,WAAW,CAAC,CAAC;QAClB,IAAIuB,WAAW,EAAE;UACb;UACA;UACA;UACA;UACA,IAAI,CAACH,KAAK,CAAC,CAAC;QAChB;MACJ;IACJ;IACA,IAAIK,WAAW,KAAK,IAAI,CAAChM,eAAe,CAAC2K,UAAU,CAACvM,MAAM,CAAC,EAAE;MACzD,IAAI,CAAC6N,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACpM,YAAY,CAAC0B,IAAI,CAAC,CAAC;EAC5B;EACA;EACAgJ,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAClP,QAAQ,EAAE;MACf,MAAMsC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACU,OAAO,CAAC,CAAC;MACtC,IAAI,CAAC2B,eAAe,CAACmM,IAAI,CAAC,CAACC,CAAC,EAAErU,CAAC,KAAK;QAChC,OAAO,IAAI,CAAC2K,cAAc,GACpB,IAAI,CAACA,cAAc,CAAC0J,CAAC,EAAErU,CAAC,EAAE4F,OAAO,CAAC,GAClCA,OAAO,CAAC0O,OAAO,CAACD,CAAC,CAAC,GAAGzO,OAAO,CAAC0O,OAAO,CAACtU,CAAC,CAAC;MACjD,CAAC,CAAC;MACF,IAAI,CAAC8H,YAAY,CAAC0B,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACA0K,iBAAiBA,CAACK,aAAa,EAAE;IAC7B,IAAIC,WAAW;IACf,IAAI,IAAI,CAAClR,QAAQ,EAAE;MACfkR,WAAW,GAAG,IAAI,CAACxE,QAAQ,CAAC3Q,GAAG,CAACgH,MAAM,IAAIA,MAAM,CAAC1B,KAAK,CAAC;IAC3D,CAAC,MACI;MACD6P,WAAW,GAAG,IAAI,CAACxE,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACrL,KAAK,GAAG4P,aAAa;IACrE;IACA,IAAI,CAACpK,MAAM,GAAGqK,WAAW;IACzB,IAAI,CAAC/I,WAAW,CAACiD,IAAI,CAAC8F,WAAW,CAAC;IAClC,IAAI,CAACpM,SAAS,CAACoM,WAAW,CAAC;IAC3B,IAAI,CAAChJ,eAAe,CAACkD,IAAI,CAAC,IAAI,CAACxH,eAAe,CAACsN,WAAW,CAAC,CAAC;IAC5D,IAAI,CAACzP,kBAAkB,CAACuJ,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACID,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACrH,WAAW,EAAE;MAClB,IAAI,IAAI,CAACiJ,KAAK,EAAE;QACZ;QACA;QACA;QACA,IAAIwE,uBAAuB,GAAG,CAAC,CAAC;QAChC,KAAK,IAAIrO,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACR,OAAO,CAAC8O,MAAM,EAAEtO,KAAK,EAAE,EAAE;UACtD,MAAMC,MAAM,GAAG,IAAI,CAACT,OAAO,CAAC+O,GAAG,CAACvO,KAAK,CAAC;UACtC,IAAI,CAACC,MAAM,CAAC0C,QAAQ,EAAE;YAClB0L,uBAAuB,GAAGrO,KAAK;YAC/B;UACJ;QACJ;QACA,IAAI,CAACY,WAAW,CAACmN,aAAa,CAACM,uBAAuB,CAAC;MAC3D,CAAC,MACI;QACD,IAAI,CAACzN,WAAW,CAACmN,aAAa,CAAC,IAAI,CAAClM,eAAe,CAAC+H,QAAQ,CAAC,CAAC,CAAC,CAAC;MACpE;IACJ;EACJ;EACA;EACAjC,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAAC3G,UAAU,IAAI,CAAC,IAAI,CAAC2B,QAAQ,IAAI,IAAI,CAACnD,OAAO,EAAE8O,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC5L,WAAW;EAC/F;EACA;EACA8K,KAAKA,CAAChO,OAAO,EAAE;IACX,IAAI,CAACZ,WAAW,CAACwB,aAAa,CAACoN,KAAK,CAAChO,OAAO,CAAC;EACjD;EACA;EACApC,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACD,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAMqR,OAAO,GAAG,IAAI,CAACvP,gBAAgB,EAAEwP,UAAU,CAAC,CAAC,IAAI,IAAI;IAC3D,MAAMC,eAAe,GAAGF,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE;IACpD,OAAO,IAAI,CAACrK,cAAc,GAAGuK,eAAe,GAAG,IAAI,CAACvK,cAAc,GAAGqK,OAAO;EAChF;EACA;EACAG,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC7I,SAAS,IAAI,IAAI,CAAClF,WAAW,IAAI,IAAI,CAACA,WAAW,CAACmK,UAAU,EAAE;MACnE,OAAO,IAAI,CAACnK,WAAW,CAACmK,UAAU,CAAC9N,EAAE;IACzC;IACA,OAAO,IAAI;EACf;EACA;EACA6J,yBAAyBA,CAAA,EAAG;IACxB,IAAI,IAAI,CAAC3J,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,IAAIoB,KAAK,GAAG,IAAI,CAACU,gBAAgB,EAAEwP,UAAU,CAAC,CAAC,IAAI,EAAE;IACrD,IAAI,IAAI,CAACtK,cAAc,EAAE;MACrB5F,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC4F,cAAc;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC5F,KAAK,EAAE;MACRA,KAAK,GAAG,IAAI,CAAC2D,QAAQ;IACzB;IACA,OAAO3D,KAAK;EAChB;EACA;AACJ;AACA;AACA;EACI,IAAIqQ,cAAcA,CAAA,EAAG;IACjB,MAAMtO,OAAO,GAAG,IAAI,CAAC1B,WAAW,CAACwB,aAAa;IAC9C,MAAMyO,mBAAmB,GAAGvO,OAAO,CAACwO,YAAY,CAAC,kBAAkB,CAAC;IACpE,OAAOD,mBAAmB,EAAEE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;EAChD;EACA;AACJ;AACA;AACA;EACIC,iBAAiBA,CAACC,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACX,MAAM,EAAE;MACZ,IAAI,CAAC1P,WAAW,CAACwB,aAAa,CAAC2G,YAAY,CAAC,kBAAkB,EAAEkI,GAAG,CAAChF,IAAI,CAAC,GAAG,CAAC,CAAC;IAClF,CAAC,MACI;MACD,IAAI,CAACrL,WAAW,CAACwB,aAAa,CAAC4G,eAAe,CAAC,kBAAkB,CAAC;IACtE;EACJ;EACA;AACJ;AACA;AACA;EACIkI,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC1B,KAAK,CAAC,CAAC;IACZ,IAAI,CAAC9F,IAAI,CAAC,CAAC;EACf;EACA;AACJ;AACA;AACA;EACI,IAAIyH,gBAAgBA,CAAA,EAAG;IACnB;IACA;IACA,OAAO,IAAI,CAACrJ,SAAS,IAAI,CAAC,IAAI,CAAC+D,KAAK,IAAK,IAAI,CAACvH,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC9G,WAAY;EAChF;EACA,OAAO4T,IAAI,YAAAC,kBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwF7Q,SAAS;EAAA;EAC5G,OAAO8Q,IAAI,kBAD8ExZ,EAAE,CAAAyZ,iBAAA;IAAAC,IAAA,EACJhR,SAAS;IAAAiR,SAAA;IAAAC,cAAA,WAAAC,yBAAA7U,EAAA,EAAAC,GAAA,EAAA6U,QAAA;MAAA,IAAA9U,EAAA;QADPhF,EAAE,CAAA+Z,cAAA,CAAAD,QAAA,EAIjBzR,kBAAkB;QAJHrI,EAAE,CAAA+Z,cAAA,CAAAD,QAAA,EAI8D5V,SAAS;QAJzElE,EAAE,CAAA+Z,cAAA,CAAAD,QAAA,EAIyI1V,YAAY;MAAA;MAAA,IAAAY,EAAA;QAAA,IAAAgV,EAAA;QAJvJha,EAAE,CAAAia,cAAA,CAAAD,EAAA,GAAFha,EAAE,CAAAka,WAAA,QAAAjV,GAAA,CAAAgB,aAAA,GAAA+T,EAAA,CAAAG,KAAA;QAAFna,EAAE,CAAAia,cAAA,CAAAD,EAAA,GAAFha,EAAE,CAAAka,WAAA,QAAAjV,GAAA,CAAAwE,OAAA,GAAAuQ,EAAA;QAAFha,EAAE,CAAAia,cAAA,CAAAD,EAAA,GAAFha,EAAE,CAAAka,WAAA,QAAAjV,GAAA,CAAAyE,YAAA,GAAAsQ,EAAA;MAAA;IAAA;IAAAI,SAAA,WAAAC,gBAAArV,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFhF,EAAE,CAAAsa,WAAA,CAAA3V,GAAA;QAAF3E,EAAE,CAAAsa,WAAA,CAAA1V,GAAA;QAAF5E,EAAE,CAAAsa,WAAA,CAIwZza,mBAAmB;MAAA;MAAA,IAAAmF,EAAA;QAAA,IAAAgV,EAAA;QAJ7aha,EAAE,CAAAia,cAAA,CAAAD,EAAA,GAAFha,EAAE,CAAAka,WAAA,QAAAjV,GAAA,CAAAyH,OAAA,GAAAsN,EAAA,CAAAG,KAAA;QAAFna,EAAE,CAAAia,cAAA,CAAAD,EAAA,GAAFha,EAAE,CAAAka,WAAA,QAAAjV,GAAA,CAAAmF,KAAA,GAAA4P,EAAA,CAAAG,KAAA;QAAFna,EAAE,CAAAia,cAAA,CAAAD,EAAA,GAAFha,EAAE,CAAAka,WAAA,QAAAjV,GAAA,CAAA0H,WAAA,GAAAqN,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAI,SAAA,WAC+0C,UAAU,mBAAmB,SAAS;IAAAC,QAAA;IAAAC,YAAA,WAAAC,uBAAA1V,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADv3ChF,EAAE,CAAAqG,UAAA,qBAAAsU,qCAAApU,MAAA;UAAA,OACJtB,GAAA,CAAAyB,cAAA,CAAAH,MAAqB,CAAC;QAAA,CAAd,CAAC,mBAAAqU,mCAAA;UAAA,OAAT3V,GAAA,CAAAwQ,QAAA,CAAS,CAAC;QAAA,CAAF,CAAC,kBAAAoF,kCAAA;UAAA,OAAT5V,GAAA,CAAAyQ,OAAA,CAAQ,CAAC;QAAA,CAAD,CAAC;MAAA;MAAA,IAAA1Q,EAAA;QADPhF,EAAE,CAAAiH,WAAA,OAAAhC,GAAA,CAAAiC,EAAA,cAAAjC,GAAA,CAAA2H,QAAA,IACQ,CAAC,GAAA3H,GAAA,CAAA+H,QAAA,mBAAA/H,GAAA,CAAA8K,SAAA,GAAA9K,GAAA,CAAAiC,EAAA,GAAI,QAAQ,GAAG,IAAI,mBAAAjC,GAAA,CAAA8K,SAAA,gBAAA9K,GAAA,CAAAmC,SAAA,IAAnB,IAAI,mBAAjBnC,GAAA,CAAAqI,QAAA,CAAAwN,QAAA,CAAkB,CAAC,mBAAnB7V,GAAA,CAAA2H,QAAA,CAAAkO,QAAA,CAAkB,CAAC,kBAAA7V,GAAA,CAAAyJ,UAAA,2BAAnBzJ,GAAA,CAAA2T,wBAAA,CAAyB,CAAC;QADxB5Y,EAAE,CAAA8G,WAAA,4BAAA7B,GAAA,CAAA2H,QACI,CAAC,2BAAA3H,GAAA,CAAAyJ,UAAD,CAAC,4BAAAzJ,GAAA,CAAAqI,QAAD,CAAC,yBAAArI,GAAA,CAAA6O,KAAD,CAAC,4BAAA7O,GAAA,CAAAkC,QAAD,CAAC;MAAA;IAAA;IAAA4T,MAAA;MAAAlP,mBAAA;MAAA7E,UAAA;MAAA4F,QAAA,8BAAuLlM,gBAAgB;MAAAmM,aAAA,wCAAqDnM,gBAAgB;MAAAsM,QAAA,8BAAuCxE,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG7H,eAAe,CAAC6H,KAAK,CAAE;MAAAyE,4BAAA,sEAAkGvM,gBAAgB;MAAA+E,WAAA;MAAA6H,QAAA,8BAAkE5M,gBAAgB;MAAAyG,QAAA,8BAAsCzG,gBAAgB;MAAAkN,sBAAA,0DAAgFlN,gBAAgB;MAAAmN,WAAA;MAAArF,KAAA;MAAApB,SAAA;MAAAgH,cAAA;MAAAC,iBAAA;MAAAE,yBAAA,gEAA4Q5N,eAAe;MAAA6N,cAAA;MAAAtH,EAAA;MAAAyH,UAAA;MAAAC,wBAAA,8DAA4JlO,gBAAgB;IAAA;IAAAsa,OAAA;MAAA/L,YAAA;MAAAC,aAAA;MAAAE,aAAA;MAAAC,eAAA;MAAAC,WAAA;IAAA;IAAA2L,QAAA;IAAAC,QAAA,GADjpClb,EAAE,CAAAmb,kBAAA,CACknE,CACrsE;MAAEjT,OAAO,EAAEzE,mBAAmB;MAAE2X,WAAW,EAAE1S;IAAU,CAAC,EACxD;MAAER,OAAO,EAAElE,2BAA2B;MAAEoX,WAAW,EAAE1S;IAAU,CAAC,CACnE,GAJoF1I,EAAE,CAAAqb,oBAAA;IAAAC,kBAAA,EAAAxW,GAAA;IAAAyW,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mBAAA3W,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAA4W,GAAA,GAAF5b,EAAE,CAAAoG,gBAAA;QAAFpG,EAAE,CAAA6b,eAAA,CAAAhX,GAAA;QAAF7E,EAAE,CAAAkF,cAAA,eAIkqB,CAAC;QAJrqBlF,EAAE,CAAAqG,UAAA,mBAAAyV,wCAAA;UAAF9b,EAAE,CAAAwG,aAAA,CAAAoV,GAAA;UAAA,OAAF5b,EAAE,CAAAyG,WAAA,CAIylBxB,GAAA,CAAA0M,IAAA,CAAK,CAAC;QAAA,CAAC,CAAC;QAJnmB3R,EAAE,CAAAkF,cAAA,YAImuB,CAAC;QAJtuBlF,EAAE,CAAA+F,mBAAA,IAAAhB,gCAAA,iBAIsvB,CAAC,IAAAe,gCAAA,iBAA+G,CAAC;QAJz2B9F,EAAE,CAAAoF,YAAA,CAImoC,CAAC;QAJtoCpF,EAAE,CAAAkF,cAAA,YAIqrC,CAAC,YAAyC,CAAC;QAJluClF,EAAE,CAAA+b,cAAA;QAAF/b,EAAE,CAAAkF,cAAA,YAIg7C,CAAC;QAJn7ClF,EAAE,CAAAgc,SAAA,aAIs9C,CAAC;QAJz9Chc,EAAE,CAAAoF,YAAA,CAIo+C,CAAC,CAAW,CAAC,CAAS,CAAC,CAAO,CAAC;QAJrgDpF,EAAE,CAAAic,UAAA,KAAA/V,iCAAA,0BAImrE,CAAC;QAJtrElG,EAAE,CAAAqG,UAAA,oBAAA6V,kDAAA;UAAFlc,EAAE,CAAAwG,aAAA,CAAAoV,GAAA;UAAA,OAAF5b,EAAE,CAAAyG,WAAA,CAIqlExB,GAAA,CAAAyM,KAAA,CAAM,CAAC;QAAA,CAAC,CAAC,2BAAAyK,yDAAA;UAJhmEnc,EAAE,CAAAwG,aAAA,CAAAoV,GAAA;UAAA,OAAF5b,EAAE,CAAAyG,WAAA,CAIonExB,GAAA,CAAAyM,KAAA,CAAM,CAAC;QAAA,CAAC,CAAC,4BAAA0K,0DAAA7V,MAAA;UAJ/nEvG,EAAE,CAAAwG,aAAA,CAAAoV,GAAA;UAAA,OAAF5b,EAAE,CAAAyG,WAAA,CAIopExB,GAAA,CAAAuQ,qBAAA,CAAAjP,MAA4B,CAAC;QAAA,CAAC,CAAC;MAAA;MAAA,IAAAvB,EAAA;QAAA,MAAAqX,wBAAA,GAJrrErc,EAAE,CAAAsc,WAAA;QAAFtc,EAAE,CAAAuF,SAAA,EAIkuB,CAAC;QAJruBvF,EAAE,CAAAiH,WAAA,OAAAhC,GAAA,CAAAkH,QAAA;QAAFnM,EAAE,CAAAuF,SAAA,CAIynC,CAAC;QAJ5nCvF,EAAE,CAAAgG,aAAA,CAAAf,GAAA,CAAA6O,KAAA,QAIynC,CAAC;QAJ5nC9T,EAAE,CAAAuF,SAAA,EAIuuD,CAAC;QAJ1uDvF,EAAE,CAAA+G,UAAA,wCAIuuD,CAAC,kCAAA9B,GAAA,CAAAoH,kBAAyD,CAAC,sCAAApH,GAAA,CAAAmH,eAA0D,CAAC,8BAAAnH,GAAA,CAAA8G,uBAAA,IAAAsQ,wBAAmF,CAAC,iCAAApX,GAAA,CAAA0E,UAAgD,CAAC,6BAAA1E,GAAA,CAAA+G,aAA+C,CAAC,8CAAmD,CAAC;MAAA;IAAA;IAAAuQ,YAAA,GAA4mLzc,gBAAgB,EAAuID,mBAAmB,EAA4+BwD,OAAO;IAAAmZ,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC96R;AACA;EAAA,QAAA/O,SAAA,oBAAAA,SAAA,KAN6F3N,EAAE,CAAA2c,iBAAA,CAMJjU,SAAS,EAAc,CAAC;IACvGgR,IAAI,EAAE9Y,SAAS;IACfgc,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAE5B,QAAQ,EAAE,WAAW;MAAEwB,aAAa,EAAE5b,iBAAiB,CAACic,IAAI;MAAEJ,eAAe,EAAE5b,uBAAuB,CAACic,MAAM;MAAEC,IAAI,EAAE;QAC1I,MAAM,EAAE,UAAU;QAClB,eAAe,EAAE,SAAS;QAC1B,OAAO,EAAE,gBAAgB;QACzB,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,0BAA0B;QAC7C,sBAAsB,EAAE,kCAAkC;QAC1D,sBAAsB,EAAE,WAAW;QACnC,mBAAmB,EAAE,mBAAmB;QACxC,sBAAsB,EAAE,qBAAqB;QAC7C,sBAAsB,EAAE,qBAAqB;QAC7C,qBAAqB,EAAE,YAAY;QACnC,8BAA8B,EAAE,4BAA4B;QAC5D,iCAAiC,EAAE,UAAU;QAC7C,gCAAgC,EAAE,YAAY;QAC9C,iCAAiC,EAAE,UAAU;QAC7C,8BAA8B,EAAE,OAAO;QACvC,iCAAiC,EAAE,UAAU;QAC7C,WAAW,EAAE,wBAAwB;QACrC,SAAS,EAAE,YAAY;QACvB,QAAQ,EAAE;MACd,CAAC;MAAEC,SAAS,EAAE,CACV;QAAE/U,OAAO,EAAEzE,mBAAmB;QAAE2X,WAAW,EAAE1S;MAAU,CAAC,EACxD;QAAER,OAAO,EAAElE,2BAA2B;QAAEoX,WAAW,EAAE1S;MAAU,CAAC,CACnE;MAAEwU,OAAO,EAAE,CAACpd,gBAAgB,EAAED,mBAAmB,EAAEwD,OAAO,CAAC;MAAEqY,QAAQ,EAAE,ktEAAktE;MAAEc,MAAM,EAAE,CAAC,+5JAA+5J;IAAE,CAAC;EACntO,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE/S,OAAO,EAAE,CAAC;MAClDiQ,IAAI,EAAE3Y,eAAe;MACrB6b,IAAI,EAAE,CAAC1Y,SAAS,EAAE;QAAEiZ,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEzT,YAAY,EAAE,CAAC;MACfgQ,IAAI,EAAE3Y,eAAe;MACrB6b,IAAI,EAAE,CAACxY,YAAY,EAAE;QAAE+Y,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAElX,aAAa,EAAE,CAAC;MAChByT,IAAI,EAAE1Y,YAAY;MAClB4b,IAAI,EAAE,CAACvU,kBAAkB;IAC7B,CAAC,CAAC;IAAEwD,mBAAmB,EAAE,CAAC;MACtB6N,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAElQ,OAAO,EAAE,CAAC;MACVgN,IAAI,EAAExY,SAAS;MACf0b,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAExS,KAAK,EAAE,CAAC;MACRsP,IAAI,EAAExY,SAAS;MACf0b,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEjQ,WAAW,EAAE,CAAC;MACd+M,IAAI,EAAExY,SAAS;MACf0b,IAAI,EAAE,CAAC/c,mBAAmB;IAC9B,CAAC,CAAC;IAAEmH,UAAU,EAAE,CAAC;MACb0S,IAAI,EAAEzY;IACV,CAAC,CAAC;IAAE2L,QAAQ,EAAE,CAAC;MACX8M,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmM,aAAa,EAAE,CAAC;MAChB6M,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsM,QAAQ,EAAE,CAAC;MACX0M,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC;QACCQ,SAAS,EAAG5U,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG7H,eAAe,CAAC6H,KAAK;MACpE,CAAC;IACT,CAAC,CAAC;IAAEyE,4BAA4B,EAAE,CAAC;MAC/ByM,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+E,WAAW,EAAE,CAAC;MACdiU,IAAI,EAAEzY;IACV,CAAC,CAAC;IAAEqM,QAAQ,EAAE,CAAC;MACXoM,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyG,QAAQ,EAAE,CAAC;MACXuS,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkN,sBAAsB,EAAE,CAAC;MACzB8L,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmN,WAAW,EAAE,CAAC;MACd6L,IAAI,EAAEzY;IACV,CAAC,CAAC;IAAEuH,KAAK,EAAE,CAAC;MACRkR,IAAI,EAAEzY;IACV,CAAC,CAAC;IAAEmG,SAAS,EAAE,CAAC;MACZsS,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAExO,cAAc,EAAE,CAAC;MACjBsL,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEvO,iBAAiB,EAAE,CAAC;MACpBqL,IAAI,EAAEzY;IACV,CAAC,CAAC;IAAEsN,yBAAyB,EAAE,CAAC;MAC5BmL,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEzc;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE6N,cAAc,EAAE,CAAC;MACjBkL,IAAI,EAAEzY;IACV,CAAC,CAAC;IAAEiG,EAAE,EAAE,CAAC;MACLwS,IAAI,EAAEzY;IACV,CAAC,CAAC;IAAE0N,UAAU,EAAE,CAAC;MACb+K,IAAI,EAAEzY;IACV,CAAC,CAAC;IAAE2N,wBAAwB,EAAE,CAAC;MAC3B8K,IAAI,EAAEzY,KAAK;MACX2b,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1c;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuO,YAAY,EAAE,CAAC;MACfyK,IAAI,EAAEvY;IACV,CAAC,CAAC;IAAE+N,aAAa,EAAE,CAAC;MAChBwK,IAAI,EAAEvY,MAAM;MACZyb,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAExN,aAAa,EAAE,CAAC;MAChBsK,IAAI,EAAEvY,MAAM;MACZyb,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEvN,eAAe,EAAE,CAAC;MAClBqK,IAAI,EAAEvY;IACV,CAAC,CAAC;IAAEmO,WAAW,EAAE,CAAC;MACdoK,IAAI,EAAEvY;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMkc,gBAAgB,CAAC;EACnB,OAAOhE,IAAI,YAAAiE,yBAAA/D,iBAAA;IAAA,YAAAA,iBAAA,IAAwF8D,gBAAgB;EAAA;EACnH,OAAOE,IAAI,kBA3H8Evd,EAAE,CAAAwd,iBAAA;IAAA9D,IAAA,EA2HJ2D,gBAAgB;IAAA1D,SAAA;IAAAuB,QAAA,GA3Hdlb,EAAE,CAAAmb,kBAAA,CA2H6E,CAAC;MAAEjT,OAAO,EAAEG,kBAAkB;MAAE+S,WAAW,EAAEiC;IAAiB,CAAC,CAAC;EAAA;AAC5O;AACA;EAAA,QAAA1P,SAAA,oBAAAA,SAAA,KA7H6F3N,EAAE,CAAA2c,iBAAA,CA6HJU,gBAAgB,EAAc,CAAC;IAC9G3D,IAAI,EAAEtY,SAAS;IACfwb,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BI,SAAS,EAAE,CAAC;QAAE/U,OAAO,EAAEG,kBAAkB;QAAE+S,WAAW,EAAEiC;MAAiB,CAAC;IAC9E,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMI,eAAe,CAAC;EAClB,OAAOpE,IAAI,YAAAqE,wBAAAnE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFkE,eAAe;EAAA;EAClH,OAAOE,IAAI,kBAvI8E3d,EAAE,CAAA4d,gBAAA;IAAAlE,IAAA,EAuIS+D;EAAe;EAMnH,OAAOI,IAAI,kBA7I8E7d,EAAE,CAAA8d,gBAAA;IAAAb,SAAA,EA6IqC,CAAChV,mCAAmC,CAAC;IAAAiV,OAAA,GAAYnd,aAAa,EAAEyE,eAAe,EAAEC,eAAe,EAAElD,mBAAmB,EAC7OmD,kBAAkB,EAClBF,eAAe,EACfC,eAAe;EAAA;AAC3B;AACA;EAAA,QAAAkJ,SAAA,oBAAAA,SAAA,KAlJ6F3N,EAAE,CAAA2c,iBAAA,CAkJJc,eAAe,EAAc,CAAC;IAC7G/D,IAAI,EAAErY,QAAQ;IACdub,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CAACnd,aAAa,EAAEyE,eAAe,EAAEC,eAAe,EAAEiE,SAAS,EAAE2U,gBAAgB,CAAC;MACvFU,OAAO,EAAE,CACLxc,mBAAmB,EACnBmD,kBAAkB,EAClBgE,SAAS,EACT2U,gBAAgB,EAChB7Y,eAAe,EACfC,eAAe,CAClB;MACDwY,SAAS,EAAE,CAAChV,mCAAmC;IACnD,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASwV,eAAe,IAAIxZ,CAAC,EAAEyD,0BAA0B,IAAIwQ,CAAC,EAAEpQ,2CAA2C,IAAIjE,CAAC,EAAEmE,iBAAiB,IAAIjE,CAAC,EAAEkE,mCAAmC,IAAI9D,CAAC,EAAEkE,kBAAkB,IAAI2V,CAAC,EAAE1V,eAAe,IAAI2V,CAAC,EAAEvV,SAAS,IAAIwV,CAAC,EAAEb,gBAAgB,IAAI/Z,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}