# PRODUCTION ENVIRONMENT CONFIGURATION
# =====================================
# ⚠️  NEVER COMMIT THIS FILE TO VERSION CONTROL
# ⚠️  REPLACE ALL PLACEHOLDER VALUES WITH REAL PRODUCTION VALUES

# Server Configuration
PORT=3002
HOST=0.0.0.0
NODE_ENV=production

# Frontend URL (Update with your production domain)
FRONTEND_URL=https://your-production-domain.com

# JWT Configuration - PRODUCTION SECRETS
# 🔐 Generate new secrets for production using: node scripts/generate-jwt-secret.js
JWT_SECRET=REPLACE_WITH_PRODUCTION_JWT_SECRET_FROM_GENERATOR
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ALGORITHM=HS256

# Database Configuration - PRODUCTION
# 🗄️  Replace with your production database credentials
DATABASE_URL=****************************************/database_name
DB_HOST=your-production-db-host.com
DB_PORT=5432
DB_NAME=secure_backend_prod
DB_USER=your_db_username
DB_PASSWORD=your_secure_db_password
DB_SSL=true
USE_POSTGRESQL="true"

# Razorpay Configuration - PRODUCTION
# 💳 Replace with your live Razorpay credentials
RAZORPAY_KEY_ID=rzp_live_your_live_key_id
RAZORPAY_KEY_SECRET=your_live_razorpay_secret
RAZORPAY_WEBHOOK_SECRET=your_live_webhook_secret

# Email Configuration - PRODUCTION
# 📧 Configure with your production email service
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-production-email-app-password
EMAIL_FROM=<EMAIL>
EMAIL_SERVICE=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false

# SMS Configuration - PRODUCTION
# 📱 Configure with your production SMS service (Twilio)
TWILIO_SID=your_production_twilio_sid
TWILIO_TOKEN=your_production_twilio_token
TWILIO_PHONE=your_production_twilio_phone_number

# Security Configuration - PRODUCTION
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCK_TIME=30
SESSION_TIMEOUT=1800000

# Rate Limiting - PRODUCTION
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# CORS Configuration - PRODUCTION
CORS_ORIGIN=https://your-production-domain.com
CORS_CREDENTIALS=true

# SSL/TLS Configuration
SSL_CERT_PATH=/path/to/ssl/certificate.crt
SSL_KEY_PATH=/path/to/ssl/private.key

# Monitoring and Logging
LOG_LEVEL=info
LOG_FILE=/var/log/secure-backend/app.log
ENABLE_REQUEST_LOGGING=true

# Redis Configuration (for session storage in production)
REDIS_URL=redis://your-redis-host:6379
REDIS_PASSWORD=your_redis_password

# Additional Security Headers
HSTS_MAX_AGE=31536000
CSP_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'

# Health Check Configuration
HEALTH_CHECK_ENDPOINT=/health
HEALTH_CHECK_TIMEOUT=5000
