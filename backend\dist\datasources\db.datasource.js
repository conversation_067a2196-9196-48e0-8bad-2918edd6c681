"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DbDataSource = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
// Database configuration based on environment
function getDatabaseConfig() {
    const usePostgreSQL = process.env.USE_POSTGRESQL === 'true' ||
        process.env.NODE_ENV === 'production' ||
        process.env.DATABASE_URL;
    if (usePostgreSQL) {
        console.log('🐘 Using PostgreSQL database');
        return {
            name: 'db',
            connector: 'postgresql',
            url: process.env.DATABASE_URL,
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '5432'),
            user: process.env.DB_USER || 'postgres',
            password: process.env.DB_PASSWORD || 'password',
            database: process.env.DB_NAME || 'secure_backend',
            // SSL configuration
            ssl: process.env.DB_SSL === 'true' ? {
                rejectUnauthorized: false
            } : false,
            // Connection pool settings
            min: 5,
            max: 20,
            acquireTimeoutMillis: 60000,
            idleTimeoutMillis: 600000,
            // Additional PostgreSQL settings
            schema: 'public',
            debug: process.env.NODE_ENV === 'development'
        };
    }
    else {
        console.log('💾 Using in-memory database with file persistence');
        return {
            name: 'db',
            connector: 'memory',
            localStorage: '',
            file: './data/db.json'
        };
    }
}
const config = getDatabaseConfig();
// Observe application's life cycle to disconnect the datasource when
// application is stopped. This allows the application to be shut down
// gracefully. The `stop()` method is inherited from `juggler.DataSource`.
// Learn more at https://loopback.io/doc/en/lb4/Life-cycle.html
let DbDataSource = class DbDataSource extends repository_1.juggler.DataSource {
    constructor(dsConfig = config) {
        super(dsConfig);
    }
};
exports.DbDataSource = DbDataSource;
DbDataSource.dataSourceName = 'db';
DbDataSource.defaultConfig = config;
exports.DbDataSource = DbDataSource = tslib_1.__decorate([
    (0, core_1.lifeCycleObserver)('datasource'),
    tslib_1.__param(0, (0, core_1.inject)('datasources.config.db', { optional: true })),
    tslib_1.__metadata("design:paramtypes", [Object])
], DbDataSource);
//# sourceMappingURL=db.datasource.js.map