"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Payment = void 0;
const tslib_1 = require("tslib");
const repository_1 = require("@loopback/repository");
const user_model_1 = require("./user.model");
let Payment = class Payment extends repository_1.Entity {
    constructor(data) {
        super(data);
    }
};
exports.Payment = Payment;
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        id: true,
        generated: true,
        postgresql: {
            columnName: 'id'
        }
    }),
    tslib_1.__metadata("design:type", String)
], Payment.prototype, "id", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        postgresql: {
            columnName: 'razorpay_order_id'
        }
    }),
    tslib_1.__metadata("design:type", String)
], Payment.prototype, "razorpayOrderId", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            columnName: 'razorpay_payment_id'
        }
    }),
    tslib_1.__metadata("design:type", String)
], Payment.prototype, "razorpayPaymentId", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            columnName: 'razorpay_signature'
        }
    }),
    tslib_1.__metadata("design:type", String)
], Payment.prototype, "razorpaySignature", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        required: true,
        jsonSchema: {
            minimum: 1,
            maximum: 1000000,
        },
    }),
    tslib_1.__metadata("design:type", Number)
], Payment.prototype, "amount", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        default: 'INR',
        jsonSchema: {
            enum: ['INR', 'USD'],
        },
    }),
    tslib_1.__metadata("design:type", String)
], Payment.prototype, "currency", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        default: 'pending',
        jsonSchema: {
            enum: ['pending', 'paid', 'failed', 'cancelled', 'refunded'],
        },
    }),
    tslib_1.__metadata("design:type", String)
], Payment.prototype, "status", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
    }),
    tslib_1.__metadata("design:type", String)
], Payment.prototype, "description", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'object',
    }),
    tslib_1.__metadata("design:type", Object)
], Payment.prototype, "metadata", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        default: () => new Date(),
        postgresql: {
            columnName: 'created_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], Payment.prototype, "createdAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        default: () => new Date(),
        postgresql: {
            columnName: 'updated_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], Payment.prototype, "updatedAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        postgresql: {
            columnName: 'paid_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], Payment.prototype, "paidAt", void 0);
tslib_1.__decorate([
    (0, repository_1.belongsTo)(() => user_model_1.User, {}, {
        postgresql: {
            columnName: 'user_id'
        }
    }),
    tslib_1.__metadata("design:type", String)
], Payment.prototype, "userId", void 0);
exports.Payment = Payment = tslib_1.__decorate([
    (0, repository_1.model)({
        settings: {
            postgresql: {
                table: 'payment'
            }
        }
    }),
    tslib_1.__metadata("design:paramtypes", [Object])
], Payment);
//# sourceMappingURL=payment.model.js.map