{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDividerModule } from '@angular/material/divider';\n// Components\nimport { PaymentTestComponent } from '../../components/payment/payment-test/payment-test.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'test',\n  pathMatch: 'full'\n}, {\n  path: 'test',\n  component: PaymentTestComponent\n}];\nexport class PaymentModule {\n  static #_ = this.ɵfac = function PaymentModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PaymentModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: PaymentModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(routes),\n    // Angular Material\n    MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatSelectModule, MatProgressSpinnerModule, MatSnackBarModule, MatDividerModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PaymentModule, {\n    declarations: [PaymentTestComponent],\n    imports: [CommonModule, ReactiveFormsModule, i1.RouterModule,\n    // Angular Material\n    MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatSelectModule, MatProgressSpinnerModule, MatSnackBarModule, MatDividerModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "RouterModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatDividerModule", "PaymentTestComponent", "routes", "path", "redirectTo", "pathMatch", "component", "PaymentModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\modules\\payment\\payment.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule, Routes } from '@angular/router';\n\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDividerModule } from '@angular/material/divider';\n\n// Components\nimport { PaymentTestComponent } from '../../components/payment/payment-test/payment-test.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    redirectTo: 'test',\n    pathMatch: 'full'\n  },\n  {\n    path: 'test',\n    component: PaymentTestComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    PaymentTestComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(routes),\n    \n    // Angular Material\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatDividerModule\n  ]\n})\nexport class PaymentModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,YAAY,QAAgB,iBAAiB;AAEtD;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,oBAAoB,QAAQ,8DAA8D;;;AAEnG,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,MAAM;EAClBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,SAAS,EAAEL;CACZ,CACF;AAuBD,OAAM,MAAOM,aAAa;EAAA,QAAAC,CAAA,G;qCAAbD,aAAa;EAAA;EAAA,QAAAE,EAAA,G;UAAbF;EAAa;EAAA,QAAAG,EAAA,G;cAhBtBrB,YAAY,EACZC,mBAAmB,EACnBC,YAAY,CAACoB,QAAQ,CAACT,MAAM,CAAC;IAE7B;IACAV,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,eAAe,EACfC,wBAAwB,EACxBC,iBAAiB,EACjBC,gBAAgB;EAAA;;;2EAGPO,aAAa;IAAAK,YAAA,GAnBtBX,oBAAoB;IAAAY,OAAA,GAGpBxB,YAAY,EACZC,mBAAmB,EAAAwB,EAAA,CAAAvB,YAAA;IAGnB;IACAC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,eAAe,EACfC,wBAAwB,EACxBC,iBAAiB,EACjBC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}