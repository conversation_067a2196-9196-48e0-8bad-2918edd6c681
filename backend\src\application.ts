import {BootMixin} from '@loopback/boot';
import {ApplicationConfig} from '@loopback/core';
import {
  RestExplorerBindings,
  RestExplorerComponent,
} from '@loopback/rest-explorer';
import {RepositoryMixin} from '@loopback/repository';
import {RestApplication} from '@loopback/rest';
import {ServiceMixin} from '@loopback/service-proxy';
import path from 'path';
import {
  AuthenticationComponent,
  registerAuthenticationStrategy,
} from '@loopback/authentication';
import {
  JWTAuthenticationComponent,
  SECURITY_SCHEME_SPEC,
  UserServiceBindings,
  Credentials,
} from '@loopback/authentication-jwt';
import {AuthorizationComponent} from '@loopback/authorization';
import {SecurityBindings} from '@loopback/security';
import {MyUserService, JwtService} from './services';
import {SecuritySequence} from './sequence';

export {ApplicationConfig};

export class SecureBackendApplication extends BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) {
  constructor(options: ApplicationConfig = {}) {
    super(options);

    // Configure CORS
    this.configure('rest.cors').to({
      origin: process.env.FRONTEND_URL || 'http://localhost:3001',
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-CSRF-Token'
      ],
      credentials: true,
      preflightContinue: false,
      optionsSuccessStatus: 204
    });

    // Set up the custom sequence
    this.sequence(SecuritySequence);

    // Set up default home page
    this.static('/', path.join(__dirname, '../public'));

    // Customize @loopback/rest-explorer configuration here
    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });
    this.component(RestExplorerComponent);

    // Add authentication component
    this.component(AuthenticationComponent);
    this.component(JWTAuthenticationComponent);
    this.component(AuthorizationComponent);

    // Use default JWT user service for now
    // this.bind(UserServiceBindings.USER_SERVICE).toClass(MyUserService);

    // Bind custom JWT service
    this.bind('services.JwtService').toClass(JwtService);

    // Bind other services
    this.bind('services.SecurityService').toClass(require('./services/security.service').SecurityService);
    this.bind('services.EmailService').toClass(require('./services/email.service').EmailService);
    this.bind('services.SmsService').toClass(require('./services/sms.service').SmsService);
    this.bind('services.PaymentService').toClass(require('./services/payment.service').PaymentService);

    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
    };
  }
}
