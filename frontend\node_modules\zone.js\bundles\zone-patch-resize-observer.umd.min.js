"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){!function e(n){n.__load_patch("ResizeObserver",(function(e,n,r){var t=e.ResizeObserver;if(t){var o=r.symbol("ResizeObserver");r.patchMethod(e,"ResizeObserver",(function(e){return function(e,r){var i=r.length>0?r[0]:null;return i&&(r[0]=function(e,r){for(var t=this,u={},a=n.current,c=0,f=e;c<f.length;c++){var p=f[c],s=p.target[o];s||(s=a);var v=u[s.name];v||(u[s.name]=v={entries:[],zone:s}),v.entries.push(p)}Object.keys(u).forEach((function(e){var o=u[e];o.zone!==n.current?o.zone.run(i,t,[o.entries,r],"ResizeObserver"):i.call(t,o.entries,r)}))}),r.length>0?new t(r[0]):new t}})),r.patchMethod(t.prototype,"observe",(function(e){return function(r,t){var i=t.length>0?t[0]:null;if(!i)return e.apply(r,t);var u=r[o];return u||(u=r[o]=[]),u.push(i),i[o]=n.current,e.apply(r,t)}})),r.patchMethod(t.prototype,"unobserve",(function(e){return function(n,r){var t=r.length>0?r[0]:null;if(!t)return e.apply(n,r);var i=n[o];if(i)for(var u=0;u<i.length;u++)if(i[u]===t){i.splice(u,1);break}return t[o]=void 0,e.apply(n,r)}})),r.patchMethod(t.prototype,"disconnect",(function(e){return function(n,r){var t=n[o];return t&&(t.forEach((function(e){e[o]=void 0})),n[o]=void 0),e.apply(n,r)}}))}}))}(Zone)}));