{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    if (this.authService.isAuthenticated && !this.authService.isTokenExpired()) {\n      // Check if email verification is required\n      if (route.data?.['requireEmailVerification'] && !this.authService.isEmailVerified) {\n        this.router.navigate(['/auth/verify-email']);\n        return false;\n      }\n      // Check if 2FA is required\n      if (route.data?.['require2FA'] && !this.authService.isTwoFactorEnabled) {\n        this.router.navigate(['/auth/setup-2fa']);\n        return false;\n      }\n      return true;\n    }\n    // Not authenticated, redirect to login\n    this.router.navigate(['/auth/login'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    return false;\n  }\n  static #_ = this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthGuard,\n    factory: AuthGuard.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "isAuthenticated", "isTokenExpired", "data", "isEmailVerified", "navigate", "isTwoFactorEnabled", "queryParams", "returnUrl", "url", "_", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    \n    if (this.authService.isAuthenticated && !this.authService.isTokenExpired()) {\n      // Check if email verification is required\n      if (route.data?.['requireEmailVerification'] && !this.authService.isEmailVerified) {\n        this.router.navigate(['/auth/verify-email']);\n        return false;\n      }\n\n      // Check if 2FA is required\n      if (route.data?.['require2FA'] && !this.authService.isTwoFactorEnabled) {\n        this.router.navigate(['/auth/setup-2fa']);\n        return false;\n      }\n\n      return true;\n    }\n\n    // Not authenticated, redirect to login\n    this.router.navigate(['/auth/login'], {\n      queryParams: { returnUrl: state.url }\n    });\n    return false;\n  }\n}\n"], "mappings": ";;;AAQA,OAAM,MAAOA,SAAS;EACpBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAG1B,IAAI,IAAI,CAACJ,WAAW,CAACK,eAAe,IAAI,CAAC,IAAI,CAACL,WAAW,CAACM,cAAc,EAAE,EAAE;MAC1E;MACA,IAAIH,KAAK,CAACI,IAAI,GAAG,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAACP,WAAW,CAACQ,eAAe,EAAE;QACjF,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;QAC5C,OAAO,KAAK;MACd;MAEA;MACA,IAAIN,KAAK,CAACI,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAACP,WAAW,CAACU,kBAAkB,EAAE;QACtE,IAAI,CAACT,MAAM,CAACQ,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QACzC,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb;IAEA;IACA,IAAI,CAACR,MAAM,CAACQ,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;MACpCE,WAAW,EAAE;QAAEC,SAAS,EAAER,KAAK,CAACS;MAAG;KACpC,CAAC;IACF,OAAO,KAAK;EACd;EAAC,QAAAC,CAAA,G;qCAhCUhB,SAAS,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAATvB,SAAS;IAAAwB,OAAA,EAATxB,SAAS,CAAAyB,IAAA;IAAAC,UAAA,EAFR;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}