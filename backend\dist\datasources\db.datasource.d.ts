import { LifeCycleObserver } from '@loopback/core';
import { juggler } from '@loopback/repository';
export declare class DbDataSource extends juggler.DataSource implements LifeCycleObserver {
    static dataSourceName: string;
    static readonly defaultConfig: {
        name: string;
        connector: string;
        url: string | undefined;
        host: string;
        port: number;
        user: string;
        password: string;
        database: string;
        ssl: boolean | {
            rejectUnauthorized: boolean;
        };
        min: number;
        max: number;
        acquireTimeoutMillis: number;
        idleTimeoutMillis: number;
        schema: string;
        debug: boolean;
        localStorage?: undefined;
        file?: undefined;
    } | {
        name: string;
        connector: string;
        localStorage: string;
        file: string;
        url?: undefined;
        host?: undefined;
        port?: undefined;
        user?: undefined;
        password?: undefined;
        database?: undefined;
        ssl?: undefined;
        min?: undefined;
        max?: undefined;
        acquireTimeoutMillis?: undefined;
        idleTimeoutMillis?: undefined;
        schema?: undefined;
        debug?: undefined;
    };
    constructor(dsConfig?: object);
}
