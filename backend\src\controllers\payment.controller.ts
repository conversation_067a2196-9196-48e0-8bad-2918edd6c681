import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
  post,
  get,
  param,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {PaymentRepository} from '../repositories';
import {PaymentService} from '../services';
import {Payment} from '../models';

@authenticate('jwt')
export class PaymentController {
  constructor(
    @inject(SecurityBindings.USER)
    public currentUserProfile: UserProfile,
    @repository(PaymentRepository) protected paymentRepository: PaymentRepository,
    @inject('services.PaymentService', {optional: true}) public paymentService?: PaymentService,
  ) {}

  @post('/payments/create-order')
  @response(200, {
    description: 'Create payment order',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            orderId: {type: 'string'},
            amount: {type: 'number'},
            currency: {type: 'string'},
            key: {type: 'string'},
          },
        },
      },
    },
  })
  async createOrder(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['amount', 'currency'],
            properties: {
              amount: {type: 'number', minimum: 1, maximum: 1000000},
              currency: {type: 'string', enum: ['INR', 'USD']},
              description: {type: 'string'},
            },
          },
        },
      },
    })
    request: {amount: number; currency: string; description?: string},
  ): Promise<{orderId: string; amount: number; currency: string; key: string}> {
    if (!this.paymentService) {
      throw new HttpErrors.ServiceUnavailable('Payment service is not available');
    }

    const userId = this.currentUserProfile[securityId];

    const order = await this.paymentService.createOrder(
      request.amount,
      request.currency,
      userId,
      request.description,
    );

    return {
      ...order,
      key: process.env.RAZORPAY_KEY_ID || 'your_key_id',
    };
  }

  @post('/payments/verify')
  @response(200, {
    description: 'Verify payment',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            success: {type: 'boolean'},
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async verifyPayment(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['orderId', 'paymentId', 'signature'],
            properties: {
              orderId: {type: 'string'},
              paymentId: {type: 'string'},
              signature: {type: 'string'},
            },
          },
        },
      },
    })
    request: {orderId: string; paymentId: string; signature: string},
  ): Promise<{success: boolean; message: string}> {
    if (!this.paymentService) {
      throw new HttpErrors.ServiceUnavailable('Payment service is not available');
    }

    try {
      const isValid = await this.paymentService.verifyPayment(
        request.orderId,
        request.paymentId,
        request.signature,
      );

      if (isValid) {
        return {
          success: true,
          message: 'Payment verified successfully',
        };
      } else {
        return {
          success: false,
          message: 'Payment verification failed',
        };
      }
    } catch (error) {
      throw new HttpErrors.BadRequest('Payment verification failed');
    }
  }

  @get('/payments/status/{orderId}')
  @response(200, {
    description: 'Get payment status',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            payment: {
              type: 'object',
              properties: {
                id: {type: 'string'},
                status: {type: 'string'},
                amount: {type: 'number'},
                currency: {type: 'string'},
                createdAt: {type: 'string'},
                paidAt: {type: 'string'},
              },
            },
          },
        },
      },
    },
  })
  async getPaymentStatus(
    @param.path.string('orderId') orderId: string,
  ): Promise<{payment: Payment | null}> {
    if (!this.paymentService) {
      throw new HttpErrors.ServiceUnavailable('Payment service is not available');
    }

    const payment = await this.paymentService.getPaymentStatus(orderId);
    
    // Ensure user can only access their own payments
    if (payment && payment.userId !== this.currentUserProfile[securityId]) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    return {payment};
  }

  @get('/payments/my-payments')
  @response(200, {
    description: 'Get user payments',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            payments: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: {type: 'string'},
                  status: {type: 'string'},
                  amount: {type: 'number'},
                  currency: {type: 'string'},
                  description: {type: 'string'},
                  createdAt: {type: 'string'},
                  paidAt: {type: 'string'},
                },
              },
            },
          },
        },
      },
    },
  })
  async getMyPayments(): Promise<{payments: Payment[]}> {
    if (!this.paymentService) {
      throw new HttpErrors.ServiceUnavailable('Payment service is not available');
    }

    const userId = this.currentUserProfile[securityId];
    const payments = await this.paymentService.getUserPayments(userId);
    return {payments};
  }

  @post('/payments/refund')
  @response(200, {
    description: 'Refund payment',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            success: {type: 'boolean'},
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async refundPayment(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['paymentId'],
            properties: {
              paymentId: {type: 'string'},
              amount: {type: 'number'},
            },
          },
        },
      },
    })
    request: {paymentId: string; amount?: number},
  ): Promise<{success: boolean; message: string}> {
    if (!this.paymentService) {
      throw new HttpErrors.ServiceUnavailable('Payment service is not available');
    }

    try {
      // Verify the payment belongs to the current user
      const payment = await this.paymentRepository.findOne({
        where: {razorpayPaymentId: request.paymentId},
      });

      if (!payment || payment.userId !== this.currentUserProfile[securityId]) {
        throw new HttpErrors.Forbidden('Access denied');
      }

      const success = await this.paymentService.refundPayment(
        request.paymentId,
        request.amount,
      );

      return {
        success,
        message: success ? 'Refund processed successfully' : 'Refund processing failed',
      };
    } catch (error) {
      throw new HttpErrors.BadRequest('Refund processing failed');
    }
  }
}

// Webhook controller (separate from authenticated routes)
export class PaymentWebhookController {
  constructor(
    @inject('services.PaymentService') public paymentService: PaymentService,
  ) {}

  @post('/payments/webhook')
  @response(200, {
    description: 'Payment webhook',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'string'},
          },
        },
      },
    },
  })
  async handleWebhook(
    @requestBody({
      content: {
        'application/json': {
          schema: {type: 'object'},
        },
      },
    })
    payload: any,
    @param.header.string('x-razorpay-signature') signature: string,
  ): Promise<{status: string}> {
    try {
      await this.paymentService.handleWebhook(payload, signature);
      return {status: 'success'};
    } catch (error) {
      throw new HttpErrors.BadRequest('Webhook processing failed');
    }
  }
}
