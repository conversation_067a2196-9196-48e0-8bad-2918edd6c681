"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const razorpay_1 = tslib_1.__importDefault(require("razorpay"));
const crypto = tslib_1.__importStar(require("crypto"));
const repositories_1 = require("../repositories");
let PaymentService = class PaymentService {
    constructor(paymentRepository) {
        this.paymentRepository = paymentRepository;
        this.razorpay = new razorpay_1.default({
            key_id: process.env.RAZORPAY_KEY_ID || 'your_key_id',
            key_secret: process.env.RAZORPAY_KEY_SECRET || 'your_key_secret',
        });
    }
    async createOrder(amount, currency, userId, description) {
        console.log('🔍 Payment order creation started');
        console.log('   Amount:', amount);
        console.log('   Currency:', currency);
        console.log('   User ID:', userId);
        console.log('   Description:', description);
        console.log('   Razorpay Key ID:', process.env.RAZORPAY_KEY_ID);
        console.log('   Razorpay Key Secret:', process.env.RAZORPAY_KEY_SECRET ? 'Set' : 'Not set');
        try {
            // Convert amount to smallest currency unit (paise for INR, cents for USD)
            const amountInSmallestUnit = currency === 'INR' ? amount * 100 : amount * 100;
            console.log('   Amount in smallest unit:', amountInSmallestUnit);
            const options = {
                amount: amountInSmallestUnit,
                currency: currency.toUpperCase(),
                receipt: `receipt_${Date.now()}`,
                notes: {
                    userId,
                    description: description || 'Payment for services',
                },
            };
            console.log('   Razorpay order options:', options);
            console.log('   Creating Razorpay order...');
            const order = await this.razorpay.orders.create(options);
            console.log('   Razorpay order created:', order.id);
            // Save payment record
            await this.paymentRepository.create({
                razorpayOrderId: order.id,
                amount,
                currency: currency.toUpperCase(),
                status: 'pending',
                description,
                userId,
                metadata: {
                    receipt: options.receipt,
                },
            });
            return {
                orderId: order.id,
                amount: amount,
                currency: currency.toUpperCase(),
            };
        }
        catch (error) {
            console.error('❌ Payment order creation failed:', error);
            console.error('   Error message:', error.message);
            console.error('   Error stack:', error.stack);
            if (error.response) {
                console.error('   Response status:', error.response.status);
                console.error('   Response data:', error.response.data);
            }
            throw new rest_1.HttpErrors.BadRequest('Failed to create payment order');
        }
    }
    async verifyPayment(orderId, paymentId, signature) {
        try {
            // Find payment record
            const payment = await this.paymentRepository.findOne({
                where: { razorpayOrderId: orderId },
            });
            if (!payment) {
                throw new rest_1.HttpErrors.NotFound('Payment record not found');
            }
            // Verify signature
            const body = orderId + '|' + paymentId;
            const expectedSignature = crypto
                .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET || 'your_key_secret')
                .update(body.toString())
                .digest('hex');
            const isAuthentic = expectedSignature === signature;
            if (isAuthentic) {
                // Update payment status
                await this.paymentRepository.updateById(payment.id, {
                    razorpayPaymentId: paymentId,
                    razorpaySignature: signature,
                    status: 'paid',
                    paidAt: new Date(),
                    updatedAt: new Date(),
                });
                return true;
            }
            else {
                // Update payment status to failed
                await this.paymentRepository.updateById(payment.id, {
                    status: 'failed',
                    updatedAt: new Date(),
                });
                return false;
            }
        }
        catch (error) {
            console.error('Payment verification failed:', error);
            throw new rest_1.HttpErrors.BadRequest('Payment verification failed');
        }
    }
    async getPaymentStatus(orderId) {
        try {
            const payment = await this.paymentRepository.findOne({
                where: { razorpayOrderId: orderId },
            });
            return payment;
        }
        catch (error) {
            console.error('Failed to get payment status:', error);
            return null;
        }
    }
    async getUserPayments(userId) {
        try {
            return await this.paymentRepository.find({
                where: { userId },
                order: ['createdAt DESC'],
            });
        }
        catch (error) {
            console.error('Failed to get user payments:', error);
            return [];
        }
    }
    async refundPayment(paymentId, amount) {
        try {
            const payment = await this.paymentRepository.findOne({
                where: { razorpayPaymentId: paymentId },
            });
            if (!payment || payment.status !== 'paid') {
                throw new rest_1.HttpErrors.BadRequest('Payment not found or not eligible for refund');
            }
            const refundAmount = amount || payment.amount;
            const refundAmountInSmallestUnit = payment.currency === 'INR' ? refundAmount * 100 : refundAmount * 100;
            const refund = await this.razorpay.payments.refund(paymentId, {
                amount: refundAmountInSmallestUnit,
            });
            if (refund.status === 'processed') {
                await this.paymentRepository.updateById(payment.id, {
                    status: 'refunded',
                    updatedAt: new Date(),
                    metadata: {
                        ...payment.metadata,
                        refundId: refund.id,
                        refundAmount,
                    },
                });
                return true;
            }
            return false;
        }
        catch (error) {
            console.error('Refund failed:', error);
            throw new rest_1.HttpErrors.BadRequest('Refund processing failed');
        }
    }
    async handleWebhook(payload, signature) {
        try {
            // Verify webhook signature
            const expectedSignature = crypto
                .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET || 'your_webhook_secret')
                .update(JSON.stringify(payload))
                .digest('hex');
            if (expectedSignature !== signature) {
                throw new rest_1.HttpErrors.Unauthorized('Invalid webhook signature');
            }
            const event = payload.event;
            const paymentEntity = payload.payload.payment.entity;
            switch (event) {
                case 'payment.captured':
                    await this.handlePaymentCaptured(paymentEntity);
                    break;
                case 'payment.failed':
                    await this.handlePaymentFailed(paymentEntity);
                    break;
                default:
                    console.log(`Unhandled webhook event: ${event}`);
            }
        }
        catch (error) {
            console.error('Webhook handling failed:', error);
            throw error;
        }
    }
    async handlePaymentCaptured(paymentEntity) {
        const payment = await this.paymentRepository.findOne({
            where: { razorpayOrderId: paymentEntity.order_id },
        });
        if (payment) {
            await this.paymentRepository.updateById(payment.id, {
                razorpayPaymentId: paymentEntity.id,
                status: 'paid',
                paidAt: new Date(),
                updatedAt: new Date(),
            });
        }
    }
    async handlePaymentFailed(paymentEntity) {
        const payment = await this.paymentRepository.findOne({
            where: { razorpayOrderId: paymentEntity.order_id },
        });
        if (payment) {
            await this.paymentRepository.updateById(payment.id, {
                razorpayPaymentId: paymentEntity.id,
                status: 'failed',
                updatedAt: new Date(),
            });
        }
    }
};
exports.PaymentService = PaymentService;
exports.PaymentService = PaymentService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.TRANSIENT }),
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.PaymentRepository)),
    tslib_1.__metadata("design:paramtypes", [repositories_1.PaymentRepository])
], PaymentService);
//# sourceMappingURL=payment.service.js.map