{"ast": null, "code": "import * as i1 from '@angular/common/http';\nimport { HttpClient } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { SecurityContext, DOCUMENT, Injectable, Optional, Inject, SkipSelf, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport * as i2 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { of, throwError, forkJoin } from 'rxjs';\nimport { tap, map, catchError, finalize, share } from 'rxjs/operators';\n\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\nlet policy;\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\nfunction getPolicy() {\n  if (policy === undefined) {\n    policy = null;\n    if (typeof window !== 'undefined') {\n      const ttWindow = window;\n      if (ttWindow.trustedTypes !== undefined) {\n        policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n          createHTML: s => s\n        });\n      }\n    }\n  }\n  return policy;\n}\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\nfunction trustedHTMLFromString(html) {\n  return getPolicy()?.createHTML(html) || html;\n}\n\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nfunction getMatIconNameNotFoundError(iconName) {\n  return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nfunction getMatIconNoHttpProviderError() {\n  return Error('Could not find HttpClient for use with Angular Material icons. ' + 'Please add provideHttpClient() to your providers.');\n}\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeUrlError(url) {\n  return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL ` + `via Angular's DomSanitizer. Attempted URL was \"${url}\".`);\n}\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeLiteralError(literal) {\n  return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by ` + `Angular's DomSanitizer. Attempted literal was \"${literal}\".`);\n}\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n  url;\n  svgText;\n  options;\n  svgElement;\n  constructor(url, svgText, options) {\n    this.url = url;\n    this.svgText = svgText;\n    this.options = options;\n  }\n}\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\nclass MatIconRegistry {\n  _httpClient;\n  _sanitizer;\n  _errorHandler;\n  _document;\n  /**\n   * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n   */\n  _svgIconConfigs = new Map();\n  /**\n   * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n   * Multiple icon sets can be registered under the same namespace.\n   */\n  _iconSetConfigs = new Map();\n  /** Cache for icons loaded by direct URLs. */\n  _cachedIconsByUrl = new Map();\n  /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n  _inProgressUrlFetches = new Map();\n  /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n  _fontCssClassesByAlias = new Map();\n  /** Registered icon resolver functions. */\n  _resolvers = [];\n  /**\n   * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n   * specified. The default 'material-icons' value assumes that the material icon font has been\n   * loaded as described at https://google.github.io/material-design-icons/#icon-font-for-the-web\n   */\n  _defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n  constructor(_httpClient, _sanitizer, document, _errorHandler) {\n    this._httpClient = _httpClient;\n    this._sanitizer = _sanitizer;\n    this._errorHandler = _errorHandler;\n    this._document = document;\n  }\n  /**\n   * Registers an icon by URL in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIcon(iconName, url, options) {\n    return this.addSvgIconInNamespace('', iconName, url, options);\n  }\n  /**\n   * Registers an icon using an HTML string in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteral(iconName, literal, options) {\n    return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n  }\n  /**\n   * Registers an icon by URL in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIconInNamespace(namespace, iconName, url, options) {\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n  }\n  /**\n   * Registers an icon resolver function with the registry. The function will be invoked with the\n   * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n   * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n   * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n   * will be invoked in the order in which they have been registered.\n   * @param resolver Resolver function to be registered.\n   */\n  addSvgIconResolver(resolver) {\n    this._resolvers.push(resolver);\n    return this;\n  }\n  /**\n   * Registers an icon using an HTML string in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteralInNamespace(namespace, iconName, literal, options) {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n    // TODO: add an ngDevMode check\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig('', trustedLiteral, options));\n  }\n  /**\n   * Registers an icon set by URL in the default namespace.\n   * @param url\n   */\n  addSvgIconSet(url, options) {\n    return this.addSvgIconSetInNamespace('', url, options);\n  }\n  /**\n   * Registers an icon set using an HTML string in the default namespace.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteral(literal, options) {\n    return this.addSvgIconSetLiteralInNamespace('', literal, options);\n  }\n  /**\n   * Registers an icon set by URL in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param url\n   */\n  addSvgIconSetInNamespace(namespace, url, options) {\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n  }\n  /**\n   * Registers an icon set using an HTML string in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteralInNamespace(namespace, literal, options) {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n  }\n  /**\n   * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n   * component with the alias as the fontSet input will cause the class name to be applied\n   * to the `<mat-icon>` element.\n   *\n   * If the registered font is a ligature font, then don't forget to also include the special\n   * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n   *\n   * ```ts\n   * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n   * ```\n   *\n   * And use like this:\n   *\n   * ```html\n   * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n   * ```\n   *\n   * @param alias Alias for the font.\n   * @param classNames Class names override to be used instead of the alias.\n   */\n  registerFontClassAlias(alias, classNames = alias) {\n    this._fontCssClassesByAlias.set(alias, classNames);\n    return this;\n  }\n  /**\n   * Returns the CSS class name associated with the alias by a previous call to\n   * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n   */\n  classNameForFontAlias(alias) {\n    return this._fontCssClassesByAlias.get(alias) || alias;\n  }\n  /**\n   * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  setDefaultFontSetClass(...classNames) {\n    this._defaultFontSetClass = classNames;\n    return this;\n  }\n  /**\n   * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  getDefaultFontSetClass() {\n    return this._defaultFontSetClass;\n  }\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n   * The response from the URL may be cached so this will not always cause an HTTP request, but\n   * the produced element will always be a new copy of the originally fetched icon. (That is,\n   * it will not contain any modifications made to elements previously returned).\n   *\n   * @param safeUrl URL from which to fetch the SVG icon.\n   */\n  getSvgIconFromUrl(safeUrl) {\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n    const cachedIcon = this._cachedIconsByUrl.get(url);\n    if (cachedIcon) {\n      return of(cloneSvg(cachedIcon));\n    }\n    return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(tap(svg => this._cachedIconsByUrl.set(url, svg)), map(svg => cloneSvg(svg)));\n  }\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n   * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n   * if not, the Observable will throw an error.\n   *\n   * @param name Name of the icon to be retrieved.\n   * @param namespace Namespace in which to look for the icon.\n   */\n  getNamedSvgIcon(name, namespace = '') {\n    const key = iconKey(namespace, name);\n    let config = this._svgIconConfigs.get(key);\n    // Return (copy of) cached icon if possible.\n    if (config) {\n      return this._getSvgFromConfig(config);\n    }\n    // Otherwise try to resolve the config from one of the resolver functions.\n    config = this._getIconConfigFromResolvers(namespace, name);\n    if (config) {\n      this._svgIconConfigs.set(key, config);\n      return this._getSvgFromConfig(config);\n    }\n    // See if we have any icon sets registered for the namespace.\n    const iconSetConfigs = this._iconSetConfigs.get(namespace);\n    if (iconSetConfigs) {\n      return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n    }\n    return throwError(getMatIconNameNotFoundError(key));\n  }\n  ngOnDestroy() {\n    this._resolvers = [];\n    this._svgIconConfigs.clear();\n    this._iconSetConfigs.clear();\n    this._cachedIconsByUrl.clear();\n  }\n  /**\n   * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n   */\n  _getSvgFromConfig(config) {\n    if (config.svgText) {\n      // We already have the SVG element for this icon, return a copy.\n      return of(cloneSvg(this._svgElementFromConfig(config)));\n    } else {\n      // Fetch the icon from the config's URL, cache it, and return a copy.\n      return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n    }\n  }\n  /**\n   * Attempts to find an icon with the specified name in any of the SVG icon sets.\n   * First searches the available cached icons for a nested element with a matching name, and\n   * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n   * that have not been cached, and searches again after all fetches are completed.\n   * The returned Observable produces the SVG element if possible, and throws\n   * an error if no icon with the specified name can be found.\n   */\n  _getSvgFromIconSetConfigs(name, iconSetConfigs) {\n    // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n    // requested name.\n    const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n    if (namedIcon) {\n      // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n      // time anyway, there's probably not much advantage compared to just always extracting\n      // it from the icon set.\n      return of(namedIcon);\n    }\n    // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n    // fetched, fetch them now and look for iconName in the results.\n    const iconSetFetchRequests = iconSetConfigs.filter(iconSetConfig => !iconSetConfig.svgText).map(iconSetConfig => {\n      return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(catchError(err => {\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n        // Swallow errors fetching individual URLs so the\n        // combined Observable won't necessarily fail.\n        const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n        this._errorHandler.handleError(new Error(errorMessage));\n        return of(null);\n      }));\n    });\n    // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n    // cached SVG element (unless the request failed), and we can check again for the icon.\n    return forkJoin(iconSetFetchRequests).pipe(map(() => {\n      const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n      // TODO: add an ngDevMode check\n      if (!foundIcon) {\n        throw getMatIconNameNotFoundError(name);\n      }\n      return foundIcon;\n    }));\n  }\n  /**\n   * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  _extractIconWithNameFromAnySet(iconName, iconSetConfigs) {\n    // Iterate backwards, so icon sets added later have precedence.\n    for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n      const config = iconSetConfigs[i];\n      // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n      // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n      // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n      // some of the parsing.\n      if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n        const svg = this._svgElementFromConfig(config);\n        const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n        if (foundIcon) {\n          return foundIcon;\n        }\n      }\n    }\n    return null;\n  }\n  /**\n   * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n   * from it.\n   */\n  _loadSvgIconFromConfig(config) {\n    return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText), map(() => this._svgElementFromConfig(config)));\n  }\n  /**\n   * Loads the content of the icon set URL specified in the\n   * SvgIconConfig and attaches it to the config.\n   */\n  _loadSvgIconSetFromConfig(config) {\n    if (config.svgText) {\n      return of(null);\n    }\n    return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText));\n  }\n  /**\n   * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  _extractSvgIconFromSet(iconSet, iconName, options) {\n    // Use the `id=\"iconName\"` syntax in order to escape special\n    // characters in the ID (versus using the #iconName syntax).\n    const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n    if (!iconSource) {\n      return null;\n    }\n    // Clone the element and remove the ID to prevent multiple elements from being added\n    // to the page with the same ID.\n    const iconElement = iconSource.cloneNode(true);\n    iconElement.removeAttribute('id');\n    // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n    // the content of a new <svg> node.\n    if (iconElement.nodeName.toLowerCase() === 'svg') {\n      return this._setSvgAttributes(iconElement, options);\n    }\n    // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n    // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n    // tag is problematic on Firefox, because it needs to include the current page path.\n    if (iconElement.nodeName.toLowerCase() === 'symbol') {\n      return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n    }\n    // createElement('SVG') doesn't work as expected; the DOM ends up with\n    // the correct nodes, but the SVG content doesn't render. Instead we\n    // have to create an empty SVG node using innerHTML and append its content.\n    // Elements created using DOMParser.parseFromString have the same problem.\n    // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    // Clone the node so we don't remove it from the parent icon set element.\n    svg.appendChild(iconElement);\n    return this._setSvgAttributes(svg, options);\n  }\n  /**\n   * Creates a DOM element from the given SVG string.\n   */\n  _svgElementFromString(str) {\n    const div = this._document.createElement('DIV');\n    div.innerHTML = str;\n    const svg = div.querySelector('svg');\n    // TODO: add an ngDevMode check\n    if (!svg) {\n      throw Error('<svg> tag not found');\n    }\n    return svg;\n  }\n  /**\n   * Converts an element into an SVG node by cloning all of its children.\n   */\n  _toSvgElement(element) {\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    const attributes = element.attributes;\n    // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n    for (let i = 0; i < attributes.length; i++) {\n      const {\n        name,\n        value\n      } = attributes[i];\n      if (name !== 'id') {\n        svg.setAttribute(name, value);\n      }\n    }\n    for (let i = 0; i < element.childNodes.length; i++) {\n      if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n        svg.appendChild(element.childNodes[i].cloneNode(true));\n      }\n    }\n    return svg;\n  }\n  /**\n   * Sets the default attributes for an SVG element to be used as an icon.\n   */\n  _setSvgAttributes(svg, options) {\n    svg.setAttribute('fit', '');\n    svg.setAttribute('height', '100%');\n    svg.setAttribute('width', '100%');\n    svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n    svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n    if (options && options.viewBox) {\n      svg.setAttribute('viewBox', options.viewBox);\n    }\n    return svg;\n  }\n  /**\n   * Returns an Observable which produces the string contents of the given icon. Results may be\n   * cached, so future calls with the same URL may not cause another HTTP request.\n   */\n  _fetchIcon(iconConfig) {\n    const {\n      url: safeUrl,\n      options\n    } = iconConfig;\n    const withCredentials = options?.withCredentials ?? false;\n    if (!this._httpClient) {\n      throw getMatIconNoHttpProviderError();\n    }\n    // TODO: add an ngDevMode check\n    if (safeUrl == null) {\n      throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n    }\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n    // TODO: add an ngDevMode check\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n    // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n    // already a request in progress for that URL. It's necessary to call share() on the\n    // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n    const inProgressFetch = this._inProgressUrlFetches.get(url);\n    if (inProgressFetch) {\n      return inProgressFetch;\n    }\n    const req = this._httpClient.get(url, {\n      responseType: 'text',\n      withCredentials\n    }).pipe(map(svg => {\n      // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n      // trusted HTML.\n      return trustedHTMLFromString(svg);\n    }), finalize(() => this._inProgressUrlFetches.delete(url)), share());\n    this._inProgressUrlFetches.set(url, req);\n    return req;\n  }\n  /**\n   * Registers an icon config by name in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param iconName Name under which to register the config.\n   * @param config Config to be registered.\n   */\n  _addSvgIconConfig(namespace, iconName, config) {\n    this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n    return this;\n  }\n  /**\n   * Registers an icon set config in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param config Config to be registered.\n   */\n  _addSvgIconSetConfig(namespace, config) {\n    const configNamespace = this._iconSetConfigs.get(namespace);\n    if (configNamespace) {\n      configNamespace.push(config);\n    } else {\n      this._iconSetConfigs.set(namespace, [config]);\n    }\n    return this;\n  }\n  /** Parses a config's text into an SVG element. */\n  _svgElementFromConfig(config) {\n    if (!config.svgElement) {\n      const svg = this._svgElementFromString(config.svgText);\n      this._setSvgAttributes(svg, config.options);\n      config.svgElement = svg;\n    }\n    return config.svgElement;\n  }\n  /** Tries to create an icon config through the registered resolver functions. */\n  _getIconConfigFromResolvers(namespace, name) {\n    for (let i = 0; i < this._resolvers.length; i++) {\n      const result = this._resolvers[i](name, namespace);\n      if (result) {\n        return isSafeUrlWithOptions(result) ? new SvgIconConfig(result.url, null, result.options) : new SvgIconConfig(result, null);\n      }\n    }\n    return undefined;\n  }\n  static ɵfac = function MatIconRegistry_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatIconRegistry)(i0.ɵɵinject(i1.HttpClient, 8), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(i0.ErrorHandler));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatIconRegistry,\n    factory: MatIconRegistry.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.HttpClient,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.DomSanitizer\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.ErrorHandler\n  }], null);\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction ICON_REGISTRY_PROVIDER_FACTORY(parentRegistry, httpClient, sanitizer, errorHandler, document) {\n  return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst ICON_REGISTRY_PROVIDER = {\n  // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n  provide: MatIconRegistry,\n  deps: [[new Optional(), new SkipSelf(), MatIconRegistry], [new Optional(), HttpClient], DomSanitizer, ErrorHandler, [new Optional(), DOCUMENT]],\n  useFactory: ICON_REGISTRY_PROVIDER_FACTORY\n};\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg) {\n  return svg.cloneNode(true);\n}\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace, name) {\n  return namespace + ':' + name;\n}\nfunction isSafeUrlWithOptions(value) {\n  return !!(value.url && value.options);\n}\nexport { ICON_REGISTRY_PROVIDER_FACTORY as I, MatIconRegistry as M, getMatIconNoHttpProviderError as a, getMatIconFailedToSanitizeUrlError as b, getMatIconFailedToSanitizeLiteralError as c, ICON_REGISTRY_PROVIDER as d, getMatIconNameNotFoundError as g };", "map": {"version": 3, "names": ["i1", "HttpClient", "i0", "SecurityContext", "DOCUMENT", "Injectable", "Optional", "Inject", "SkipSelf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i2", "Dom<PERSON><PERSON><PERSON>zer", "of", "throwError", "fork<PERSON><PERSON>n", "tap", "map", "catchError", "finalize", "share", "policy", "getPolicy", "undefined", "window", "ttWindow", "trustedTypes", "createPolicy", "createHTML", "s", "trustedHTMLFromString", "html", "getMatIconNameNotFoundError", "iconName", "Error", "getMatIconNoHttpProviderError", "getMatIconFailedToSanitizeUrlError", "url", "getMatIconFailedToSanitizeLiteralError", "literal", "SvgIconConfig", "svgText", "options", "svgElement", "constructor", "MatIconRegistry", "_httpClient", "_sanitizer", "_error<PERSON><PERSON><PERSON>", "_document", "_svgIconConfigs", "Map", "_iconSetConfigs", "_cachedIconsByUrl", "_inProgressUrlFetches", "_fontCssClassesByAlias", "_resolvers", "_defaultFontSetClass", "document", "addSvgIcon", "addSvgIconInNamespace", "addSvgIconLiteral", "addSvgIconLiteralInNamespace", "namespace", "_addSvgIconConfig", "addSvgIconResolver", "resolver", "push", "cleanLiteral", "sanitize", "HTML", "trustedLiteral", "addSvgIconSet", "addSvgIconSetInNamespace", "addSvgIconSetLiteral", "addSvgIconSetLiteralInNamespace", "_addSvgIconSetConfig", "registerFontClassAlias", "alias", "classNames", "set", "classNameForFontAlias", "get", "setDefaultFontSetClass", "getDefaultFontSetClass", "getSvgIconFromUrl", "safeUrl", "RESOURCE_URL", "cachedIcon", "cloneSvg", "_loadSvgIconFromConfig", "pipe", "svg", "getNamedSvgIcon", "name", "key", "<PERSON><PERSON><PERSON>", "config", "_getSvgFromConfig", "_getIconConfigFromResolvers", "iconSetConfigs", "_getSvgFromIconSetConfigs", "ngOnDestroy", "clear", "_svgElementFromConfig", "namedIcon", "_extractIconWithNameFromAnySet", "iconSetFetchRequests", "filter", "iconSetConfig", "_loadSvgIconSetFromConfig", "err", "errorMessage", "message", "handleError", "foundIcon", "i", "length", "toString", "indexOf", "_extractSvgIconFromSet", "_fetchIcon", "iconSet", "iconSource", "querySelector", "iconElement", "cloneNode", "removeAttribute", "nodeName", "toLowerCase", "_setSvgAttributes", "_toSvgElement", "_svgElementFromString", "append<PERSON><PERSON><PERSON>", "str", "div", "createElement", "innerHTML", "element", "attributes", "value", "setAttribute", "childNodes", "nodeType", "ELEMENT_NODE", "viewBox", "iconConfig", "withCredentials", "inProgressFetch", "req", "responseType", "delete", "configNamespace", "result", "isSafeUrlWithOptions", "ɵfac", "MatIconRegistry_Factory", "__ngFactoryType__", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "decorators", "ICON_REGISTRY_PROVIDER_FACTORY", "parentRegistry", "httpClient", "sanitizer", "<PERSON><PERSON><PERSON><PERSON>", "ICON_REGISTRY_PROVIDER", "provide", "deps", "useFactory", "I", "M", "a", "b", "c", "d", "g"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/material/fesm2022/icon-registry-CwOTJ7YM.mjs"], "sourcesContent": ["import * as i1 from '@angular/common/http';\nimport { HttpClient } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { SecurityContext, DOCUMENT, Injectable, Optional, Inject, SkipSelf, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport * as i2 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { of, throwError, forkJoin } from 'rxjs';\nimport { tap, map, catchError, finalize, share } from 'rxjs/operators';\n\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\nlet policy;\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\nfunction getPolicy() {\n    if (policy === undefined) {\n        policy = null;\n        if (typeof window !== 'undefined') {\n            const ttWindow = window;\n            if (ttWindow.trustedTypes !== undefined) {\n                policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n                    createHTML: (s) => s,\n                });\n            }\n        }\n    }\n    return policy;\n}\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\nfunction trustedHTMLFromString(html) {\n    return getPolicy()?.createHTML(html) || html;\n}\n\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nfunction getMatIconNameNotFoundError(iconName) {\n    return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nfunction getMatIconNoHttpProviderError() {\n    return Error('Could not find HttpClient for use with Angular Material icons. ' +\n        'Please add provideHttpClient() to your providers.');\n}\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeUrlError(url) {\n    return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL ` +\n        `via Angular's DomSanitizer. Attempted URL was \"${url}\".`);\n}\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeLiteralError(literal) {\n    return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by ` +\n        `Angular's DomSanitizer. Attempted literal was \"${literal}\".`);\n}\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n    url;\n    svgText;\n    options;\n    svgElement;\n    constructor(url, svgText, options) {\n        this.url = url;\n        this.svgText = svgText;\n        this.options = options;\n    }\n}\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\nclass MatIconRegistry {\n    _httpClient;\n    _sanitizer;\n    _errorHandler;\n    _document;\n    /**\n     * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n     */\n    _svgIconConfigs = new Map();\n    /**\n     * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n     * Multiple icon sets can be registered under the same namespace.\n     */\n    _iconSetConfigs = new Map();\n    /** Cache for icons loaded by direct URLs. */\n    _cachedIconsByUrl = new Map();\n    /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n    _inProgressUrlFetches = new Map();\n    /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n    _fontCssClassesByAlias = new Map();\n    /** Registered icon resolver functions. */\n    _resolvers = [];\n    /**\n     * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n     * specified. The default 'material-icons' value assumes that the material icon font has been\n     * loaded as described at https://google.github.io/material-design-icons/#icon-font-for-the-web\n     */\n    _defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n    constructor(_httpClient, _sanitizer, document, _errorHandler) {\n        this._httpClient = _httpClient;\n        this._sanitizer = _sanitizer;\n        this._errorHandler = _errorHandler;\n        this._document = document;\n    }\n    /**\n     * Registers an icon by URL in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIcon(iconName, url, options) {\n        return this.addSvgIconInNamespace('', iconName, url, options);\n    }\n    /**\n     * Registers an icon using an HTML string in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteral(iconName, literal, options) {\n        return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n    }\n    /**\n     * Registers an icon by URL in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIconInNamespace(namespace, iconName, url, options) {\n        return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon resolver function with the registry. The function will be invoked with the\n     * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n     * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n     * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n     * will be invoked in the order in which they have been registered.\n     * @param resolver Resolver function to be registered.\n     */\n    addSvgIconResolver(resolver) {\n        this._resolvers.push(resolver);\n        return this;\n    }\n    /**\n     * Registers an icon using an HTML string in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteralInNamespace(namespace, iconName, literal, options) {\n        const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n        // TODO: add an ngDevMode check\n        if (!cleanLiteral) {\n            throw getMatIconFailedToSanitizeLiteralError(literal);\n        }\n        // Security: The literal is passed in as SafeHtml, and is thus trusted.\n        const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n        return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig('', trustedLiteral, options));\n    }\n    /**\n     * Registers an icon set by URL in the default namespace.\n     * @param url\n     */\n    addSvgIconSet(url, options) {\n        return this.addSvgIconSetInNamespace('', url, options);\n    }\n    /**\n     * Registers an icon set using an HTML string in the default namespace.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteral(literal, options) {\n        return this.addSvgIconSetLiteralInNamespace('', literal, options);\n    }\n    /**\n     * Registers an icon set by URL in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param url\n     */\n    addSvgIconSetInNamespace(namespace, url, options) {\n        return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon set using an HTML string in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteralInNamespace(namespace, literal, options) {\n        const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n        if (!cleanLiteral) {\n            throw getMatIconFailedToSanitizeLiteralError(literal);\n        }\n        // Security: The literal is passed in as SafeHtml, and is thus trusted.\n        const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n        return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n    }\n    /**\n     * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n     * component with the alias as the fontSet input will cause the class name to be applied\n     * to the `<mat-icon>` element.\n     *\n     * If the registered font is a ligature font, then don't forget to also include the special\n     * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n     *\n     * ```ts\n     * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n     * ```\n     *\n     * And use like this:\n     *\n     * ```html\n     * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n     * ```\n     *\n     * @param alias Alias for the font.\n     * @param classNames Class names override to be used instead of the alias.\n     */\n    registerFontClassAlias(alias, classNames = alias) {\n        this._fontCssClassesByAlias.set(alias, classNames);\n        return this;\n    }\n    /**\n     * Returns the CSS class name associated with the alias by a previous call to\n     * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n     */\n    classNameForFontAlias(alias) {\n        return this._fontCssClassesByAlias.get(alias) || alias;\n    }\n    /**\n     * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     */\n    setDefaultFontSetClass(...classNames) {\n        this._defaultFontSetClass = classNames;\n        return this;\n    }\n    /**\n     * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     */\n    getDefaultFontSetClass() {\n        return this._defaultFontSetClass;\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n     * The response from the URL may be cached so this will not always cause an HTTP request, but\n     * the produced element will always be a new copy of the originally fetched icon. (That is,\n     * it will not contain any modifications made to elements previously returned).\n     *\n     * @param safeUrl URL from which to fetch the SVG icon.\n     */\n    getSvgIconFromUrl(safeUrl) {\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n        if (!url) {\n            throw getMatIconFailedToSanitizeUrlError(safeUrl);\n        }\n        const cachedIcon = this._cachedIconsByUrl.get(url);\n        if (cachedIcon) {\n            return of(cloneSvg(cachedIcon));\n        }\n        return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(tap(svg => this._cachedIconsByUrl.set(url, svg)), map(svg => cloneSvg(svg)));\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n     * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n     * if not, the Observable will throw an error.\n     *\n     * @param name Name of the icon to be retrieved.\n     * @param namespace Namespace in which to look for the icon.\n     */\n    getNamedSvgIcon(name, namespace = '') {\n        const key = iconKey(namespace, name);\n        let config = this._svgIconConfigs.get(key);\n        // Return (copy of) cached icon if possible.\n        if (config) {\n            return this._getSvgFromConfig(config);\n        }\n        // Otherwise try to resolve the config from one of the resolver functions.\n        config = this._getIconConfigFromResolvers(namespace, name);\n        if (config) {\n            this._svgIconConfigs.set(key, config);\n            return this._getSvgFromConfig(config);\n        }\n        // See if we have any icon sets registered for the namespace.\n        const iconSetConfigs = this._iconSetConfigs.get(namespace);\n        if (iconSetConfigs) {\n            return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n        }\n        return throwError(getMatIconNameNotFoundError(key));\n    }\n    ngOnDestroy() {\n        this._resolvers = [];\n        this._svgIconConfigs.clear();\n        this._iconSetConfigs.clear();\n        this._cachedIconsByUrl.clear();\n    }\n    /**\n     * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n     */\n    _getSvgFromConfig(config) {\n        if (config.svgText) {\n            // We already have the SVG element for this icon, return a copy.\n            return of(cloneSvg(this._svgElementFromConfig(config)));\n        }\n        else {\n            // Fetch the icon from the config's URL, cache it, and return a copy.\n            return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n        }\n    }\n    /**\n     * Attempts to find an icon with the specified name in any of the SVG icon sets.\n     * First searches the available cached icons for a nested element with a matching name, and\n     * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n     * that have not been cached, and searches again after all fetches are completed.\n     * The returned Observable produces the SVG element if possible, and throws\n     * an error if no icon with the specified name can be found.\n     */\n    _getSvgFromIconSetConfigs(name, iconSetConfigs) {\n        // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n        // requested name.\n        const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n        if (namedIcon) {\n            // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n            // time anyway, there's probably not much advantage compared to just always extracting\n            // it from the icon set.\n            return of(namedIcon);\n        }\n        // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n        // fetched, fetch them now and look for iconName in the results.\n        const iconSetFetchRequests = iconSetConfigs\n            .filter(iconSetConfig => !iconSetConfig.svgText)\n            .map(iconSetConfig => {\n            return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(catchError((err) => {\n                const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n                // Swallow errors fetching individual URLs so the\n                // combined Observable won't necessarily fail.\n                const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n                this._errorHandler.handleError(new Error(errorMessage));\n                return of(null);\n            }));\n        });\n        // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n        // cached SVG element (unless the request failed), and we can check again for the icon.\n        return forkJoin(iconSetFetchRequests).pipe(map(() => {\n            const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n            // TODO: add an ngDevMode check\n            if (!foundIcon) {\n                throw getMatIconNameNotFoundError(name);\n            }\n            return foundIcon;\n        }));\n    }\n    /**\n     * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractIconWithNameFromAnySet(iconName, iconSetConfigs) {\n        // Iterate backwards, so icon sets added later have precedence.\n        for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n            const config = iconSetConfigs[i];\n            // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n            // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n            // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n            // some of the parsing.\n            if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n                const svg = this._svgElementFromConfig(config);\n                const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n                if (foundIcon) {\n                    return foundIcon;\n                }\n            }\n        }\n        return null;\n    }\n    /**\n     * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n     * from it.\n     */\n    _loadSvgIconFromConfig(config) {\n        return this._fetchIcon(config).pipe(tap(svgText => (config.svgText = svgText)), map(() => this._svgElementFromConfig(config)));\n    }\n    /**\n     * Loads the content of the icon set URL specified in the\n     * SvgIconConfig and attaches it to the config.\n     */\n    _loadSvgIconSetFromConfig(config) {\n        if (config.svgText) {\n            return of(null);\n        }\n        return this._fetchIcon(config).pipe(tap(svgText => (config.svgText = svgText)));\n    }\n    /**\n     * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractSvgIconFromSet(iconSet, iconName, options) {\n        // Use the `id=\"iconName\"` syntax in order to escape special\n        // characters in the ID (versus using the #iconName syntax).\n        const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n        if (!iconSource) {\n            return null;\n        }\n        // Clone the element and remove the ID to prevent multiple elements from being added\n        // to the page with the same ID.\n        const iconElement = iconSource.cloneNode(true);\n        iconElement.removeAttribute('id');\n        // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n        // the content of a new <svg> node.\n        if (iconElement.nodeName.toLowerCase() === 'svg') {\n            return this._setSvgAttributes(iconElement, options);\n        }\n        // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n        // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n        // tag is problematic on Firefox, because it needs to include the current page path.\n        if (iconElement.nodeName.toLowerCase() === 'symbol') {\n            return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n        }\n        // createElement('SVG') doesn't work as expected; the DOM ends up with\n        // the correct nodes, but the SVG content doesn't render. Instead we\n        // have to create an empty SVG node using innerHTML and append its content.\n        // Elements created using DOMParser.parseFromString have the same problem.\n        // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n        const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n        // Clone the node so we don't remove it from the parent icon set element.\n        svg.appendChild(iconElement);\n        return this._setSvgAttributes(svg, options);\n    }\n    /**\n     * Creates a DOM element from the given SVG string.\n     */\n    _svgElementFromString(str) {\n        const div = this._document.createElement('DIV');\n        div.innerHTML = str;\n        const svg = div.querySelector('svg');\n        // TODO: add an ngDevMode check\n        if (!svg) {\n            throw Error('<svg> tag not found');\n        }\n        return svg;\n    }\n    /**\n     * Converts an element into an SVG node by cloning all of its children.\n     */\n    _toSvgElement(element) {\n        const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n        const attributes = element.attributes;\n        // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n        for (let i = 0; i < attributes.length; i++) {\n            const { name, value } = attributes[i];\n            if (name !== 'id') {\n                svg.setAttribute(name, value);\n            }\n        }\n        for (let i = 0; i < element.childNodes.length; i++) {\n            if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n                svg.appendChild(element.childNodes[i].cloneNode(true));\n            }\n        }\n        return svg;\n    }\n    /**\n     * Sets the default attributes for an SVG element to be used as an icon.\n     */\n    _setSvgAttributes(svg, options) {\n        svg.setAttribute('fit', '');\n        svg.setAttribute('height', '100%');\n        svg.setAttribute('width', '100%');\n        svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n        svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n        if (options && options.viewBox) {\n            svg.setAttribute('viewBox', options.viewBox);\n        }\n        return svg;\n    }\n    /**\n     * Returns an Observable which produces the string contents of the given icon. Results may be\n     * cached, so future calls with the same URL may not cause another HTTP request.\n     */\n    _fetchIcon(iconConfig) {\n        const { url: safeUrl, options } = iconConfig;\n        const withCredentials = options?.withCredentials ?? false;\n        if (!this._httpClient) {\n            throw getMatIconNoHttpProviderError();\n        }\n        // TODO: add an ngDevMode check\n        if (safeUrl == null) {\n            throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n        }\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n        // TODO: add an ngDevMode check\n        if (!url) {\n            throw getMatIconFailedToSanitizeUrlError(safeUrl);\n        }\n        // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n        // already a request in progress for that URL. It's necessary to call share() on the\n        // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n        const inProgressFetch = this._inProgressUrlFetches.get(url);\n        if (inProgressFetch) {\n            return inProgressFetch;\n        }\n        const req = this._httpClient.get(url, { responseType: 'text', withCredentials }).pipe(map(svg => {\n            // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n            // trusted HTML.\n            return trustedHTMLFromString(svg);\n        }), finalize(() => this._inProgressUrlFetches.delete(url)), share());\n        this._inProgressUrlFetches.set(url, req);\n        return req;\n    }\n    /**\n     * Registers an icon config by name in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param iconName Name under which to register the config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconConfig(namespace, iconName, config) {\n        this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n        return this;\n    }\n    /**\n     * Registers an icon set config in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconSetConfig(namespace, config) {\n        const configNamespace = this._iconSetConfigs.get(namespace);\n        if (configNamespace) {\n            configNamespace.push(config);\n        }\n        else {\n            this._iconSetConfigs.set(namespace, [config]);\n        }\n        return this;\n    }\n    /** Parses a config's text into an SVG element. */\n    _svgElementFromConfig(config) {\n        if (!config.svgElement) {\n            const svg = this._svgElementFromString(config.svgText);\n            this._setSvgAttributes(svg, config.options);\n            config.svgElement = svg;\n        }\n        return config.svgElement;\n    }\n    /** Tries to create an icon config through the registered resolver functions. */\n    _getIconConfigFromResolvers(namespace, name) {\n        for (let i = 0; i < this._resolvers.length; i++) {\n            const result = this._resolvers[i](name, namespace);\n            if (result) {\n                return isSafeUrlWithOptions(result)\n                    ? new SvgIconConfig(result.url, null, result.options)\n                    : new SvgIconConfig(result, null);\n            }\n        }\n        return undefined;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatIconRegistry, deps: [{ token: i1.HttpClient, optional: true }, { token: i2.DomSanitizer }, { token: DOCUMENT, optional: true }, { token: i0.ErrorHandler }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatIconRegistry, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatIconRegistry, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.HttpClient, decorators: [{\n                    type: Optional\n                }] }, { type: i2.DomSanitizer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ErrorHandler }] });\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction ICON_REGISTRY_PROVIDER_FACTORY(parentRegistry, httpClient, sanitizer, errorHandler, document) {\n    return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst ICON_REGISTRY_PROVIDER = {\n    // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n    provide: MatIconRegistry,\n    deps: [\n        [new Optional(), new SkipSelf(), MatIconRegistry],\n        [new Optional(), HttpClient],\n        DomSanitizer,\n        ErrorHandler,\n        [new Optional(), DOCUMENT],\n    ],\n    useFactory: ICON_REGISTRY_PROVIDER_FACTORY,\n};\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg) {\n    return svg.cloneNode(true);\n}\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace, name) {\n    return namespace + ':' + name;\n}\nfunction isSafeUrlWithOptions(value) {\n    return !!(value.url && value.options);\n}\n\nexport { ICON_REGISTRY_PROVIDER_FACTORY as I, MatIconRegistry as M, getMatIconNoHttpProviderError as a, getMatIconFailedToSanitizeUrlError as b, getMatIconFailedToSanitizeLiteralError as c, ICON_REGISTRY_PROVIDER as d, getMatIconNameNotFoundError as g };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,sBAAsB;AAC1C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,eAAe,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,eAAe;AAC/G,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,EAAE,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,MAAM;AAC/C,SAASC,GAAG,EAAEC,GAAG,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,gBAAgB;;AAEtE;AACA;AACA;AACA;AACA,IAAIC,MAAM;AACV;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAA,EAAG;EACjB,IAAID,MAAM,KAAKE,SAAS,EAAE;IACtBF,MAAM,GAAG,IAAI;IACb,IAAI,OAAOG,MAAM,KAAK,WAAW,EAAE;MAC/B,MAAMC,QAAQ,GAAGD,MAAM;MACvB,IAAIC,QAAQ,CAACC,YAAY,KAAKH,SAAS,EAAE;QACrCF,MAAM,GAAGI,QAAQ,CAACC,YAAY,CAACC,YAAY,CAAC,oBAAoB,EAAE;UAC9DC,UAAU,EAAGC,CAAC,IAAKA;QACvB,CAAC,CAAC;MACN;IACJ;EACJ;EACA,OAAOR,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,qBAAqBA,CAACC,IAAI,EAAE;EACjC,OAAOT,SAAS,CAAC,CAAC,EAAEM,UAAU,CAACG,IAAI,CAAC,IAAIA,IAAI;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,2BAA2BA,CAACC,QAAQ,EAAE;EAC3C,OAAOC,KAAK,CAAC,sCAAsCD,QAAQ,GAAG,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,6BAA6BA,CAAA,EAAG;EACrC,OAAOD,KAAK,CAAC,iEAAiE,GAC1E,mDAAmD,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,kCAAkCA,CAACC,GAAG,EAAE;EAC7C,OAAOH,KAAK,CAAC,wEAAwE,GACjF,kDAAkDG,GAAG,IAAI,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sCAAsCA,CAACC,OAAO,EAAE;EACrD,OAAOL,KAAK,CAAC,0EAA0E,GACnF,kDAAkDK,OAAO,IAAI,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBH,GAAG;EACHI,OAAO;EACPC,OAAO;EACPC,UAAU;EACVC,WAAWA,CAACP,GAAG,EAAEI,OAAO,EAAEC,OAAO,EAAE;IAC/B,IAAI,CAACL,GAAG,GAAGA,GAAG;IACd,IAAI,CAACI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,eAAe,CAAC;EAClBC,WAAW;EACXC,UAAU;EACVC,aAAa;EACbC,SAAS;EACT;AACJ;AACA;EACIC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;EACIC,eAAe,GAAG,IAAID,GAAG,CAAC,CAAC;EAC3B;EACAE,iBAAiB,GAAG,IAAIF,GAAG,CAAC,CAAC;EAC7B;EACAG,qBAAqB,GAAG,IAAIH,GAAG,CAAC,CAAC;EACjC;EACAI,sBAAsB,GAAG,IAAIJ,GAAG,CAAC,CAAC;EAClC;EACAK,UAAU,GAAG,EAAE;EACf;AACJ;AACA;AACA;AACA;EACIC,oBAAoB,GAAG,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;EAC9Db,WAAWA,CAACE,WAAW,EAAEC,UAAU,EAAEW,QAAQ,EAAEV,aAAa,EAAE;IAC1D,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAGS,QAAQ;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAUA,CAAC1B,QAAQ,EAAEI,GAAG,EAAEK,OAAO,EAAE;IAC/B,OAAO,IAAI,CAACkB,qBAAqB,CAAC,EAAE,EAAE3B,QAAQ,EAAEI,GAAG,EAAEK,OAAO,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACImB,iBAAiBA,CAAC5B,QAAQ,EAAEM,OAAO,EAAEG,OAAO,EAAE;IAC1C,OAAO,IAAI,CAACoB,4BAA4B,CAAC,EAAE,EAAE7B,QAAQ,EAAEM,OAAO,EAAEG,OAAO,CAAC;EAC5E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkB,qBAAqBA,CAACG,SAAS,EAAE9B,QAAQ,EAAEI,GAAG,EAAEK,OAAO,EAAE;IACrD,OAAO,IAAI,CAACsB,iBAAiB,CAACD,SAAS,EAAE9B,QAAQ,EAAE,IAAIO,aAAa,CAACH,GAAG,EAAE,IAAI,EAAEK,OAAO,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIuB,kBAAkBA,CAACC,QAAQ,EAAE;IACzB,IAAI,CAACV,UAAU,CAACW,IAAI,CAACD,QAAQ,CAAC;IAC9B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIJ,4BAA4BA,CAACC,SAAS,EAAE9B,QAAQ,EAAEM,OAAO,EAAEG,OAAO,EAAE;IAChE,MAAM0B,YAAY,GAAG,IAAI,CAACrB,UAAU,CAACsB,QAAQ,CAACjE,eAAe,CAACkE,IAAI,EAAE/B,OAAO,CAAC;IAC5E;IACA,IAAI,CAAC6B,YAAY,EAAE;MACf,MAAM9B,sCAAsC,CAACC,OAAO,CAAC;IACzD;IACA;IACA,MAAMgC,cAAc,GAAGzC,qBAAqB,CAACsC,YAAY,CAAC;IAC1D,OAAO,IAAI,CAACJ,iBAAiB,CAACD,SAAS,EAAE9B,QAAQ,EAAE,IAAIO,aAAa,CAAC,EAAE,EAAE+B,cAAc,EAAE7B,OAAO,CAAC,CAAC;EACtG;EACA;AACJ;AACA;AACA;EACI8B,aAAaA,CAACnC,GAAG,EAAEK,OAAO,EAAE;IACxB,OAAO,IAAI,CAAC+B,wBAAwB,CAAC,EAAE,EAAEpC,GAAG,EAAEK,OAAO,CAAC;EAC1D;EACA;AACJ;AACA;AACA;EACIgC,oBAAoBA,CAACnC,OAAO,EAAEG,OAAO,EAAE;IACnC,OAAO,IAAI,CAACiC,+BAA+B,CAAC,EAAE,EAAEpC,OAAO,EAAEG,OAAO,CAAC;EACrE;EACA;AACJ;AACA;AACA;AACA;EACI+B,wBAAwBA,CAACV,SAAS,EAAE1B,GAAG,EAAEK,OAAO,EAAE;IAC9C,OAAO,IAAI,CAACkC,oBAAoB,CAACb,SAAS,EAAE,IAAIvB,aAAa,CAACH,GAAG,EAAE,IAAI,EAAEK,OAAO,CAAC,CAAC;EACtF;EACA;AACJ;AACA;AACA;AACA;EACIiC,+BAA+BA,CAACZ,SAAS,EAAExB,OAAO,EAAEG,OAAO,EAAE;IACzD,MAAM0B,YAAY,GAAG,IAAI,CAACrB,UAAU,CAACsB,QAAQ,CAACjE,eAAe,CAACkE,IAAI,EAAE/B,OAAO,CAAC;IAC5E,IAAI,CAAC6B,YAAY,EAAE;MACf,MAAM9B,sCAAsC,CAACC,OAAO,CAAC;IACzD;IACA;IACA,MAAMgC,cAAc,GAAGzC,qBAAqB,CAACsC,YAAY,CAAC;IAC1D,OAAO,IAAI,CAACQ,oBAAoB,CAACb,SAAS,EAAE,IAAIvB,aAAa,CAAC,EAAE,EAAE+B,cAAc,EAAE7B,OAAO,CAAC,CAAC;EAC/F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImC,sBAAsBA,CAACC,KAAK,EAAEC,UAAU,GAAGD,KAAK,EAAE;IAC9C,IAAI,CAACvB,sBAAsB,CAACyB,GAAG,CAACF,KAAK,EAAEC,UAAU,CAAC;IAClD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,qBAAqBA,CAACH,KAAK,EAAE;IACzB,OAAO,IAAI,CAACvB,sBAAsB,CAAC2B,GAAG,CAACJ,KAAK,CAAC,IAAIA,KAAK;EAC1D;EACA;AACJ;AACA;AACA;EACIK,sBAAsBA,CAAC,GAAGJ,UAAU,EAAE;IAClC,IAAI,CAACtB,oBAAoB,GAAGsB,UAAU;IACtC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIK,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC3B,oBAAoB;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI4B,iBAAiBA,CAACC,OAAO,EAAE;IACvB,MAAMjD,GAAG,GAAG,IAAI,CAACU,UAAU,CAACsB,QAAQ,CAACjE,eAAe,CAACmF,YAAY,EAAED,OAAO,CAAC;IAC3E,IAAI,CAACjD,GAAG,EAAE;MACN,MAAMD,kCAAkC,CAACkD,OAAO,CAAC;IACrD;IACA,MAAME,UAAU,GAAG,IAAI,CAACnC,iBAAiB,CAAC6B,GAAG,CAAC7C,GAAG,CAAC;IAClD,IAAImD,UAAU,EAAE;MACZ,OAAO3E,EAAE,CAAC4E,QAAQ,CAACD,UAAU,CAAC,CAAC;IACnC;IACA,OAAO,IAAI,CAACE,sBAAsB,CAAC,IAAIlD,aAAa,CAAC8C,OAAO,EAAE,IAAI,CAAC,CAAC,CAACK,IAAI,CAAC3E,GAAG,CAAC4E,GAAG,IAAI,IAAI,CAACvC,iBAAiB,CAAC2B,GAAG,CAAC3C,GAAG,EAAEuD,GAAG,CAAC,CAAC,EAAE3E,GAAG,CAAC2E,GAAG,IAAIH,QAAQ,CAACG,GAAG,CAAC,CAAC,CAAC;EAC1J;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACC,IAAI,EAAE/B,SAAS,GAAG,EAAE,EAAE;IAClC,MAAMgC,GAAG,GAAGC,OAAO,CAACjC,SAAS,EAAE+B,IAAI,CAAC;IACpC,IAAIG,MAAM,GAAG,IAAI,CAAC/C,eAAe,CAACgC,GAAG,CAACa,GAAG,CAAC;IAC1C;IACA,IAAIE,MAAM,EAAE;MACR,OAAO,IAAI,CAACC,iBAAiB,CAACD,MAAM,CAAC;IACzC;IACA;IACAA,MAAM,GAAG,IAAI,CAACE,2BAA2B,CAACpC,SAAS,EAAE+B,IAAI,CAAC;IAC1D,IAAIG,MAAM,EAAE;MACR,IAAI,CAAC/C,eAAe,CAAC8B,GAAG,CAACe,GAAG,EAAEE,MAAM,CAAC;MACrC,OAAO,IAAI,CAACC,iBAAiB,CAACD,MAAM,CAAC;IACzC;IACA;IACA,MAAMG,cAAc,GAAG,IAAI,CAAChD,eAAe,CAAC8B,GAAG,CAACnB,SAAS,CAAC;IAC1D,IAAIqC,cAAc,EAAE;MAChB,OAAO,IAAI,CAACC,yBAAyB,CAACP,IAAI,EAAEM,cAAc,CAAC;IAC/D;IACA,OAAOtF,UAAU,CAACkB,2BAA2B,CAAC+D,GAAG,CAAC,CAAC;EACvD;EACAO,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9C,UAAU,GAAG,EAAE;IACpB,IAAI,CAACN,eAAe,CAACqD,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACnD,eAAe,CAACmD,KAAK,CAAC,CAAC;IAC5B,IAAI,CAAClD,iBAAiB,CAACkD,KAAK,CAAC,CAAC;EAClC;EACA;AACJ;AACA;EACIL,iBAAiBA,CAACD,MAAM,EAAE;IACtB,IAAIA,MAAM,CAACxD,OAAO,EAAE;MAChB;MACA,OAAO5B,EAAE,CAAC4E,QAAQ,CAAC,IAAI,CAACe,qBAAqB,CAACP,MAAM,CAAC,CAAC,CAAC;IAC3D,CAAC,MACI;MACD;MACA,OAAO,IAAI,CAACP,sBAAsB,CAACO,MAAM,CAAC,CAACN,IAAI,CAAC1E,GAAG,CAAC2E,GAAG,IAAIH,QAAQ,CAACG,GAAG,CAAC,CAAC,CAAC;IAC9E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIS,yBAAyBA,CAACP,IAAI,EAAEM,cAAc,EAAE;IAC5C;IACA;IACA,MAAMK,SAAS,GAAG,IAAI,CAACC,8BAA8B,CAACZ,IAAI,EAAEM,cAAc,CAAC;IAC3E,IAAIK,SAAS,EAAE;MACX;MACA;MACA;MACA,OAAO5F,EAAE,CAAC4F,SAAS,CAAC;IACxB;IACA;IACA;IACA,MAAME,oBAAoB,GAAGP,cAAc,CACtCQ,MAAM,CAACC,aAAa,IAAI,CAACA,aAAa,CAACpE,OAAO,CAAC,CAC/CxB,GAAG,CAAC4F,aAAa,IAAI;MACtB,OAAO,IAAI,CAACC,yBAAyB,CAACD,aAAa,CAAC,CAAClB,IAAI,CAACzE,UAAU,CAAE6F,GAAG,IAAK;QAC1E,MAAM1E,GAAG,GAAG,IAAI,CAACU,UAAU,CAACsB,QAAQ,CAACjE,eAAe,CAACmF,YAAY,EAAEsB,aAAa,CAACxE,GAAG,CAAC;QACrF;QACA;QACA,MAAM2E,YAAY,GAAG,yBAAyB3E,GAAG,YAAY0E,GAAG,CAACE,OAAO,EAAE;QAC1E,IAAI,CAACjE,aAAa,CAACkE,WAAW,CAAC,IAAIhF,KAAK,CAAC8E,YAAY,CAAC,CAAC;QACvD,OAAOnG,EAAE,CAAC,IAAI,CAAC;MACnB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IACF;IACA;IACA,OAAOE,QAAQ,CAAC4F,oBAAoB,CAAC,CAAChB,IAAI,CAAC1E,GAAG,CAAC,MAAM;MACjD,MAAMkG,SAAS,GAAG,IAAI,CAACT,8BAA8B,CAACZ,IAAI,EAAEM,cAAc,CAAC;MAC3E;MACA,IAAI,CAACe,SAAS,EAAE;QACZ,MAAMnF,2BAA2B,CAAC8D,IAAI,CAAC;MAC3C;MACA,OAAOqB,SAAS;IACpB,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;AACA;EACIT,8BAA8BA,CAACzE,QAAQ,EAAEmE,cAAc,EAAE;IACrD;IACA,KAAK,IAAIgB,CAAC,GAAGhB,cAAc,CAACiB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACjD,MAAMnB,MAAM,GAAGG,cAAc,CAACgB,CAAC,CAAC;MAChC;MACA;MACA;MACA;MACA,IAAInB,MAAM,CAACxD,OAAO,IAAIwD,MAAM,CAACxD,OAAO,CAAC6E,QAAQ,CAAC,CAAC,CAACC,OAAO,CAACtF,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;QACpE,MAAM2D,GAAG,GAAG,IAAI,CAACY,qBAAqB,CAACP,MAAM,CAAC;QAC9C,MAAMkB,SAAS,GAAG,IAAI,CAACK,sBAAsB,CAAC5B,GAAG,EAAE3D,QAAQ,EAAEgE,MAAM,CAACvD,OAAO,CAAC;QAC5E,IAAIyE,SAAS,EAAE;UACX,OAAOA,SAAS;QACpB;MACJ;IACJ;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIzB,sBAAsBA,CAACO,MAAM,EAAE;IAC3B,OAAO,IAAI,CAACwB,UAAU,CAACxB,MAAM,CAAC,CAACN,IAAI,CAAC3E,GAAG,CAACyB,OAAO,IAAKwD,MAAM,CAACxD,OAAO,GAAGA,OAAQ,CAAC,EAAExB,GAAG,CAAC,MAAM,IAAI,CAACuF,qBAAqB,CAACP,MAAM,CAAC,CAAC,CAAC;EAClI;EACA;AACJ;AACA;AACA;EACIa,yBAAyBA,CAACb,MAAM,EAAE;IAC9B,IAAIA,MAAM,CAACxD,OAAO,EAAE;MAChB,OAAO5B,EAAE,CAAC,IAAI,CAAC;IACnB;IACA,OAAO,IAAI,CAAC4G,UAAU,CAACxB,MAAM,CAAC,CAACN,IAAI,CAAC3E,GAAG,CAACyB,OAAO,IAAKwD,MAAM,CAACxD,OAAO,GAAGA,OAAQ,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACI+E,sBAAsBA,CAACE,OAAO,EAAEzF,QAAQ,EAAES,OAAO,EAAE;IAC/C;IACA;IACA,MAAMiF,UAAU,GAAGD,OAAO,CAACE,aAAa,CAAC,QAAQ3F,QAAQ,IAAI,CAAC;IAC9D,IAAI,CAAC0F,UAAU,EAAE;MACb,OAAO,IAAI;IACf;IACA;IACA;IACA,MAAME,WAAW,GAAGF,UAAU,CAACG,SAAS,CAAC,IAAI,CAAC;IAC9CD,WAAW,CAACE,eAAe,CAAC,IAAI,CAAC;IACjC;IACA;IACA,IAAIF,WAAW,CAACG,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;MAC9C,OAAO,IAAI,CAACC,iBAAiB,CAACL,WAAW,EAAEnF,OAAO,CAAC;IACvD;IACA;IACA;IACA;IACA,IAAImF,WAAW,CAACG,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;MACjD,OAAO,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACC,aAAa,CAACN,WAAW,CAAC,EAAEnF,OAAO,CAAC;IAC3E;IACA;IACA;IACA;IACA;IACA;IACA,MAAMkD,GAAG,GAAG,IAAI,CAACwC,qBAAqB,CAACtG,qBAAqB,CAAC,aAAa,CAAC,CAAC;IAC5E;IACA8D,GAAG,CAACyC,WAAW,CAACR,WAAW,CAAC;IAC5B,OAAO,IAAI,CAACK,iBAAiB,CAACtC,GAAG,EAAElD,OAAO,CAAC;EAC/C;EACA;AACJ;AACA;EACI0F,qBAAqBA,CAACE,GAAG,EAAE;IACvB,MAAMC,GAAG,GAAG,IAAI,CAACtF,SAAS,CAACuF,aAAa,CAAC,KAAK,CAAC;IAC/CD,GAAG,CAACE,SAAS,GAAGH,GAAG;IACnB,MAAM1C,GAAG,GAAG2C,GAAG,CAACX,aAAa,CAAC,KAAK,CAAC;IACpC;IACA,IAAI,CAAChC,GAAG,EAAE;MACN,MAAM1D,KAAK,CAAC,qBAAqB,CAAC;IACtC;IACA,OAAO0D,GAAG;EACd;EACA;AACJ;AACA;EACIuC,aAAaA,CAACO,OAAO,EAAE;IACnB,MAAM9C,GAAG,GAAG,IAAI,CAACwC,qBAAqB,CAACtG,qBAAqB,CAAC,aAAa,CAAC,CAAC;IAC5E,MAAM6G,UAAU,GAAGD,OAAO,CAACC,UAAU;IACrC;IACA,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,UAAU,CAACtB,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,MAAM;QAAEtB,IAAI;QAAE8C;MAAM,CAAC,GAAGD,UAAU,CAACvB,CAAC,CAAC;MACrC,IAAItB,IAAI,KAAK,IAAI,EAAE;QACfF,GAAG,CAACiD,YAAY,CAAC/C,IAAI,EAAE8C,KAAK,CAAC;MACjC;IACJ;IACA,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,OAAO,CAACI,UAAU,CAACzB,MAAM,EAAED,CAAC,EAAE,EAAE;MAChD,IAAIsB,OAAO,CAACI,UAAU,CAAC1B,CAAC,CAAC,CAAC2B,QAAQ,KAAK,IAAI,CAAC9F,SAAS,CAAC+F,YAAY,EAAE;QAChEpD,GAAG,CAACyC,WAAW,CAACK,OAAO,CAACI,UAAU,CAAC1B,CAAC,CAAC,CAACU,SAAS,CAAC,IAAI,CAAC,CAAC;MAC1D;IACJ;IACA,OAAOlC,GAAG;EACd;EACA;AACJ;AACA;EACIsC,iBAAiBA,CAACtC,GAAG,EAAElD,OAAO,EAAE;IAC5BkD,GAAG,CAACiD,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC;IAC3BjD,GAAG,CAACiD,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;IAClCjD,GAAG,CAACiD,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC;IACjCjD,GAAG,CAACiD,YAAY,CAAC,qBAAqB,EAAE,eAAe,CAAC;IACxDjD,GAAG,CAACiD,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;IACxC,IAAInG,OAAO,IAAIA,OAAO,CAACuG,OAAO,EAAE;MAC5BrD,GAAG,CAACiD,YAAY,CAAC,SAAS,EAAEnG,OAAO,CAACuG,OAAO,CAAC;IAChD;IACA,OAAOrD,GAAG;EACd;EACA;AACJ;AACA;AACA;EACI6B,UAAUA,CAACyB,UAAU,EAAE;IACnB,MAAM;MAAE7G,GAAG,EAAEiD,OAAO;MAAE5C;IAAQ,CAAC,GAAGwG,UAAU;IAC5C,MAAMC,eAAe,GAAGzG,OAAO,EAAEyG,eAAe,IAAI,KAAK;IACzD,IAAI,CAAC,IAAI,CAACrG,WAAW,EAAE;MACnB,MAAMX,6BAA6B,CAAC,CAAC;IACzC;IACA;IACA,IAAImD,OAAO,IAAI,IAAI,EAAE;MACjB,MAAMpD,KAAK,CAAC,+BAA+BoD,OAAO,IAAI,CAAC;IAC3D;IACA,MAAMjD,GAAG,GAAG,IAAI,CAACU,UAAU,CAACsB,QAAQ,CAACjE,eAAe,CAACmF,YAAY,EAAED,OAAO,CAAC;IAC3E;IACA,IAAI,CAACjD,GAAG,EAAE;MACN,MAAMD,kCAAkC,CAACkD,OAAO,CAAC;IACrD;IACA;IACA;IACA;IACA,MAAM8D,eAAe,GAAG,IAAI,CAAC9F,qBAAqB,CAAC4B,GAAG,CAAC7C,GAAG,CAAC;IAC3D,IAAI+G,eAAe,EAAE;MACjB,OAAOA,eAAe;IAC1B;IACA,MAAMC,GAAG,GAAG,IAAI,CAACvG,WAAW,CAACoC,GAAG,CAAC7C,GAAG,EAAE;MAAEiH,YAAY,EAAE,MAAM;MAAEH;IAAgB,CAAC,CAAC,CAACxD,IAAI,CAAC1E,GAAG,CAAC2E,GAAG,IAAI;MAC7F;MACA;MACA,OAAO9D,qBAAqB,CAAC8D,GAAG,CAAC;IACrC,CAAC,CAAC,EAAEzE,QAAQ,CAAC,MAAM,IAAI,CAACmC,qBAAqB,CAACiG,MAAM,CAAClH,GAAG,CAAC,CAAC,EAAEjB,KAAK,CAAC,CAAC,CAAC;IACpE,IAAI,CAACkC,qBAAqB,CAAC0B,GAAG,CAAC3C,GAAG,EAAEgH,GAAG,CAAC;IACxC,OAAOA,GAAG;EACd;EACA;AACJ;AACA;AACA;AACA;AACA;EACIrF,iBAAiBA,CAACD,SAAS,EAAE9B,QAAQ,EAAEgE,MAAM,EAAE;IAC3C,IAAI,CAAC/C,eAAe,CAAC8B,GAAG,CAACgB,OAAO,CAACjC,SAAS,EAAE9B,QAAQ,CAAC,EAAEgE,MAAM,CAAC;IAC9D,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIrB,oBAAoBA,CAACb,SAAS,EAAEkC,MAAM,EAAE;IACpC,MAAMuD,eAAe,GAAG,IAAI,CAACpG,eAAe,CAAC8B,GAAG,CAACnB,SAAS,CAAC;IAC3D,IAAIyF,eAAe,EAAE;MACjBA,eAAe,CAACrF,IAAI,CAAC8B,MAAM,CAAC;IAChC,CAAC,MACI;MACD,IAAI,CAAC7C,eAAe,CAAC4B,GAAG,CAACjB,SAAS,EAAE,CAACkC,MAAM,CAAC,CAAC;IACjD;IACA,OAAO,IAAI;EACf;EACA;EACAO,qBAAqBA,CAACP,MAAM,EAAE;IAC1B,IAAI,CAACA,MAAM,CAACtD,UAAU,EAAE;MACpB,MAAMiD,GAAG,GAAG,IAAI,CAACwC,qBAAqB,CAACnC,MAAM,CAACxD,OAAO,CAAC;MACtD,IAAI,CAACyF,iBAAiB,CAACtC,GAAG,EAAEK,MAAM,CAACvD,OAAO,CAAC;MAC3CuD,MAAM,CAACtD,UAAU,GAAGiD,GAAG;IAC3B;IACA,OAAOK,MAAM,CAACtD,UAAU;EAC5B;EACA;EACAwD,2BAA2BA,CAACpC,SAAS,EAAE+B,IAAI,EAAE;IACzC,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5D,UAAU,CAAC6D,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMqC,MAAM,GAAG,IAAI,CAACjG,UAAU,CAAC4D,CAAC,CAAC,CAACtB,IAAI,EAAE/B,SAAS,CAAC;MAClD,IAAI0F,MAAM,EAAE;QACR,OAAOC,oBAAoB,CAACD,MAAM,CAAC,GAC7B,IAAIjH,aAAa,CAACiH,MAAM,CAACpH,GAAG,EAAE,IAAI,EAAEoH,MAAM,CAAC/G,OAAO,CAAC,GACnD,IAAIF,aAAa,CAACiH,MAAM,EAAE,IAAI,CAAC;MACzC;IACJ;IACA,OAAOlI,SAAS;EACpB;EACA,OAAOoI,IAAI,YAAAC,wBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFhH,eAAe,EAAzB1C,EAAE,CAAA2J,QAAA,CAAyC7J,EAAE,CAACC,UAAU,MAAxDC,EAAE,CAAA2J,QAAA,CAAmFnJ,EAAE,CAACC,YAAY,GAApGT,EAAE,CAAA2J,QAAA,CAA+GzJ,QAAQ,MAAzHF,EAAE,CAAA2J,QAAA,CAAoJ3J,EAAE,CAACO,YAAY;EAAA;EAC9P,OAAOqJ,KAAK,kBAD6E5J,EAAE,CAAA6J,kBAAA;IAAAC,KAAA,EACYpH,eAAe;IAAAqH,OAAA,EAAfrH,eAAe,CAAA8G,IAAA;IAAAQ,UAAA,EAAc;EAAM;AAC9I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FjK,EAAE,CAAAkK,iBAAA,CAGJxH,eAAe,EAAc,CAAC;IAC7GyH,IAAI,EAAEhK,UAAU;IAChBiK,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAErK,EAAE,CAACC,UAAU;IAAEsK,UAAU,EAAE,CAAC;MACnDF,IAAI,EAAE/J;IACV,CAAC;EAAE,CAAC,EAAE;IAAE+J,IAAI,EAAE3J,EAAE,CAACC;EAAa,CAAC,EAAE;IAAE0J,IAAI,EAAE/I,SAAS;IAAEiJ,UAAU,EAAE,CAAC;MAC7DF,IAAI,EAAE/J;IACV,CAAC,EAAE;MACC+J,IAAI,EAAE9J,MAAM;MACZ+J,IAAI,EAAE,CAAClK,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEiK,IAAI,EAAEnK,EAAE,CAACO;EAAa,CAAC,CAAC;AAAA;AAChD;AACA;AACA;AACA;AACA;AACA,SAAS+J,8BAA8BA,CAACC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,YAAY,EAAEnH,QAAQ,EAAE;EACnG,OAAOgH,cAAc,IAAI,IAAI7H,eAAe,CAAC8H,UAAU,EAAEC,SAAS,EAAElH,QAAQ,EAAEmH,YAAY,CAAC;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG;EAC3B;EACAC,OAAO,EAAElI,eAAe;EACxBmI,IAAI,EAAE,CACF,CAAC,IAAIzK,QAAQ,CAAC,CAAC,EAAE,IAAIE,QAAQ,CAAC,CAAC,EAAEoC,eAAe,CAAC,EACjD,CAAC,IAAItC,QAAQ,CAAC,CAAC,EAAEL,UAAU,CAAC,EAC5BU,YAAY,EACZF,YAAY,EACZ,CAAC,IAAIH,QAAQ,CAAC,CAAC,EAAEF,QAAQ,CAAC,CAC7B;EACD4K,UAAU,EAAER;AAChB,CAAC;AACD;AACA,SAAShF,QAAQA,CAACG,GAAG,EAAE;EACnB,OAAOA,GAAG,CAACkC,SAAS,CAAC,IAAI,CAAC;AAC9B;AACA;AACA,SAAS9B,OAAOA,CAACjC,SAAS,EAAE+B,IAAI,EAAE;EAC9B,OAAO/B,SAAS,GAAG,GAAG,GAAG+B,IAAI;AACjC;AACA,SAAS4D,oBAAoBA,CAACd,KAAK,EAAE;EACjC,OAAO,CAAC,EAAEA,KAAK,CAACvG,GAAG,IAAIuG,KAAK,CAAClG,OAAO,CAAC;AACzC;AAEA,SAAS+H,8BAA8B,IAAIS,CAAC,EAAErI,eAAe,IAAIsI,CAAC,EAAEhJ,6BAA6B,IAAIiJ,CAAC,EAAEhJ,kCAAkC,IAAIiJ,CAAC,EAAE/I,sCAAsC,IAAIgJ,CAAC,EAAER,sBAAsB,IAAIS,CAAC,EAAEvJ,2BAA2B,IAAIwJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}