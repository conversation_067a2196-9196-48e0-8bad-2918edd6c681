import { UserProfile, securityId } from '@loopback/security';
export interface TokenPayload {
    [securityId]: string;
    id: string;
    email: string;
    roles: string[];
    iat?: number;
    exp?: number;
    iss?: string;
    aud?: string;
}
export interface RefreshTokenPayload {
    userId: string;
    tokenId: string;
    iat?: number;
    exp?: number;
}
export declare class JwtService {
    private readonly jwtSecret;
    private readonly jwtExpiresIn;
    private readonly refreshExpiresIn;
    private readonly algorithm;
    private readonly issuer;
    private readonly audience;
    constructor();
    /**
     * Generate access token for user
     */
    generateToken(userProfile: UserProfile): Promise<string>;
    /**
     * Generate refresh token for user
     */
    generateRefreshToken(userId: string): Promise<string>;
    /**
     * Verify and decode access token
     */
    verifyToken(token: string): Promise<UserProfile>;
    /**
     * Verify refresh token
     */
    verifyRefreshToken(refreshToken: string): Promise<RefreshTokenPayload>;
    /**
     * Extract token from Authorization header
     */
    extractTokenFromHeader(authHeader: string): string;
    /**
     * Check if token is expired without throwing error
     */
    isTokenExpired(token: string): boolean;
    /**
     * Get token expiration time
     */
    getTokenExpiration(token: string): Date | null;
    /**
     * Generate unique token ID for refresh tokens
     */
    private generateTokenId;
    /**
     * Blacklist token (for logout functionality)
     * In production, implement with Redis or database
     */
    blacklistToken(token: string): Promise<void>;
}
