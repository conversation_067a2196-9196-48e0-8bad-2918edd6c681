{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nexport { B as BreakpointObserver, M as MediaMatcher } from './breakpoints-observer-QutrMj4x.mjs';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport './platform-DNDzkVcI.mjs';\nimport '@angular/common';\nimport './array-I1yfCXUO.mjs';\nclass LayoutModule {\n  static ɵfac = function LayoutModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LayoutModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: LayoutModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LayoutModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nconst Breakpoints = {\n  XSmall: '(max-width: 599.98px)',\n  Small: '(min-width: 600px) and (max-width: 959.98px)',\n  Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n  Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n  XLarge: '(min-width: 1920px)',\n  Handset: '(max-width: 599.98px) and (orientation: portrait), ' + '(max-width: 959.98px) and (orientation: landscape)',\n  Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' + '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  Web: '(min-width: 840px) and (orientation: portrait), ' + '(min-width: 1280px) and (orientation: landscape)',\n  HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n  TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n  WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n  HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n  TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  WebLandscape: '(min-width: 1280px) and (orientation: landscape)'\n};\nexport { Breakpoints, LayoutModule };", "map": {"version": 3, "names": ["i0", "NgModule", "B", "BreakpointObserver", "M", "MediaMatcher", "LayoutModule", "ɵfac", "LayoutModule_Factory", "__ngFactoryType__", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "ngDevMode", "ɵsetClassMetadata", "args", "Breakpoints", "XSmall", "Small", "Medium", "Large", "<PERSON>L<PERSON>ge", "Handset", "Tablet", "Web", "HandsetPortrait", "TabletPortrait", "WebPortrait", "HandsetLandscape", "TabletLandscape", "WebLandscape"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/cdk/fesm2022/layout.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nexport { B as BreakpointObserver, M as MediaMatcher } from './breakpoints-observer-QutrMj4x.mjs';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport './platform-DNDzkVcI.mjs';\nimport '@angular/common';\nimport './array-I1yfCXUO.mjs';\n\nclass LayoutModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: LayoutModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: LayoutModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: LayoutModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: LayoutModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nconst Breakpoints = {\n    XSmall: '(max-width: 599.98px)',\n    Small: '(min-width: 600px) and (max-width: 959.98px)',\n    Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n    Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n    XLarge: '(min-width: 1920px)',\n    Handset: '(max-width: 599.98px) and (orientation: portrait), ' +\n        '(max-width: 959.98px) and (orientation: landscape)',\n    Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' +\n        '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n    Web: '(min-width: 840px) and (orientation: portrait), ' +\n        '(min-width: 1280px) and (orientation: landscape)',\n    HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n    TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n    WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n    HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n    TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n    WebLandscape: '(min-width: 1280px) and (orientation: landscape)',\n};\n\nexport { Breakpoints, LayoutModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,YAAY,QAAQ,qCAAqC;AAChG,OAAO,MAAM;AACb,OAAO,gBAAgB;AACvB,OAAO,yBAAyB;AAChC,OAAO,iBAAiB;AACxB,OAAO,sBAAsB;AAE7B,MAAMC,YAAY,CAAC;EACf,OAAOC,IAAI,YAAAC,qBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFH,YAAY;EAAA;EAC/G,OAAOI,IAAI,kBAD8EV,EAAE,CAAAW,gBAAA;IAAAC,IAAA,EACSN;EAAY;EAChH,OAAOO,IAAI,kBAF8Eb,EAAE,CAAAc,gBAAA;AAG/F;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAJ6Ff,EAAE,CAAAgB,iBAAA,CAIJV,YAAY,EAAc,CAAC;IAC1GM,IAAI,EAAEX,QAAQ;IACdgB,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;AAAA;;AAEV;AACA;AACA,MAAMC,WAAW,GAAG;EAChBC,MAAM,EAAE,uBAAuB;EAC/BC,KAAK,EAAE,8CAA8C;EACrDC,MAAM,EAAE,+CAA+C;EACvDC,KAAK,EAAE,gDAAgD;EACvDC,MAAM,EAAE,qBAAqB;EAC7BC,OAAO,EAAE,qDAAqD,GAC1D,oDAAoD;EACxDC,MAAM,EAAE,4EAA4E,GAChF,4EAA4E;EAChFC,GAAG,EAAE,kDAAkD,GACnD,kDAAkD;EACtDC,eAAe,EAAE,mDAAmD;EACpEC,cAAc,EAAE,0EAA0E;EAC1FC,WAAW,EAAE,gDAAgD;EAC7DC,gBAAgB,EAAE,oDAAoD;EACtEC,eAAe,EAAE,4EAA4E;EAC7FC,YAAY,EAAE;AAClB,CAAC;AAED,SAASd,WAAW,EAAEZ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}