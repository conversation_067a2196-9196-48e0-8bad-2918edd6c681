#!/usr/bin/env node

/**
 * Password Verification Test Script
 */

require('dotenv').config();
const { Client } = require('pg');
const bcrypt = require('bcryptjs');

async function testPasswordVerification() {
  console.log('🔐 TESTING PASSWORD VERIFICATION');
  console.log('=================================\n');

  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'secure_backend',
  });

  try {
    await client.connect();
    console.log('✅ Connected to PostgreSQL');

    // Get user data
    const result = await client.query(
      'SELECT email, password, email_verified FROM public."user" WHERE email = $1',
      ['<EMAIL>']
    );

    if (result.rows.length === 0) {
      console.log('❌ User not found in database');
      return;
    }

    const user = result.rows[0];
    console.log('📊 User found:');
    console.log('   Email:', user.email);
    console.log('   Email Verified:', user.email_verified);
    console.log('   Password Hash Length:', user.password ? user.password.length : 'null');
    console.log('   Password Hash Preview:', user.password ? user.password.substring(0, 20) + '...' : 'null');

    // Test password verification
    const testPassword = 'Test123!@#';
    console.log('\n🔍 Testing password:', testPassword);
    
    if (user.password) {
      const isValid = await bcrypt.compare(testPassword, user.password);
      console.log('✅ Password verification result:', isValid);
      
      if (!isValid) {
        console.log('❌ Password does not match');
        console.log('💡 Trying to create new hash for comparison...');
        const newHash = await bcrypt.hash(testPassword, 12);
        console.log('   New hash:', newHash.substring(0, 20) + '...');
        const newVerify = await bcrypt.compare(testPassword, newHash);
        console.log('   New hash verification:', newVerify);
      }
    } else {
      console.log('❌ No password hash found');
    }

    // Test admin user too
    console.log('\n🔍 Testing admin user...');
    const adminResult = await client.query(
      'SELECT email, password, email_verified FROM public."user" WHERE email = $1',
      ['<EMAIL>']
    );

    if (adminResult.rows.length > 0) {
      const admin = adminResult.rows[0];
      console.log('📊 Admin user found:');
      console.log('   Email:', admin.email);
      console.log('   Email Verified:', admin.email_verified);
      
      const adminPassword = 'Admin123!@#';
      console.log('🔍 Testing admin password:', adminPassword);
      
      if (admin.password) {
        const isAdminValid = await bcrypt.compare(adminPassword, admin.password);
        console.log('✅ Admin password verification result:', isAdminValid);
      }
    }

    await client.end();
    console.log('\n🎉 Password verification test completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    await client.end();
  }
}

if (require.main === module) {
  testPasswordVerification().catch(console.error);
}

module.exports = { testPasswordVerification };
