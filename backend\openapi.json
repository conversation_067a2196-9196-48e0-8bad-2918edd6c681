{"openapi": "3.0.0", "info": {"title": "secure-backend", "version": "1.0.0", "description": "Secure modular backend with authentication and payments", "contact": {}}, "paths": {"/2fa/disable": {"post": {"x-controller-name": "TwoFactorController", "x-operation-name": "disable2FA", "tags": ["TwoFactorController"], "responses": {"200": {"description": "Disable two-factor authentication", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["token"], "properties": {"token": {"type": "string"}}}}}}, "operationId": "TwoFactorController.disable2FA"}}, "/2fa/send-email": {"post": {"x-controller-name": "TwoFactorController", "x-operation-name": "send2FAEmail", "tags": ["TwoFactorController"], "responses": {"200": {"description": "Send 2FA code via email", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}, "operationId": "TwoFactorController.send2FAEmail"}}, "/2fa/send-sms": {"post": {"x-controller-name": "TwoFactorController", "x-operation-name": "send2FASMS", "tags": ["TwoFactorController"], "responses": {"200": {"description": "Send 2FA code via SMS", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}, "operationId": "TwoFactorController.send2FASMS"}}, "/2fa/setup": {"post": {"x-controller-name": "TwoFactorController", "x-operation-name": "setup2FA", "tags": ["TwoFactorController"], "responses": {"200": {"description": "Setup two-factor authentication", "content": {"application/json": {"schema": {"type": "object", "properties": {"secret": {"type": "string"}, "qrCode": {"type": "string"}}}}}}}, "operationId": "TwoFactorController.setup2FA"}}, "/2fa/status": {"get": {"x-controller-name": "TwoFactorController", "x-operation-name": "get2FAStatus", "tags": ["TwoFactorController"], "responses": {"200": {"description": "Get two-factor authentication status", "content": {"application/json": {"schema": {"type": "object", "properties": {"enabled": {"type": "boolean"}}}}}}}, "operationId": "TwoFactorController.get2FAStatus"}}, "/2fa/verify": {"post": {"x-controller-name": "TwoFactorController", "x-operation-name": "verify2FA", "tags": ["TwoFactorController"], "responses": {"200": {"description": "Verify and enable two-factor authentication", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["token"], "properties": {"token": {"type": "string"}}}}}}, "operationId": "TwoFactorController.verify2FA"}}, "/2fa/verify-email": {"post": {"x-controller-name": "TwoFactorController", "x-operation-name": "verify2FAEmail", "tags": ["TwoFactorController"], "responses": {"200": {"description": "Verify 2FA email code", "content": {"application/json": {"schema": {"type": "object", "properties": {"valid": {"type": "boolean"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["code"], "properties": {"code": {"type": "string"}}}}}}, "operationId": "TwoFactorController.verify2FAEmail"}}, "/2fa/verify-sms": {"post": {"x-controller-name": "TwoFactorController", "x-operation-name": "verify2FASMS", "tags": ["TwoFactorController"], "responses": {"200": {"description": "Verify 2FA SMS code", "content": {"application/json": {"schema": {"type": "object", "properties": {"valid": {"type": "boolean"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["code"], "properties": {"code": {"type": "string"}}}}}}, "operationId": "TwoFactorController.verify2FASMS"}}, "/auth/forgot-password": {"post": {"x-controller-name": "AuthController", "x-operation-name": "forgotPassword", "tags": ["AuthController"], "responses": {"200": {"description": "Password reset request", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email"}}}}}}, "operationId": "AuthController.forgotPassword"}}, "/auth/login": {"post": {"x-controller-name": "AuthController", "x-operation-name": "login", "tags": ["AuthController"], "responses": {"200": {"description": "Token", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"$ref": "#/components/schemas/UserExcluding_password_"}, "requiresTwoFactor": {"type": "boolean"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 8}, "twoFactorToken": {"type": "string"}}}}}}, "operationId": "AuthController.login"}}, "/auth/reset-password": {"post": {"x-controller-name": "AuthController", "x-operation-name": "resetPassword", "tags": ["AuthController"], "responses": {"200": {"description": "Password reset", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["token", "password"], "properties": {"token": {"type": "string"}, "password": {"type": "string", "minLength": 8}}}}}}, "operationId": "AuthController.resetPassword"}}, "/auth/signup": {"post": {"x-controller-name": "AuthController", "x-operation-name": "signUp", "tags": ["AuthController"], "responses": {"200": {"description": "User registration", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "userId": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUser"}}}}, "operationId": "AuthController.signUp"}}, "/auth/verify-email": {"post": {"x-controller-name": "AuthController", "x-operation-name": "verifyEmail", "tags": ["AuthController"], "responses": {"200": {"description": "Email verification", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["token"], "properties": {"token": {"type": "string"}}}}}}, "operationId": "AuthController.verifyEmail"}}, "/otp/login": {"post": {"x-controller-name": "OtpController", "x-operation-name": "loginWithOTP", "tags": ["OtpController"], "responses": {"200": {"description": "Login with OTP", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"type": "object"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["identifier", "code"], "properties": {"identifier": {"type": "string"}, "code": {"type": "string"}}}}}}, "operationId": "OtpController.loginWithOTP"}}, "/otp/send-email": {"post": {"x-controller-name": "OtpController", "x-operation-name": "sendEmailOTP", "tags": ["OtpController"], "responses": {"200": {"description": "Send OTP via email", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["email", "type"], "properties": {"email": {"type": "string", "format": "email"}, "type": {"type": "string", "enum": ["login", "verification"]}}}}}}, "operationId": "OtpController.sendEmailOTP"}}, "/otp/send-sms": {"post": {"x-controller-name": "OtpController", "x-operation-name": "sendSMSOTP", "tags": ["OtpController"], "responses": {"200": {"description": "Send OTP via SMS", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["phone", "type"], "properties": {"phone": {"type": "string"}, "type": {"type": "string", "enum": ["login", "verification"]}}}}}}, "operationId": "OtpController.sendSMSOTP"}}, "/otp/verify": {"post": {"x-controller-name": "OtpController", "x-operation-name": "verifyOTP", "tags": ["OtpController"], "responses": {"200": {"description": "Verify OTP", "content": {"application/json": {"schema": {"type": "object", "properties": {"valid": {"type": "boolean"}, "message": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["identifier", "code", "type"], "properties": {"identifier": {"type": "string"}, "code": {"type": "string"}, "type": {"type": "string", "enum": ["login", "verification"]}}}}}}, "operationId": "OtpController.verifyOTP"}}, "/payments/create-order": {"post": {"x-controller-name": "PaymentController", "x-operation-name": "createOrder", "tags": ["PaymentController"], "responses": {"200": {"description": "Create payment order", "content": {"application/json": {"schema": {"type": "object", "properties": {"orderId": {"type": "string"}, "amount": {"type": "number"}, "currency": {"type": "string"}, "key": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["amount", "currency"], "properties": {"amount": {"type": "number", "minimum": 1, "maximum": 1000000}, "currency": {"type": "string", "enum": ["INR", "USD"]}, "description": {"type": "string"}}}}}}, "operationId": "PaymentController.createOrder"}}, "/payments/my-payments": {"get": {"x-controller-name": "PaymentController", "x-operation-name": "getMyPayments", "tags": ["PaymentController"], "responses": {"200": {"description": "Get user payments", "content": {"application/json": {"schema": {"type": "object", "properties": {"payments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "amount": {"type": "number"}, "currency": {"type": "string"}, "description": {"type": "string"}, "createdAt": {"type": "string"}, "paidAt": {"type": "string"}}}}}}}}}}, "operationId": "PaymentController.getMyPayments"}}, "/payments/refund": {"post": {"x-controller-name": "PaymentController", "x-operation-name": "refundPayment", "tags": ["PaymentController"], "responses": {"200": {"description": "Refund payment", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["paymentId"], "properties": {"paymentId": {"type": "string"}, "amount": {"type": "number"}}}}}}, "operationId": "PaymentController.refundPayment"}}, "/payments/status/{orderId}": {"get": {"x-controller-name": "PaymentController", "x-operation-name": "getPaymentStatus", "tags": ["PaymentController"], "responses": {"200": {"description": "Get payment status", "content": {"application/json": {"schema": {"type": "object", "properties": {"payment": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "amount": {"type": "number"}, "currency": {"type": "string"}, "createdAt": {"type": "string"}, "paidAt": {"type": "string"}}}}}}}}}, "parameters": [{"name": "orderId", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "PaymentController.getPaymentStatus"}}, "/payments/verify": {"post": {"x-controller-name": "PaymentController", "x-operation-name": "verifyPayment", "tags": ["PaymentController"], "responses": {"200": {"description": "Verify payment", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["orderId", "paymentId", "signature"], "properties": {"orderId": {"type": "string"}, "paymentId": {"type": "string"}, "signature": {"type": "string"}}}}}}, "operationId": "PaymentController.verifyPayment"}}, "/payments/webhook": {"post": {"x-controller-name": "PaymentWebhookController", "x-operation-name": "handleWebhook", "tags": ["PaymentWebhookController"], "responses": {"200": {"description": "Payment webhook", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}}}}}}}, "parameters": [{"name": "x-razorpay-signature", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "operationId": "PaymentWebhookController.handleWebhook"}}, "/ping": {"get": {"x-controller-name": "PingController", "x-operation-name": "ping", "tags": ["PingController"], "responses": {"200": {"description": "Ping Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PingResponse"}}}}}, "operationId": "PingController.ping"}}}, "servers": [{"url": "http://localhost:3002"}], "components": {"schemas": {"NewUser": {"title": "NewUser", "type": "object", "description": "(tsType: Omit<User, 'id' | 'emailVerified' | 'phoneVerified' | 'twoFactorEnabled' | 'roles' | 'isActive' | 'createdAt' | 'updatedAt'>, schemaOptions: { title: 'NewUser', exclude: [ 'id', 'emailVerified', 'phoneVerified', 'twoFactorEnabled', 'roles', 'isActive', 'createdAt', 'updatedAt' ] })", "properties": {"email": {"type": "string", "format": "email", "minLength": 5, "maxLength": 100, "errorMessage": "Email should be a valid email address"}, "firstName": {"type": "string", "minLength": 2, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$", "errorMessage": "First name should contain only letters and spaces"}, "lastName": {"type": "string", "minLength": 2, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$", "errorMessage": "Last name should contain only letters and spaces"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$", "errorMessage": "Phone number should be a valid international format"}, "twoFactorSecret": {"type": "string"}, "lastLoginAt": {"type": "string", "format": "date-time"}, "loginAttempts": {"type": "number"}, "lockUntil": {"type": "string", "format": "date-time"}, "emailVerificationToken": {"type": "string"}, "emailVerificationExpires": {"type": "string", "format": "date-time"}, "passwordResetToken": {"type": "string"}, "passwordResetExpires": {"type": "string", "format": "date-time"}, "password": {"type": "string"}}, "required": ["email", "firstName", "lastName"], "additionalProperties": false, "x-typescript-type": "Omit<User, 'id' | 'emailVerified' | 'phoneVerified' | 'twoFactorEnabled' | 'roles' | 'isActive' | 'createdAt' | 'updatedAt'>"}, "UserExcluding_password_": {"title": "UserExcluding_password_", "type": "object", "description": "(tsType: Omit<User, 'password'>, schemaOptions: { exclude: [ 'password' ] })", "properties": {"id": {"type": "string"}, "email": {"type": "string", "format": "email", "minLength": 5, "maxLength": 100, "errorMessage": "Email should be a valid email address"}, "firstName": {"type": "string", "minLength": 2, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$", "errorMessage": "First name should contain only letters and spaces"}, "lastName": {"type": "string", "minLength": 2, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$", "errorMessage": "Last name should contain only letters and spaces"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$", "errorMessage": "Phone number should be a valid international format"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "twoFactorEnabled": {"type": "boolean"}, "twoFactorSecret": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "lastLoginAt": {"type": "string", "format": "date-time"}, "loginAttempts": {"type": "number"}, "lockUntil": {"type": "string", "format": "date-time"}, "emailVerificationToken": {"type": "string"}, "emailVerificationExpires": {"type": "string", "format": "date-time"}, "passwordResetToken": {"type": "string"}, "passwordResetExpires": {"type": "string", "format": "date-time"}}, "required": ["email", "firstName", "lastName"], "additionalProperties": false, "x-typescript-type": "Omit<User, 'password'>"}, "PingResponse": {"type": "object", "title": "PingResponse", "properties": {"greeting": {"type": "string"}, "date": {"type": "string"}, "url": {"type": "string"}, "headers": {"type": "object", "properties": {"Content-Type": {"type": "string"}}, "additionalProperties": true}}}}, "securitySchemes": {"jwt": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"jwt": []}]}