"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecureBackendApplication = void 0;
const tslib_1 = require("tslib");
const boot_1 = require("@loopback/boot");
const rest_explorer_1 = require("@loopback/rest-explorer");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const service_proxy_1 = require("@loopback/service-proxy");
const path_1 = tslib_1.__importDefault(require("path"));
const authentication_1 = require("@loopback/authentication");
const authentication_jwt_1 = require("@loopback/authentication-jwt");
const authorization_1 = require("@loopback/authorization");
const services_1 = require("./services");
const sequence_1 = require("./sequence");
class SecureBackendApplication extends (0, boot_1.BootMixin)((0, service_proxy_1.ServiceMixin)((0, repository_1.RepositoryMixin)(rest_1.RestApplication))) {
    constructor(options = {}) {
        super(options);
        // Configure CORS
        this.configure('rest.cors').to({
            origin: process.env.FRONTEND_URL || 'http://localhost:3001',
            methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
            allowedHeaders: [
                'Origin',
                'X-Requested-With',
                'Content-Type',
                'Accept',
                'Authorization',
                'X-CSRF-Token'
            ],
            credentials: true,
            preflightContinue: false,
            optionsSuccessStatus: 204
        });
        // Set up the custom sequence
        this.sequence(sequence_1.SecuritySequence);
        // Set up default home page
        this.static('/', path_1.default.join(__dirname, '../public'));
        // Customize @loopback/rest-explorer configuration here
        this.configure(rest_explorer_1.RestExplorerBindings.COMPONENT).to({
            path: '/explorer',
        });
        this.component(rest_explorer_1.RestExplorerComponent);
        // Add authentication component
        this.component(authentication_1.AuthenticationComponent);
        this.component(authentication_jwt_1.JWTAuthenticationComponent);
        this.component(authorization_1.AuthorizationComponent);
        // Use default JWT user service for now (we'll handle auth in controllers)
        // this.bind(UserServiceBindings.USER_SERVICE).toClass(MyUserService);
        // Bind custom JWT service
        this.bind('services.JwtService').toClass(services_1.JwtService);
        // Bind other services with proper imports
        this.bind('services.SecurityService').toClass(services_1.SecurityService);
        this.bind('services.EmailService').toClass(services_1.EmailService);
        this.bind('services.SmsService').toClass(services_1.SmsService);
        this.bind('services.PaymentService').toClass(services_1.PaymentService);
        this.projectRoot = __dirname;
        // Customize @loopback/boot Booter Conventions here
        this.bootOptions = {
            controllers: {
                // Customize ControllerBooter Conventions here
                dirs: ['controllers'],
                extensions: ['.controller.js'],
                nested: true,
            },
        };
    }
}
exports.SecureBackendApplication = SecureBackendApplication;
//# sourceMappingURL=application.js.map