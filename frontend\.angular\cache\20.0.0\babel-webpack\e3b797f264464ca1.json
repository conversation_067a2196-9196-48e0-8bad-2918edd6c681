{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/payment.service\";\nimport * as i3 from \"../../../services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/divider\";\nfunction PaymentTestComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PaymentTestComponent_button_19_Template_button_click_0_listener() {\n      const testAmount_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectTestAmount(testAmount_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const testAmount_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"color\", ((tmp_2_0 = ctx_r2.paymentForm.get(\"currency\")) == null ? null : tmp_2_0.value) === testAmount_r2.currency ? \"primary\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", testAmount_r2.label, \" \");\n  }\n}\nfunction PaymentTestComponent_mat_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const currency_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", currency_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", currency_r4, \" \");\n  }\n}\nfunction PaymentTestComponent_mat_hint_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getEquivalentAmount());\n  }\n}\nfunction PaymentTestComponent_mat_spinner_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 29);\n  }\n}\nfunction PaymentTestComponent_mat_icon_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"lock\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentTestComponent_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Pay Securely with Razorpay\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentTestComponent_mat_card_57_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"div\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 36)(5, \"div\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 39);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 40)(13, \"div\", 41)(14, \"mat-icon\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const payment_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatCurrency(payment_r5.amount, payment_r5.currency), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(payment_r5.description || \"Test Payment\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 7, payment_r5.createdAt, \"medium\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", payment_r5.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", \"status-\" + payment_r5.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getStatusIcon(payment_r5.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 10, payment_r5.status), \" \");\n  }\n}\nfunction PaymentTestComponent_mat_card_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 30)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Payment History \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7, \" Your recent test transactions \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 31);\n    i0.ɵɵtemplate(10, PaymentTestComponent_mat_card_57_div_10_Template, 18, 12, \"div\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.payments);\n  }\n}\nexport class PaymentTestComponent {\n  constructor(formBuilder, paymentService, authService, snackBar) {\n    this.formBuilder = formBuilder;\n    this.paymentService = paymentService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.loading = false;\n    this.payments = [];\n    this.supportedCurrencies = environment.razorpay.supportedCurrencies;\n    this.testAmounts = [{\n      amount: 100,\n      currency: 'INR',\n      label: '₹100 - Basic Test'\n    }, {\n      amount: 500,\n      currency: 'INR',\n      label: '₹500 - Standard Test'\n    }, {\n      amount: 1000,\n      currency: 'INR',\n      label: '₹1,000 - Premium Test'\n    }, {\n      amount: 5,\n      currency: 'USD',\n      label: '$5 - Basic Test'\n    }, {\n      amount: 25,\n      currency: 'USD',\n      label: '$25 - Standard Test'\n    }, {\n      amount: 50,\n      currency: 'USD',\n      label: '$50 - Premium Test'\n    }];\n    this.paymentForm = this.formBuilder.group({\n      amount: ['', [Validators.required, Validators.min(1), Validators.max(1000000)]],\n      currency: [environment.razorpay.currency, [Validators.required]],\n      description: ['Payment test transaction']\n    });\n  }\n  ngOnInit() {\n    this.loadPaymentHistory();\n  }\n  onSubmit() {\n    if (this.paymentForm.invalid) {\n      this.markFormGroupTouched(this.paymentForm);\n      return;\n    }\n    const paymentRequest = this.paymentForm.value;\n    const validation = this.paymentService.validateAmount(paymentRequest.amount, paymentRequest.currency);\n    if (!validation.valid) {\n      this.snackBar.open(validation.error, 'Close', {\n        duration: 5000\n      });\n      return;\n    }\n    this.loading = true;\n    this.paymentService.processPayment(paymentRequest).subscribe({\n      next: response => {\n        if (response.success) {\n          this.snackBar.open('Payment completed successfully!', 'Close', {\n            duration: 5000\n          });\n          this.loadPaymentHistory();\n          this.paymentForm.patchValue({\n            amount: '',\n            description: 'Payment test transaction'\n          });\n        } else {\n          this.snackBar.open(response.message || 'Payment failed', 'Close', {\n            duration: 5000\n          });\n        }\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Payment processing failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  selectTestAmount(testAmount) {\n    this.paymentForm.patchValue({\n      amount: testAmount.amount,\n      currency: testAmount.currency,\n      description: `Test payment - ${testAmount.label}`\n    });\n  }\n  loadPaymentHistory() {\n    this.paymentService.getMyPayments().subscribe({\n      next: response => {\n        this.payments = response.payments;\n      },\n      error: error => {\n        console.error('Failed to load payment history:', error);\n      }\n    });\n  }\n  formatCurrency(amount, currency) {\n    return this.paymentService.formatCurrency(amount, currency);\n  }\n  getStatusIcon(status) {\n    return this.paymentService.getStatusIcon(status);\n  }\n  getFieldError(fieldName) {\n    const field = this.paymentForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['min']) return `Minimum amount is ${field.errors['min'].min}`;\n      if (field.errors['max']) return `Maximum amount is ${field.errors['max'].max}`;\n    }\n    return '';\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  convertAmount(amount, fromCurrency, toCurrency) {\n    return this.paymentService.convertCurrency(amount, fromCurrency, toCurrency);\n  }\n  getEquivalentAmount() {\n    const amount = this.paymentForm.get('amount')?.value;\n    const currency = this.paymentForm.get('currency')?.value;\n    if (!amount || !currency) return '';\n    const otherCurrency = currency === 'INR' ? 'USD' : 'INR';\n    const convertedAmount = this.convertAmount(amount, currency, otherCurrency);\n    return `≈ ${this.formatCurrency(convertedAmount, otherCurrency)}`;\n  }\n  static #_ = this.ɵfac = function PaymentTestComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PaymentTestComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.PaymentService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PaymentTestComponent,\n    selectors: [[\"app-payment-test\"]],\n    standalone: false,\n    decls: 84,\n    vars: 11,\n    consts: [[1, \"payment-container\"], [1, \"container\"], [1, \"subtitle\"], [1, \"payment-card\"], [1, \"test-amounts\"], [1, \"amount-buttons\"], [\"mat-stroked-button\", \"\", 3, \"color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"my-3\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"currency-field\"], [\"formControlName\", \"currency\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matSuffix\", \"\"], [\"appearance\", \"outline\", 1, \"amount-field\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"amount\", \"min\", \"1\", \"step\", \"0.01\"], [4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"maxlength\", \"100\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"payment-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"security-notice\"], [\"class\", \"payment-history\", 4, \"ngIf\"], [1, \"testing-guide\"], [1, \"guide-section\"], [1, \"test-cards\"], [1, \"test-card\"], [\"mat-stroked-button\", \"\", 3, \"click\", \"color\"], [3, \"value\"], [\"diameter\", \"20\"], [1, \"payment-history\"], [1, \"payment-list\"], [\"class\", \"payment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"payment-item\"], [1, \"payment-info\"], [1, \"payment-amount\"], [1, \"payment-details\"], [1, \"payment-description\"], [1, \"payment-date\"], [1, \"payment-id\"], [1, \"payment-actions\"], [1, \"payment-status\", 3, \"ngClass\"]],\n    template: function PaymentTestComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n        i0.ɵɵtext(3, \"Payment Testing Interface\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"p\", 2);\n        i0.ɵɵtext(5, \"Test Razorpay integration with secure payment processing\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"mat-card\", 3)(7, \"mat-card-header\")(8, \"mat-card-title\")(9, \"mat-icon\");\n        i0.ɵɵtext(10, \"payment\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(11, \" Make a Test Payment \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"mat-card-subtitle\");\n        i0.ɵɵtext(13, \" Test payments using Razorpay's secure checkout \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"mat-card-content\")(15, \"div\", 4)(16, \"h3\");\n        i0.ɵɵtext(17, \"Quick Test Amounts\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"div\", 5);\n        i0.ɵɵtemplate(19, PaymentTestComponent_button_19_Template, 2, 2, \"button\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(20, \"mat-divider\", 7);\n        i0.ɵɵelementStart(21, \"form\", 8);\n        i0.ɵɵlistener(\"ngSubmit\", function PaymentTestComponent_Template_form_ngSubmit_21_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(22, \"div\", 9)(23, \"mat-form-field\", 10)(24, \"mat-label\");\n        i0.ɵɵtext(25, \"Currency\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"mat-select\", 11);\n        i0.ɵɵtemplate(27, PaymentTestComponent_mat_option_27_Template, 2, 2, \"mat-option\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"mat-icon\", 13);\n        i0.ɵɵtext(29, \"attach_money\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"mat-form-field\", 14)(31, \"mat-label\");\n        i0.ɵɵtext(32, \"Amount\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(33, \"input\", 15);\n        i0.ɵɵelementStart(34, \"span\", 13);\n        i0.ɵɵtext(35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"mat-error\");\n        i0.ɵɵtext(37);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(38, PaymentTestComponent_mat_hint_38_Template, 2, 1, \"mat-hint\", 16);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(39, \"mat-form-field\", 17)(40, \"mat-label\");\n        i0.ɵɵtext(41, \"Description (Optional)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(42, \"input\", 18);\n        i0.ɵɵelementStart(43, \"mat-icon\", 13);\n        i0.ɵɵtext(44, \"description\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"button\", 19);\n        i0.ɵɵtemplate(46, PaymentTestComponent_mat_spinner_46_Template, 1, 0, \"mat-spinner\", 20)(47, PaymentTestComponent_mat_icon_47_Template, 2, 0, \"mat-icon\", 16)(48, PaymentTestComponent_span_48_Template, 2, 0, \"span\", 16);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(49, \"div\", 21)(50, \"mat-icon\");\n        i0.ɵɵtext(51, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(52, \"div\")(53, \"strong\");\n        i0.ɵɵtext(54, \"Secure Payment Processing\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(55, \"p\");\n        i0.ɵɵtext(56, \"All payments are processed securely through Razorpay's PCI DSS compliant platform.\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵtemplate(57, PaymentTestComponent_mat_card_57_Template, 11, 1, \"mat-card\", 22);\n        i0.ɵɵelementStart(58, \"mat-card\", 23)(59, \"mat-card-header\")(60, \"mat-card-title\")(61, \"mat-icon\");\n        i0.ɵɵtext(62, \"help\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(63, \" Testing Guide \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(64, \"mat-card-content\")(65, \"div\", 24)(66, \"h4\");\n        i0.ɵɵtext(67, \"Test Card Numbers\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"div\", 25)(69, \"div\", 26)(70, \"strong\");\n        i0.ɵɵtext(71, \"Visa:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(72, \" ************** 1111 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(73, \"div\", 26)(74, \"strong\");\n        i0.ɵɵtext(75, \"Mastercard:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(76, \" ************** 4444 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(77, \"p\")(78, \"strong\");\n        i0.ɵɵtext(79, \"CVV:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(80, \" Any 3-4 digit number | \");\n        i0.ɵɵelementStart(81, \"strong\");\n        i0.ɵɵtext(82, \"Expiry:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(83, \" Any future date\");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        let tmp_3_0;\n        i0.ɵɵadvance(19);\n        i0.ɵɵproperty(\"ngForOf\", ctx.testAmounts);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.paymentForm);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngForOf\", ctx.supportedCurrencies);\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(((tmp_3_0 = ctx.paymentForm.get(\"currency\")) == null ? null : tmp_3_0.value) === \"INR\" ? \"\\u20B9\" : \"$\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"amount\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.getEquivalentAmount());\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"disabled\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", ctx.payments.length > 0);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.MinValidator, i1.FormGroupDirective, i1.FormControlName, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatFormField, i7.MatLabel, i7.MatHint, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i10.MatIcon, i11.MatSelect, i11.MatOption, i12.MatProgressSpinner, i13.MatDivider, i5.TitleCasePipe, i5.DatePipe],\n    styles: [\".my-3[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.form-row[_ngcontent-%COMP%]   .currency-field[_ngcontent-%COMP%] {\\n  flex: 0 0 120px;\\n}\\n.form-row[_ngcontent-%COMP%]   .amount-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.test-amounts[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.test-amounts[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  color: #333;\\n}\\n.test-amounts[_ngcontent-%COMP%]   .amount-buttons[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 0.5rem;\\n}\\n.test-amounts[_ngcontent-%COMP%]   .amount-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  font-size: 0.875rem;\\n}\\n\\n.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 0.75rem;\\n}\\n.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n  color: #666;\\n}\\n.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%]   .test-card[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 0.75rem;\\n  border-radius: 6px;\\n  font-family: monospace;\\n  font-size: 0.875rem;\\n}\\n.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%]   .test-card[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n@media (max-width: 768px) {\\n  .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n  .form-row[_ngcontent-%COMP%]   .currency-field[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n  .amount-buttons[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .test-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "environment", "i0", "ɵɵelementStart", "ɵɵlistener", "PaymentTestComponent_button_19_Template_button_click_0_listener", "testAmount_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "selectTestAmount", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tmp_2_0", "paymentForm", "get", "value", "currency", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "currency_r4", "ɵɵtextInterpolate", "getEquivalentAmount", "ɵɵelement", "formatCurrency", "payment_r5", "amount", "description", "ɵɵpipeBind2", "createdAt", "id", "status", "getStatusIcon", "ɵɵpipeBind1", "ɵɵtemplate", "PaymentTestComponent_mat_card_57_div_10_Template", "payments", "PaymentTestComponent", "constructor", "formBuilder", "paymentService", "authService", "snackBar", "loading", "supportedCurrencies", "razorpay", "testAmounts", "group", "required", "min", "max", "ngOnInit", "loadPaymentHistory", "onSubmit", "invalid", "markFormGroupTouched", "paymentRequest", "validation", "validateAmount", "valid", "open", "error", "duration", "processPayment", "subscribe", "next", "response", "success", "patchValue", "message", "testAmount", "getMyPayments", "console", "getFieldError", "fieldName", "field", "errors", "touched", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "convertAmount", "fromCurrency", "to<PERSON><PERSON><PERSON><PERSON>", "convertCurrency", "otherCurrency", "convertedAmount", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "PaymentService", "i3", "AuthService", "i4", "MatSnackBar", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "PaymentTestComponent_Template", "rf", "ctx", "PaymentTestComponent_button_19_Template", "PaymentTestComponent_Template_form_ngSubmit_21_listener", "PaymentTestComponent_mat_option_27_Template", "PaymentTestComponent_mat_hint_38_Template", "PaymentTestComponent_mat_spinner_46_Template", "PaymentTestComponent_mat_icon_47_Template", "PaymentTestComponent_span_48_Template", "PaymentTestComponent_mat_card_57_Template", "tmp_3_0", "length"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\payment\\payment-test\\payment-test.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\payment\\payment-test\\payment-test.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { PaymentService } from '../../../services/payment.service';\nimport { AuthService } from '../../../services/auth.service';\nimport { Payment, PaymentRequest } from '../../../models/payment.model';\nimport { environment } from '../../../../environments/environment';\n\n@Component({\n  selector: 'app-payment-test',\n  templateUrl: './payment-test.component.html',\n  styleUrls: ['./payment-test.component.scss'],\n  standalone: false\n})\nexport class PaymentTestComponent implements OnInit {\n  paymentForm: FormGroup;\n  loading = false;\n  payments: Payment[] = [];\n  supportedCurrencies = environment.razorpay.supportedCurrencies;\n  \n  testAmounts = [\n    { amount: 100, currency: 'INR', label: '₹100 - Basic Test' },\n    { amount: 500, currency: 'INR', label: '₹500 - Standard Test' },\n    { amount: 1000, currency: 'INR', label: '₹1,000 - Premium Test' },\n    { amount: 5, currency: 'USD', label: '$5 - Basic Test' },\n    { amount: 25, currency: 'USD', label: '$25 - Standard Test' },\n    { amount: 50, currency: 'USD', label: '$50 - Premium Test' }\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private paymentService: PaymentService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar\n  ) {\n    this.paymentForm = this.formBuilder.group({\n      amount: ['', [Validators.required, Validators.min(1), Validators.max(1000000)]],\n      currency: [environment.razorpay.currency, [Validators.required]],\n      description: ['Payment test transaction']\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadPaymentHistory();\n  }\n\n  onSubmit(): void {\n    if (this.paymentForm.invalid) {\n      this.markFormGroupTouched(this.paymentForm);\n      return;\n    }\n\n    const paymentRequest: PaymentRequest = this.paymentForm.value;\n    \n    const validation = this.paymentService.validateAmount(paymentRequest.amount, paymentRequest.currency);\n    if (!validation.valid) {\n      this.snackBar.open(validation.error!, 'Close', { duration: 5000 });\n      return;\n    }\n\n    this.loading = true;\n\n    this.paymentService.processPayment(paymentRequest).subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.snackBar.open('Payment completed successfully!', 'Close', { duration: 5000 });\n          this.loadPaymentHistory();\n          this.paymentForm.patchValue({ amount: '', description: 'Payment test transaction' });\n        } else {\n          this.snackBar.open(response.message || 'Payment failed', 'Close', { duration: 5000 });\n        }\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Payment processing failed', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  selectTestAmount(testAmount: any): void {\n    this.paymentForm.patchValue({\n      amount: testAmount.amount,\n      currency: testAmount.currency,\n      description: `Test payment - ${testAmount.label}`\n    });\n  }\n\n  loadPaymentHistory(): void {\n    this.paymentService.getMyPayments().subscribe({\n      next: (response) => {\n        this.payments = response.payments;\n      },\n      error: (error) => {\n        console.error('Failed to load payment history:', error);\n      }\n    });\n  }\n\n  formatCurrency(amount: number, currency: string): string {\n    return this.paymentService.formatCurrency(amount, currency);\n  }\n\n  getStatusIcon(status: string): string {\n    return this.paymentService.getStatusIcon(status);\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.paymentForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['min']) return `Minimum amount is ${field.errors['min'].min}`;\n      if (field.errors['max']) return `Maximum amount is ${field.errors['max'].max}`;\n    }\n    return '';\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  convertAmount(amount: number, fromCurrency: string, toCurrency: string): number {\n    return this.paymentService.convertCurrency(amount, fromCurrency, toCurrency);\n  }\n\n  getEquivalentAmount(): string {\n    const amount = this.paymentForm.get('amount')?.value;\n    const currency = this.paymentForm.get('currency')?.value;\n    \n    if (!amount || !currency) return '';\n\n    const otherCurrency = currency === 'INR' ? 'USD' : 'INR';\n    const convertedAmount = this.convertAmount(amount, currency, otherCurrency);\n    \n    return `≈ ${this.formatCurrency(convertedAmount, otherCurrency)}`;\n  }\n}\n", "<div class=\"payment-container\">\n  <div class=\"container\">\n    <h1>Payment Testing Interface</h1>\n    <p class=\"subtitle\">Test Razorpay integration with secure payment processing</p>\n\n    <!-- Payment Form -->\n    <mat-card class=\"payment-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>payment</mat-icon>\n          Make a Test Payment\n        </mat-card-title>\n        <mat-card-subtitle>\n          Test payments using Razorpay's secure checkout\n        </mat-card-subtitle>\n      </mat-card-header>\n\n      <mat-card-content>\n        <!-- Quick Test Amounts -->\n        <div class=\"test-amounts\">\n          <h3>Quick Test Amounts</h3>\n          <div class=\"amount-buttons\">\n            <button *ngFor=\"let testAmount of testAmounts\" \n                    mat-stroked-button \n                    (click)=\"selectTestAmount(testAmount)\"\n                    [color]=\"paymentForm.get('currency')?.value === testAmount.currency ? 'primary' : ''\">\n              {{ testAmount.label }}\n            </button>\n          </div>\n        </div>\n\n        <mat-divider class=\"my-3\"></mat-divider>\n\n        <!-- Custom Payment Form -->\n        <form [formGroup]=\"paymentForm\" (ngSubmit)=\"onSubmit()\">\n          <div class=\"form-row\">\n            <mat-form-field class=\"currency-field\" appearance=\"outline\">\n              <mat-label>Currency</mat-label>\n              <mat-select formControlName=\"currency\">\n                <mat-option *ngFor=\"let currency of supportedCurrencies\" [value]=\"currency\">\n                  {{ currency }}\n                </mat-option>\n              </mat-select>\n              <mat-icon matSuffix>attach_money</mat-icon>\n            </mat-form-field>\n\n            <mat-form-field class=\"amount-field\" appearance=\"outline\">\n              <mat-label>Amount</mat-label>\n              <input matInput type=\"number\" formControlName=\"amount\" min=\"1\" step=\"0.01\">\n              <span matSuffix>{{ paymentForm.get('currency')?.value === 'INR' ? '₹' : '$' }}</span>\n              <mat-error>{{ getFieldError('amount') }}</mat-error>\n              <mat-hint *ngIf=\"getEquivalentAmount()\">{{ getEquivalentAmount() }}</mat-hint>\n            </mat-form-field>\n          </div>\n\n          <mat-form-field class=\"form-field\" appearance=\"outline\">\n            <mat-label>Description (Optional)</mat-label>\n            <input matInput formControlName=\"description\" maxlength=\"100\">\n            <mat-icon matSuffix>description</mat-icon>\n          </mat-form-field>\n\n          <button mat-raised-button color=\"primary\" type=\"submit\" class=\"payment-button\" [disabled]=\"loading\">\n            <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n            <mat-icon *ngIf=\"!loading\">lock</mat-icon>\n            <span *ngIf=\"!loading\">Pay Securely with Razorpay</span>\n          </button>\n        </form>\n\n        <!-- Security Notice -->\n        <div class=\"security-notice\">\n          <mat-icon>security</mat-icon>\n          <div>\n            <strong>Secure Payment Processing</strong>\n            <p>All payments are processed securely through Razorpay's PCI DSS compliant platform.</p>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Payment History -->\n    <mat-card class=\"payment-history\" *ngIf=\"payments.length > 0\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>history</mat-icon>\n          Payment History\n        </mat-card-title>\n        <mat-card-subtitle>\n          Your recent test transactions\n        </mat-card-subtitle>\n      </mat-card-header>\n\n      <mat-card-content>\n        <div class=\"payment-list\">\n          <div *ngFor=\"let payment of payments\" class=\"payment-item\">\n            <div class=\"payment-info\">\n              <div class=\"payment-amount\">\n                {{ formatCurrency(payment.amount, payment.currency) }}\n              </div>\n              <div class=\"payment-details\">\n                <div class=\"payment-description\">{{ payment.description || 'Test Payment' }}</div>\n                <div class=\"payment-date\">{{ payment.createdAt | date:'medium' }}</div>\n                <div class=\"payment-id\">ID: {{ payment.id }}</div>\n              </div>\n            </div>\n            \n            <div class=\"payment-actions\">\n              <div class=\"payment-status\" [ngClass]=\"'status-' + payment.status\">\n                <mat-icon>{{ getStatusIcon(payment.status) }}</mat-icon>\n                {{ payment.status | titlecase }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Testing Guide -->\n    <mat-card class=\"testing-guide\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>help</mat-icon>\n          Testing Guide\n        </mat-card-title>\n      </mat-card-header>\n\n      <mat-card-content>\n        <div class=\"guide-section\">\n          <h4>Test Card Numbers</h4>\n          <div class=\"test-cards\">\n            <div class=\"test-card\">\n              <strong>Visa:</strong> ************** 1111\n            </div>\n            <div class=\"test-card\">\n              <strong>Mastercard:</strong> ************** 4444\n            </div>\n          </div>\n          <p><strong>CVV:</strong> Any 3-4 digit number | <strong>Expiry:</strong> Any future date</p>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,SAASC,WAAW,QAAQ,sCAAsC;;;;;;;;;;;;;;;;;;ICgBtDC,EAAA,CAAAC,cAAA,iBAG8F;IADtFD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAA,MAAAC,aAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,gBAAA,CAAAP,aAAA,CAA4B;IAAA,EAAC;IAE5CJ,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAFDb,EAAA,CAAAc,UAAA,YAAAC,OAAA,GAAAP,MAAA,CAAAQ,WAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,KAAA,MAAAd,aAAA,CAAAe,QAAA,kBAAqF;IAC3FnB,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAqB,kBAAA,MAAAjB,aAAA,CAAAkB,KAAA,MACF;;;;;IAYItB,EAAA,CAAAC,cAAA,qBAA4E;IAC1ED,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAa;;;;IAF4Cb,EAAA,CAAAc,UAAA,UAAAS,WAAA,CAAkB;IACzEvB,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAqB,kBAAA,MAAAE,WAAA,MACF;;;;;IAUFvB,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAY,MAAA,GAA2B;IAAAZ,EAAA,CAAAa,YAAA,EAAW;;;;IAAtCb,EAAA,CAAAoB,SAAA,EAA2B;IAA3BpB,EAAA,CAAAwB,iBAAA,CAAAhB,MAAA,CAAAiB,mBAAA,GAA2B;;;;;IAWrEzB,EAAA,CAAA0B,SAAA,sBAAyD;;;;;IACzD1B,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAY,MAAA,WAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAW;;;;;IAC1Cb,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAY,MAAA,iCAA0B;IAAAZ,EAAA,CAAAa,YAAA,EAAO;;;;;IA+BtDb,EAFJ,CAAAC,cAAA,cAA2D,cAC/B,cACI;IAC1BD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,cAA6B,cACM;IAAAD,EAAA,CAAAY,MAAA,GAA2C;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAClFb,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAY,MAAA,GAAuC;;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IACvEb,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAY,MAAA,IAAoB;IAEhDZ,EAFgD,CAAAa,YAAA,EAAM,EAC9C,EACF;IAIFb,EAFJ,CAAAC,cAAA,eAA6B,eACwC,gBACvD;IAAAD,EAAA,CAAAY,MAAA,IAAmC;IAAAZ,EAAA,CAAAa,YAAA,EAAW;IACxDb,EAAA,CAAAY,MAAA,IACF;;IAEJZ,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;;;;;IAfAb,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAqB,kBAAA,MAAAb,MAAA,CAAAmB,cAAA,CAAAC,UAAA,CAAAC,MAAA,EAAAD,UAAA,CAAAT,QAAA,OACF;IAEmCnB,EAAA,CAAAoB,SAAA,GAA2C;IAA3CpB,EAAA,CAAAwB,iBAAA,CAAAI,UAAA,CAAAE,WAAA,mBAA2C;IAClD9B,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAAwB,iBAAA,CAAAxB,EAAA,CAAA+B,WAAA,OAAAH,UAAA,CAAAI,SAAA,YAAuC;IACzChC,EAAA,CAAAoB,SAAA,GAAoB;IAApBpB,EAAA,CAAAqB,kBAAA,SAAAO,UAAA,CAAAK,EAAA,CAAoB;IAKlBjC,EAAA,CAAAoB,SAAA,GAAsC;IAAtCpB,EAAA,CAAAc,UAAA,wBAAAc,UAAA,CAAAM,MAAA,CAAsC;IACtDlC,EAAA,CAAAoB,SAAA,GAAmC;IAAnCpB,EAAA,CAAAwB,iBAAA,CAAAhB,MAAA,CAAA2B,aAAA,CAAAP,UAAA,CAAAM,MAAA,EAAmC;IAC7ClC,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAoC,WAAA,SAAAR,UAAA,CAAAM,MAAA,OACF;;;;;IA1BJlC,EAHN,CAAAC,cAAA,mBAA8D,sBAC3C,qBACC,eACJ;IAAAD,EAAA,CAAAY,MAAA,cAAO;IAAAZ,EAAA,CAAAa,YAAA,EAAW;IAC5Bb,EAAA,CAAAY,MAAA,wBACF;IAAAZ,EAAA,CAAAa,YAAA,EAAiB;IACjBb,EAAA,CAAAC,cAAA,wBAAmB;IACjBD,EAAA,CAAAY,MAAA,sCACF;IACFZ,EADE,CAAAa,YAAA,EAAoB,EACJ;IAGhBb,EADF,CAAAC,cAAA,uBAAkB,cACU;IACxBD,EAAA,CAAAqC,UAAA,KAAAC,gDAAA,oBAA2D;IAqBjEtC,EAFI,CAAAa,YAAA,EAAM,EACW,EACV;;;;IArBoBb,EAAA,CAAAoB,SAAA,IAAW;IAAXpB,EAAA,CAAAc,UAAA,YAAAN,MAAA,CAAA+B,QAAA,CAAW;;;AD/E9C,OAAM,MAAOC,oBAAoB;EAe/BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB;IAHrB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAjBlB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAP,QAAQ,GAAc,EAAE;IACxB,KAAAQ,mBAAmB,GAAGhD,WAAW,CAACiD,QAAQ,CAACD,mBAAmB;IAE9D,KAAAE,WAAW,GAAG,CACZ;MAAEpB,MAAM,EAAE,GAAG;MAAEV,QAAQ,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAmB,CAAE,EAC5D;MAAEO,MAAM,EAAE,GAAG;MAAEV,QAAQ,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAsB,CAAE,EAC/D;MAAEO,MAAM,EAAE,IAAI;MAAEV,QAAQ,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAuB,CAAE,EACjE;MAAEO,MAAM,EAAE,CAAC;MAAEV,QAAQ,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAiB,CAAE,EACxD;MAAEO,MAAM,EAAE,EAAE;MAAEV,QAAQ,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAqB,CAAE,EAC7D;MAAEO,MAAM,EAAE,EAAE;MAAEV,QAAQ,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAoB,CAAE,CAC7D;IAQC,IAAI,CAACN,WAAW,GAAG,IAAI,CAAC0B,WAAW,CAACQ,KAAK,CAAC;MACxCrB,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC/B,UAAU,CAACqD,QAAQ,EAAErD,UAAU,CAACsD,GAAG,CAAC,CAAC,CAAC,EAAEtD,UAAU,CAACuD,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;MAC/ElC,QAAQ,EAAE,CAACpB,WAAW,CAACiD,QAAQ,CAAC7B,QAAQ,EAAE,CAACrB,UAAU,CAACqD,QAAQ,CAAC,CAAC;MAChErB,WAAW,EAAE,CAAC,0BAA0B;KACzC,CAAC;EACJ;EAEAwB,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACxC,WAAW,CAACyC,OAAO,EAAE;MAC5B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC1C,WAAW,CAAC;MAC3C;IACF;IAEA,MAAM2C,cAAc,GAAmB,IAAI,CAAC3C,WAAW,CAACE,KAAK;IAE7D,MAAM0C,UAAU,GAAG,IAAI,CAACjB,cAAc,CAACkB,cAAc,CAACF,cAAc,CAAC9B,MAAM,EAAE8B,cAAc,CAACxC,QAAQ,CAAC;IACrG,IAAI,CAACyC,UAAU,CAACE,KAAK,EAAE;MACrB,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAACH,UAAU,CAACI,KAAM,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAClE;IACF;IAEA,IAAI,CAACnB,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACH,cAAc,CAACuB,cAAc,CAACP,cAAc,CAAC,CAACQ,SAAS,CAAC;MAC3DC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACzB,QAAQ,CAACkB,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;YAAEE,QAAQ,EAAE;UAAI,CAAE,CAAC;UAClF,IAAI,CAACV,kBAAkB,EAAE;UACzB,IAAI,CAACvC,WAAW,CAACuD,UAAU,CAAC;YAAE1C,MAAM,EAAE,EAAE;YAAEC,WAAW,EAAE;UAA0B,CAAE,CAAC;QACtF,CAAC,MAAM;UACL,IAAI,CAACe,QAAQ,CAACkB,IAAI,CAACM,QAAQ,CAACG,OAAO,IAAI,gBAAgB,EAAE,OAAO,EAAE;YAAEP,QAAQ,EAAE;UAAI,CAAE,CAAC;QACvF;QACA,IAAI,CAACnB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDkB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnB,QAAQ,CAACkB,IAAI,CAACC,KAAK,CAACQ,OAAO,IAAI,2BAA2B,EAAE,OAAO,EAAE;UAAEP,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC7F,IAAI,CAACnB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAnC,gBAAgBA,CAAC8D,UAAe;IAC9B,IAAI,CAACzD,WAAW,CAACuD,UAAU,CAAC;MAC1B1C,MAAM,EAAE4C,UAAU,CAAC5C,MAAM;MACzBV,QAAQ,EAAEsD,UAAU,CAACtD,QAAQ;MAC7BW,WAAW,EAAE,kBAAkB2C,UAAU,CAACnD,KAAK;KAChD,CAAC;EACJ;EAEAiC,kBAAkBA,CAAA;IAChB,IAAI,CAACZ,cAAc,CAAC+B,aAAa,EAAE,CAACP,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC9B,QAAQ,GAAG8B,QAAQ,CAAC9B,QAAQ;MACnC,CAAC;MACDyB,KAAK,EAAGA,KAAK,IAAI;QACfW,OAAO,CAACX,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;KACD,CAAC;EACJ;EAEArC,cAAcA,CAACE,MAAc,EAAEV,QAAgB;IAC7C,OAAO,IAAI,CAACwB,cAAc,CAAChB,cAAc,CAACE,MAAM,EAAEV,QAAQ,CAAC;EAC7D;EAEAgB,aAAaA,CAACD,MAAc;IAC1B,OAAO,IAAI,CAACS,cAAc,CAACR,aAAa,CAACD,MAAM,CAAC;EAClD;EAEA0C,aAAaA,CAACC,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC9D,WAAW,CAACC,GAAG,CAAC4D,SAAS,CAAC;IAC7C,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGF,SAAS,cAAc;MAC/D,IAAIC,KAAK,CAACC,MAAM,CAAC,KAAK,CAAC,EAAE,OAAO,qBAAqBD,KAAK,CAACC,MAAM,CAAC,KAAK,CAAC,CAAC3B,GAAG,EAAE;MAC9E,IAAI0B,KAAK,CAACC,MAAM,CAAC,KAAK,CAAC,EAAE,OAAO,qBAAqBD,KAAK,CAACC,MAAM,CAAC,KAAK,CAAC,CAAC1B,GAAG,EAAE;IAChF;IACA,OAAO,EAAE;EACX;EAEQK,oBAAoBA,CAACuB,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAAChE,GAAG,CAACqE,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAAC5D,MAAc,EAAE6D,YAAoB,EAAEC,UAAkB;IACpE,OAAO,IAAI,CAAChD,cAAc,CAACiD,eAAe,CAAC/D,MAAM,EAAE6D,YAAY,EAAEC,UAAU,CAAC;EAC9E;EAEAlE,mBAAmBA,CAAA;IACjB,MAAMI,MAAM,GAAG,IAAI,CAACb,WAAW,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAEC,KAAK;IACpD,MAAMC,QAAQ,GAAG,IAAI,CAACH,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK;IAExD,IAAI,CAACW,MAAM,IAAI,CAACV,QAAQ,EAAE,OAAO,EAAE;IAEnC,MAAM0E,aAAa,GAAG1E,QAAQ,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;IACxD,MAAM2E,eAAe,GAAG,IAAI,CAACL,aAAa,CAAC5D,MAAM,EAAEV,QAAQ,EAAE0E,aAAa,CAAC;IAE3E,OAAO,KAAK,IAAI,CAAClE,cAAc,CAACmE,eAAe,EAAED,aAAa,CAAC,EAAE;EACnE;EAAC,QAAAE,CAAA,G;qCA5HUvD,oBAAoB,EAAAxC,EAAA,CAAAgG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlG,EAAA,CAAAgG,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAApG,EAAA,CAAAgG,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAtG,EAAA,CAAAgG,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApBjE,oBAAoB;IAAAkE,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ7BjH,EAFJ,CAAAC,cAAA,aAA+B,aACN,SACjB;QAAAD,EAAA,CAAAY,MAAA,gCAAyB;QAAAZ,EAAA,CAAAa,YAAA,EAAK;QAClCb,EAAA,CAAAC,cAAA,WAAoB;QAAAD,EAAA,CAAAY,MAAA,+DAAwD;QAAAZ,EAAA,CAAAa,YAAA,EAAI;QAM1Eb,EAHN,CAAAC,cAAA,kBAA+B,sBACZ,qBACC,eACJ;QAAAD,EAAA,CAAAY,MAAA,eAAO;QAAAZ,EAAA,CAAAa,YAAA,EAAW;QAC5Bb,EAAA,CAAAY,MAAA,6BACF;QAAAZ,EAAA,CAAAa,YAAA,EAAiB;QACjBb,EAAA,CAAAC,cAAA,yBAAmB;QACjBD,EAAA,CAAAY,MAAA,wDACF;QACFZ,EADE,CAAAa,YAAA,EAAoB,EACJ;QAKdb,EAHJ,CAAAC,cAAA,wBAAkB,cAEU,UACpB;QAAAD,EAAA,CAAAY,MAAA,0BAAkB;QAAAZ,EAAA,CAAAa,YAAA,EAAK;QAC3Bb,EAAA,CAAAC,cAAA,cAA4B;QAC1BD,EAAA,CAAAqC,UAAA,KAAA8E,uCAAA,oBAG8F;QAIlGnH,EADE,CAAAa,YAAA,EAAM,EACF;QAENb,EAAA,CAAA0B,SAAA,sBAAwC;QAGxC1B,EAAA,CAAAC,cAAA,eAAwD;QAAxBD,EAAA,CAAAE,UAAA,sBAAAkH,wDAAA;UAAA,OAAYF,GAAA,CAAA1D,QAAA,EAAU;QAAA,EAAC;QAGjDxD,EAFJ,CAAAC,cAAA,cAAsB,0BACwC,iBAC/C;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAa,YAAA,EAAY;QAC/Bb,EAAA,CAAAC,cAAA,sBAAuC;QACrCD,EAAA,CAAAqC,UAAA,KAAAgF,2CAAA,yBAA4E;QAG9ErH,EAAA,CAAAa,YAAA,EAAa;QACbb,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAY,MAAA,oBAAY;QAClCZ,EADkC,CAAAa,YAAA,EAAW,EAC5B;QAGfb,EADF,CAAAC,cAAA,0BAA0D,iBAC7C;QAAAD,EAAA,CAAAY,MAAA,cAAM;QAAAZ,EAAA,CAAAa,YAAA,EAAY;QAC7Bb,EAAA,CAAA0B,SAAA,iBAA2E;QAC3E1B,EAAA,CAAAC,cAAA,gBAAgB;QAAAD,EAAA,CAAAY,MAAA,IAA8D;QAAAZ,EAAA,CAAAa,YAAA,EAAO;QACrFb,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAY,MAAA,IAA6B;QAAAZ,EAAA,CAAAa,YAAA,EAAY;QACpDb,EAAA,CAAAqC,UAAA,KAAAiF,yCAAA,uBAAwC;QAE5CtH,EADE,CAAAa,YAAA,EAAiB,EACb;QAGJb,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;QAAAD,EAAA,CAAAY,MAAA,8BAAsB;QAAAZ,EAAA,CAAAa,YAAA,EAAY;QAC7Cb,EAAA,CAAA0B,SAAA,iBAA8D;QAC9D1B,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAY,MAAA,mBAAW;QACjCZ,EADiC,CAAAa,YAAA,EAAW,EAC3B;QAEjBb,EAAA,CAAAC,cAAA,kBAAoG;QAGlGD,EAFA,CAAAqC,UAAA,KAAAkF,4CAAA,0BAA2C,KAAAC,yCAAA,uBAChB,KAAAC,qCAAA,mBACJ;QAE3BzH,EADE,CAAAa,YAAA,EAAS,EACJ;QAILb,EADF,CAAAC,cAAA,eAA6B,gBACjB;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAa,YAAA,EAAW;QAE3Bb,EADF,CAAAC,cAAA,WAAK,cACK;QAAAD,EAAA,CAAAY,MAAA,iCAAyB;QAAAZ,EAAA,CAAAa,YAAA,EAAS;QAC1Cb,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAY,MAAA,0FAAkF;QAI7FZ,EAJ6F,CAAAa,YAAA,EAAI,EACrF,EACF,EACW,EACV;QAGXb,EAAA,CAAAqC,UAAA,KAAAqF,yCAAA,wBAA8D;QAwCxD1H,EAHN,CAAAC,cAAA,oBAAgC,uBACb,sBACC,gBACJ;QAAAD,EAAA,CAAAY,MAAA,YAAI;QAAAZ,EAAA,CAAAa,YAAA,EAAW;QACzBb,EAAA,CAAAY,MAAA,uBACF;QACFZ,EADE,CAAAa,YAAA,EAAiB,EACD;QAIdb,EAFJ,CAAAC,cAAA,wBAAkB,eACW,UACrB;QAAAD,EAAA,CAAAY,MAAA,yBAAiB;QAAAZ,EAAA,CAAAa,YAAA,EAAK;QAGtBb,EAFJ,CAAAC,cAAA,eAAwB,eACC,cACb;QAAAD,EAAA,CAAAY,MAAA,aAAK;QAAAZ,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAY,MAAA,6BACzB;QAAAZ,EAAA,CAAAa,YAAA,EAAM;QAEJb,EADF,CAAAC,cAAA,eAAuB,cACb;QAAAD,EAAA,CAAAY,MAAA,mBAAW;QAAAZ,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAY,MAAA,6BAC/B;QACFZ,EADE,CAAAa,YAAA,EAAM,EACF;QACHb,EAAH,CAAAC,cAAA,SAAG,cAAQ;QAAAD,EAAA,CAAAY,MAAA,YAAI;QAAAZ,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAY,MAAA,gCAAuB;QAAAZ,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAY,MAAA,eAAO;QAAAZ,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAY,MAAA,wBAAe;QAKlGZ,EALkG,CAAAa,YAAA,EAAI,EACxF,EACW,EACV,EACP,EACF;;;;QAvHqCb,EAAA,CAAAoB,SAAA,IAAc;QAAdpB,EAAA,CAAAc,UAAA,YAAAoG,GAAA,CAAAjE,WAAA,CAAc;QAY3CjD,EAAA,CAAAoB,SAAA,GAAyB;QAAzBpB,EAAA,CAAAc,UAAA,cAAAoG,GAAA,CAAAlG,WAAA,CAAyB;QAKUhB,EAAA,CAAAoB,SAAA,GAAsB;QAAtBpB,EAAA,CAAAc,UAAA,YAAAoG,GAAA,CAAAnE,mBAAA,CAAsB;QAUzC/C,EAAA,CAAAoB,SAAA,GAA8D;QAA9DpB,EAAA,CAAAwB,iBAAA,GAAAmG,OAAA,GAAAT,GAAA,CAAAlG,WAAA,CAAAC,GAAA,+BAAA0G,OAAA,CAAAzG,KAAA,6BAA8D;QACnElB,EAAA,CAAAoB,SAAA,GAA6B;QAA7BpB,EAAA,CAAAwB,iBAAA,CAAA0F,GAAA,CAAAtC,aAAA,WAA6B;QAC7B5E,EAAA,CAAAoB,SAAA,EAA2B;QAA3BpB,EAAA,CAAAc,UAAA,SAAAoG,GAAA,CAAAzF,mBAAA,GAA2B;QAUqCzB,EAAA,CAAAoB,SAAA,GAAoB;QAApBpB,EAAA,CAAAc,UAAA,aAAAoG,GAAA,CAAApE,OAAA,CAAoB;QACnF9C,EAAA,CAAAoB,SAAA,EAAa;QAAbpB,EAAA,CAAAc,UAAA,SAAAoG,GAAA,CAAApE,OAAA,CAAa;QAChB9C,EAAA,CAAAoB,SAAA,EAAc;QAAdpB,EAAA,CAAAc,UAAA,UAAAoG,GAAA,CAAApE,OAAA,CAAc;QAClB9C,EAAA,CAAAoB,SAAA,EAAc;QAAdpB,EAAA,CAAAc,UAAA,UAAAoG,GAAA,CAAApE,OAAA,CAAc;QAgBM9C,EAAA,CAAAoB,SAAA,GAAyB;QAAzBpB,EAAA,CAAAc,UAAA,SAAAoG,GAAA,CAAA3E,QAAA,CAAAqF,MAAA,KAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}