{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from '@guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/auth/login',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./modules/auth/auth.module').then(m => m.AuthModule)\n}, {\n  path: 'dashboard',\n  loadChildren: () => import('./modules/dashboard/dashboard.module').then(m => m.DashboardModule),\n  canActivate: [AuthGuard],\n  data: {\n    requireEmailVerification: true\n  }\n}, {\n  path: 'payment',\n  loadChildren: () => import('./modules/payment/payment.module').then(m => m.PaymentModule),\n  canActivate: [AuthGuard],\n  data: {\n    requireEmailVerification: true\n  }\n}, {\n  path: 'profile',\n  loadChildren: () => import('./modules/profile/profile.module').then(m => m.ProfileModule),\n  canActivate: [AuthGuard],\n  data: {\n    requireEmailVerification: true\n  }\n}, {\n  path: 'unauthorized',\n  loadChildren: () => import('./modules/shared/shared.module').then(m => m.SharedModule)\n}, {\n  path: '**',\n  redirectTo: '/auth/login'\n}];\nexport class AppRoutingModule {\n  static #_ = this.ɵfac = function AppRoutingModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AppRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forRoot(routes, {\n      enableTracing: false,\n      // Set to true for debugging\n      scrollPositionRestoration: 'top'\n    }), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "AuthModule", "DashboardModule", "canActivate", "data", "requireEmailVerification", "PaymentModule", "ProfileModule", "SharedModule", "AppRoutingModule", "_", "_2", "_3", "forRoot", "enableTracing", "scrollPositionRestoration", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AuthGuard } from '@guards/auth.guard';\n\nconst routes: Routes = [\n  {\n    path: '',\n    redirectTo: '/auth/login',\n    pathMatch: 'full'\n  },\n  {\n    path: 'auth',\n    loadChildren: () => import('./modules/auth/auth.module').then(m => m.AuthModule)\n  },\n  {\n    path: 'dashboard',\n    loadChildren: () => import('./modules/dashboard/dashboard.module').then(m => m.DashboardModule),\n    canActivate: [AuthGuard],\n    data: { requireEmailVerification: true }\n  },\n  {\n    path: 'payment',\n    loadChildren: () => import('./modules/payment/payment.module').then(m => m.PaymentModule),\n    canActivate: [AuthGuard],\n    data: { requireEmailVerification: true }\n  },\n  {\n    path: 'profile',\n    loadChildren: () => import('./modules/profile/profile.module').then(m => m.ProfileModule),\n    canActivate: [AuthGuard],\n    data: { requireEmailVerification: true }\n  },\n  {\n    path: 'unauthorized',\n    loadChildren: () => import('./modules/shared/shared.module').then(m => m.SharedModule)\n  },\n  {\n    path: '**',\n    redirectTo: '/auth/login'\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes, {\n    enableTracing: false, // Set to true for debugging\n    scrollPositionRestoration: 'top'\n  })],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,SAAS,QAAQ,oBAAoB;;;AAE9C,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,aAAa;EACzBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;CAChF,EACD;EACEN,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,eAAe,CAAC;EAC/FC,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBW,IAAI,EAAE;IAAEC,wBAAwB,EAAE;EAAI;CACvC,EACD;EACEV,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,aAAa,CAAC;EACzFH,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBW,IAAI,EAAE;IAAEC,wBAAwB,EAAE;EAAI;CACvC,EACD;EACEV,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,aAAa,CAAC;EACzFJ,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBW,IAAI,EAAE;IAAEC,wBAAwB,EAAE;EAAI;CACvC,EACD;EACEV,IAAI,EAAE,cAAc;EACpBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,YAAY;CACtF,EACD;EACEb,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF;AASD,OAAM,MAAOa,gBAAgB;EAAA,QAAAC,CAAA,G;qCAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA,G;UAAhBF;EAAgB;EAAA,QAAAG,EAAA,G;cANjBpB,YAAY,CAACqB,OAAO,CAACnB,MAAM,EAAE;MACrCoB,aAAa,EAAE,KAAK;MAAE;MACtBC,yBAAyB,EAAE;KAC5B,CAAC,EACQvB,YAAY;EAAA;;;2EAEXiB,gBAAgB;IAAAO,OAAA,GAAAC,EAAA,CAAAzB,YAAA;IAAA0B,OAAA,GAFjB1B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}