#!/usr/bin/env node

/**
 * Signup API Detailed Testing Script
 */

const axios = require('axios');

async function testSignupAPI() {
  console.log('🔍 TESTING SIGNUP API IN DETAIL');
  console.log('=================================\n');

  try {
    // Test 1: Basic signup
    console.log('🔍 Test 1: Basic signup...');
    const testEmail = `test-api-${Date.now()}@example.com`;
    
    try {
      const signupResponse = await axios.post('http://localhost:3002/auth/signup', {
        email: testEmail,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'User',
        phone: '+1234567890'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'http://localhost:4200'
        },
        timeout: 10000
      });
      
      console.log('✅ Signup successful!');
      console.log('   Status:', signupResponse.status);
      console.log('   Response:', signupResponse.data);
      
    } catch (signupError) {
      console.log('❌ Signup failed');
      console.log('   Status:', signupError.response?.status);
      console.log('   Status Text:', signupError.response?.statusText);
      console.log('   Error Data:', signupError.response?.data);
      console.log('   Error Message:', signupError.message);
      
      if (signupError.response?.data?.error?.details) {
        console.log('   Error Details:', signupError.response.data.error.details);
      }
    }

    // Test 2: Signup with minimal data
    console.log('\n🔍 Test 2: Signup with minimal data...');
    const testEmail2 = `minimal-${Date.now()}@example.com`;
    
    try {
      const minimalSignupResponse = await axios.post('http://localhost:3002/auth/signup', {
        email: testEmail2,
        password: 'MinimalPass123!',
        firstName: 'Min',
        lastName: 'User'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'http://localhost:4200'
        },
        timeout: 10000
      });
      
      console.log('✅ Minimal signup successful!');
      console.log('   Response:', minimalSignupResponse.data);
      
    } catch (minimalError) {
      console.log('❌ Minimal signup failed');
      console.log('   Status:', minimalError.response?.status);
      console.log('   Error Data:', minimalError.response?.data);
    }

    // Test 3: Test with existing email
    console.log('\n🔍 Test 3: Signup with existing email...');
    
    try {
      const existingEmailResponse = await axios.post('http://localhost:3002/auth/signup', {
        email: '<EMAIL>', // This email already exists
        password: 'ExistingTest123!',
        firstName: 'Existing',
        lastName: 'User'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'http://localhost:4200'
        },
        timeout: 10000
      });
      
      console.log('❌ Existing email signup should have failed but succeeded');
      console.log('   Response:', existingEmailResponse.data);
      
    } catch (existingError) {
      console.log('✅ Existing email signup correctly failed');
      console.log('   Status:', existingError.response?.status);
      console.log('   Error:', existingError.response?.data?.error?.message);
    }

    // Test 4: Test with invalid password
    console.log('\n🔍 Test 4: Signup with weak password...');
    const testEmail4 = `weak-${Date.now()}@example.com`;
    
    try {
      const weakPasswordResponse = await axios.post('http://localhost:3002/auth/signup', {
        email: testEmail4,
        password: '123', // Weak password
        firstName: 'Weak',
        lastName: 'Password'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'http://localhost:4200'
        },
        timeout: 10000
      });
      
      console.log('❌ Weak password signup should have failed but succeeded');
      console.log('   Response:', weakPasswordResponse.data);
      
    } catch (weakError) {
      console.log('✅ Weak password signup correctly failed');
      console.log('   Status:', weakError.response?.status);
      console.log('   Error:', weakError.response?.data?.error?.message);
    }

    console.log('\n🎉 Signup API testing completed!');

  } catch (error) {
    console.error('❌ Signup API testing failed:', error.message);
  }
}

if (require.main === module) {
  testSignupAPI().catch(console.error);
}

module.exports = { testSignupAPI };
