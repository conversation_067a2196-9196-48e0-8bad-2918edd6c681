{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class LoadingService {\n  constructor() {\n    this.loadingSubject = new BehaviorSubject(false);\n    this.loading$ = this.loadingSubject.asObservable();\n  }\n  show() {\n    this.loadingSubject.next(true);\n  }\n  hide() {\n    this.loadingSubject.next(false);\n  }\n  static #_ = this.ɵfac = function LoadingService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LoadingService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LoadingService,\n    factory: LoadingService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "LoadingService", "constructor", "loadingSubject", "loading$", "asObservable", "show", "next", "hide", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\services\\loading.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class LoadingService {\n  private loadingSubject = new BehaviorSubject<boolean>(false);\n  public loading$ = this.loadingSubject.asObservable();\n\n  show(): void {\n    this.loadingSubject.next(true);\n  }\n\n  hide(): void {\n    this.loadingSubject.next(false);\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAKtC,OAAM,MAAOC,cAAc;EAH3BC,YAAA;IAIU,KAAAC,cAAc,GAAG,IAAIH,eAAe,CAAU,KAAK,CAAC;IACrD,KAAAI,QAAQ,GAAG,IAAI,CAACD,cAAc,CAACE,YAAY,EAAE;;EAEpDC,IAAIA,CAAA;IACF,IAAI,CAACH,cAAc,CAACI,IAAI,CAAC,IAAI,CAAC;EAChC;EAEAC,IAAIA,CAAA;IACF,IAAI,CAACL,cAAc,CAACI,IAAI,CAAC,KAAK,CAAC;EACjC;EAAC,QAAAE,CAAA,G;qCAVUR,cAAc;EAAA;EAAA,QAAAS,EAAA,G;WAAdT,cAAc;IAAAU,OAAA,EAAdV,cAAc,CAAAW,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}