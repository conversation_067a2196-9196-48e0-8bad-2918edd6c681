{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createErrorClass } from '../util/createErrorClass';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport const TimeoutError = createErrorClass(_super => function TimeoutErrorImpl(info = null) {\n  _super(this);\n  this.message = 'Timeout has occurred';\n  this.name = 'TimeoutError';\n  this.info = info;\n});\nexport function timeout(config, schedulerArg) {\n  const {\n    first,\n    each,\n    with: _with = timeoutErrorFactory,\n    scheduler = schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : asyncScheduler,\n    meta = null\n  } = isValidDate(config) ? {\n    first: config\n  } : typeof config === 'number' ? {\n    each: config\n  } : config;\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n  return operate((source, subscriber) => {\n    let originalSourceSubscription;\n    let timerSubscription;\n    let lastValue = null;\n    let seen = 0;\n    const startTimer = delay => {\n      timerSubscription = executeSchedule(subscriber, scheduler, () => {\n        try {\n          originalSourceSubscription.unsubscribe();\n          innerFrom(_with({\n            meta,\n            lastValue,\n            seen\n          })).subscribe(subscriber);\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }, delay);\n    };\n    originalSourceSubscription = source.subscribe(createOperatorSubscriber(subscriber, value => {\n      timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      seen++;\n      subscriber.next(lastValue = value);\n      each > 0 && startTimer(each);\n    }, undefined, undefined, () => {\n      if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n        timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      }\n      lastValue = null;\n    }));\n    !seen && startTimer(first != null ? typeof first === 'number' ? first : +first - scheduler.now() : each);\n  });\n}\nfunction timeoutErrorFactory(info) {\n  throw new TimeoutError(info);\n}", "map": {"version": 3, "names": ["asyncScheduler", "isValidDate", "operate", "innerFrom", "createErrorClass", "createOperatorSubscriber", "executeSchedule", "TimeoutError", "_super", "TimeoutErrorImpl", "info", "message", "name", "timeout", "config", "schedulerArg", "first", "each", "with", "_with", "timeoutErrorFactory", "scheduler", "meta", "TypeError", "source", "subscriber", "originalSourceSubscription", "timerSubscription", "lastValue", "seen", "startTimer", "delay", "unsubscribe", "subscribe", "err", "error", "value", "next", "undefined", "closed", "now"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/rxjs/dist/esm/internal/operators/timeout.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createErrorClass } from '../util/createErrorClass';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport const TimeoutError = createErrorClass((_super) => function TimeoutErrorImpl(info = null) {\n    _super(this);\n    this.message = 'Timeout has occurred';\n    this.name = 'TimeoutError';\n    this.info = info;\n});\nexport function timeout(config, schedulerArg) {\n    const { first, each, with: _with = timeoutErrorFactory, scheduler = schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : asyncScheduler, meta = null, } = (isValidDate(config) ? { first: config } : typeof config === 'number' ? { each: config } : config);\n    if (first == null && each == null) {\n        throw new TypeError('No timeout provided.');\n    }\n    return operate((source, subscriber) => {\n        let originalSourceSubscription;\n        let timerSubscription;\n        let lastValue = null;\n        let seen = 0;\n        const startTimer = (delay) => {\n            timerSubscription = executeSchedule(subscriber, scheduler, () => {\n                try {\n                    originalSourceSubscription.unsubscribe();\n                    innerFrom(_with({\n                        meta,\n                        lastValue,\n                        seen,\n                    })).subscribe(subscriber);\n                }\n                catch (err) {\n                    subscriber.error(err);\n                }\n            }, delay);\n        };\n        originalSourceSubscription = source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n            seen++;\n            subscriber.next((lastValue = value));\n            each > 0 && startTimer(each);\n        }, undefined, undefined, () => {\n            if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n                timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n            }\n            lastValue = null;\n        }));\n        !seen && startTimer(first != null ? (typeof first === 'number' ? first : +first - scheduler.now()) : each);\n    });\n}\nfunction timeoutErrorFactory(info) {\n    throw new TimeoutError(info);\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAO,MAAMC,YAAY,GAAGH,gBAAgB,CAAEI,MAAM,IAAK,SAASC,gBAAgBA,CAACC,IAAI,GAAG,IAAI,EAAE;EAC5FF,MAAM,CAAC,IAAI,CAAC;EACZ,IAAI,CAACG,OAAO,GAAG,sBAAsB;EACrC,IAAI,CAACC,IAAI,GAAG,cAAc;EAC1B,IAAI,CAACF,IAAI,GAAGA,IAAI;AACpB,CAAC,CAAC;AACF,OAAO,SAASG,OAAOA,CAACC,MAAM,EAAEC,YAAY,EAAE;EAC1C,MAAM;IAAEC,KAAK;IAAEC,IAAI;IAAEC,IAAI,EAAEC,KAAK,GAAGC,mBAAmB;IAAEC,SAAS,GAAGN,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGf,cAAc;IAAEsB,IAAI,GAAG;EAAM,CAAC,GAAIrB,WAAW,CAACa,MAAM,CAAC,GAAG;IAAEE,KAAK,EAAEF;EAAO,CAAC,GAAG,OAAOA,MAAM,KAAK,QAAQ,GAAG;IAAEG,IAAI,EAAEH;EAAO,CAAC,GAAGA,MAAO;EACzQ,IAAIE,KAAK,IAAI,IAAI,IAAIC,IAAI,IAAI,IAAI,EAAE;IAC/B,MAAM,IAAIM,SAAS,CAAC,sBAAsB,CAAC;EAC/C;EACA,OAAOrB,OAAO,CAAC,CAACsB,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,0BAA0B;IAC9B,IAAIC,iBAAiB;IACrB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,IAAI,GAAG,CAAC;IACZ,MAAMC,UAAU,GAAIC,KAAK,IAAK;MAC1BJ,iBAAiB,GAAGrB,eAAe,CAACmB,UAAU,EAAEJ,SAAS,EAAE,MAAM;QAC7D,IAAI;UACAK,0BAA0B,CAACM,WAAW,CAAC,CAAC;UACxC7B,SAAS,CAACgB,KAAK,CAAC;YACZG,IAAI;YACJM,SAAS;YACTC;UACJ,CAAC,CAAC,CAAC,CAACI,SAAS,CAACR,UAAU,CAAC;QAC7B,CAAC,CACD,OAAOS,GAAG,EAAE;UACRT,UAAU,CAACU,KAAK,CAACD,GAAG,CAAC;QACzB;MACJ,CAAC,EAAEH,KAAK,CAAC;IACb,CAAC;IACDL,0BAA0B,GAAGF,MAAM,CAACS,SAAS,CAAC5B,wBAAwB,CAACoB,UAAU,EAAGW,KAAK,IAAK;MAC1FT,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACK,WAAW,CAAC,CAAC;MACrGH,IAAI,EAAE;MACNJ,UAAU,CAACY,IAAI,CAAET,SAAS,GAAGQ,KAAM,CAAC;MACpCnB,IAAI,GAAG,CAAC,IAAIa,UAAU,CAACb,IAAI,CAAC;IAChC,CAAC,EAAEqB,SAAS,EAAEA,SAAS,EAAE,MAAM;MAC3B,IAAI,EAAEX,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACY,MAAM,CAAC,EAAE;QACnGZ,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACK,WAAW,CAAC,CAAC;MACzG;MACAJ,SAAS,GAAG,IAAI;IACpB,CAAC,CAAC,CAAC;IACH,CAACC,IAAI,IAAIC,UAAU,CAACd,KAAK,IAAI,IAAI,GAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,CAACA,KAAK,GAAGK,SAAS,CAACmB,GAAG,CAAC,CAAC,GAAIvB,IAAI,CAAC;EAC9G,CAAC,CAAC;AACN;AACA,SAASG,mBAAmBA,CAACV,IAAI,EAAE;EAC/B,MAAM,IAAIH,YAAY,CAACG,IAAI,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}