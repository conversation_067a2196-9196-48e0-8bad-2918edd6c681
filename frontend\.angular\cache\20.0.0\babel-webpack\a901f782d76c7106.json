{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nclass MatRippleModule {\n  static ɵfac = function MatRippleModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRippleModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatRippleModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRipple],\n      exports: [MatR<PERSON>ple, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatRippleModule as M };", "map": {"version": 3, "names": ["i0", "NgModule", "M", "MatCommonModule", "<PERSON><PERSON><PERSON><PERSON>", "MatRippleModule", "ɵfac", "MatRippleModule_Factory", "__ngFactoryType__", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "imports", "ngDevMode", "ɵsetClassMetadata", "args", "exports"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/material/fesm2022/index-BFRo2fUq.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\n\nclass MatRippleModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRippleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRippleModule, imports: [MatCommonModule, MatRipple], exports: [MatRipple, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRippleModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRippleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatRipple],\n                    exports: [MatRipple, MatCommonModule],\n                }]\n        }] });\n\nexport { MatRippleModule as M };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,SAASD,CAAC,IAAIE,SAAS,QAAQ,uBAAuB;AAEtD,MAAMC,eAAe,CAAC;EAClB,OAAOC,IAAI,YAAAC,wBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFH,eAAe;EAAA;EAClH,OAAOI,IAAI,kBAD8ET,EAAE,CAAAU,gBAAA;IAAAC,IAAA,EACSN;EAAe;EACnH,OAAOO,IAAI,kBAF8EZ,EAAE,CAAAa,gBAAA;IAAAC,OAAA,GAEoCX,eAAe,EAAEA,eAAe;EAAA;AACnK;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAJ6Ff,EAAE,CAAAgB,iBAAA,CAIJX,eAAe,EAAc,CAAC;IAC7GM,IAAI,EAAEV,QAAQ;IACdgB,IAAI,EAAE,CAAC;MACCH,OAAO,EAAE,CAACX,eAAe,EAAEC,SAAS,CAAC;MACrCc,OAAO,EAAE,CAACd,SAAS,EAAED,eAAe;IACxC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASE,eAAe,IAAIH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}