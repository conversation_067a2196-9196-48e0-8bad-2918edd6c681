import { LifeCycleObserver } from '@loopback/core';
import { juggler } from '@loopback/repository';
export declare class DbDataSource extends juggler.DataSource implements LifeCycleObserver {
    static dataSourceName: string;
    static readonly defaultConfig: {
        name: string;
        connector: string;
        url: string | undefined;
        host: string;
        port: string | number;
        user: string;
        password: string;
        database: string;
        ssl: boolean | {
            rejectUnauthorized: boolean;
        };
        min: number;
        max: number;
        acquireTimeoutMillis: number;
        idleTimeoutMillis: number;
    };
    constructor(dsConfig?: object);
}
