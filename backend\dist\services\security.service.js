"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const speakeasy = tslib_1.__importStar(require("speakeasy"));
const QRCode = tslib_1.__importStar(require("qrcode"));
const crypto = tslib_1.__importStar(require("crypto"));
const bcryptjs_1 = require("bcryptjs");
const repositories_1 = require("../repositories");
let SecurityService = class SecurityService {
    constructor(userRepository, otpRepository) {
        this.userRepository = userRepository;
        this.otpRepository = otpRepository;
    }
    async generateTwoFactorSecret(userId) {
        const user = await this.userRepository.findById(userId);
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        const secret = speakeasy.generateSecret({
            name: `SecureApp (${user.email})`,
            issuer: 'SecureApp',
            length: 32,
        });
        // Save the secret to user
        await this.userRepository.updateById(userId, {
            twoFactorSecret: secret.base32,
            updatedAt: new Date(),
        });
        // Generate QR code
        const qrCode = await QRCode.toDataURL(secret.otpauth_url);
        return {
            secret: secret.base32,
            qrCode,
        };
    }
    async verifyTwoFactorToken(userId, token) {
        const user = await this.userRepository.findById(userId);
        if (!user || !user.twoFactorSecret) {
            throw new rest_1.HttpErrors.BadRequest('Two-factor authentication not set up');
        }
        const verified = speakeasy.totp.verify({
            secret: user.twoFactorSecret,
            encoding: 'base32',
            token,
            window: 2, // Allow 2 time steps before and after
        });
        return verified;
    }
    async enableTwoFactor(userId, token) {
        const isValid = await this.verifyTwoFactorToken(userId, token);
        if (!isValid) {
            throw new rest_1.HttpErrors.BadRequest('Invalid verification token');
        }
        await this.userRepository.updateById(userId, {
            twoFactorEnabled: true,
            updatedAt: new Date(),
        });
    }
    async disableTwoFactor(userId, token) {
        const isValid = await this.verifyTwoFactorToken(userId, token);
        if (!isValid) {
            throw new rest_1.HttpErrors.BadRequest('Invalid verification token');
        }
        await this.userRepository.updateById(userId, {
            twoFactorEnabled: false,
            twoFactorSecret: undefined,
            updatedAt: new Date(),
        });
    }
    async generateOTP(identifier, type) {
        // Generate 6-digit OTP
        const code = Math.floor(100000 + Math.random() * 900000).toString();
        const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
        // Delete any existing OTPs for this identifier and type
        await this.otpRepository.deleteAll({
            identifier,
            type,
            used: false,
        });
        // Create new OTP
        await this.otpRepository.create({
            identifier,
            code,
            type,
            expiresAt,
        });
        return code;
    }
    async verifyOTP(identifier, code, type) {
        const otp = await this.otpRepository.findOne({
            where: {
                identifier,
                code,
                type,
                used: false,
                expiresAt: { gt: new Date() },
            },
        });
        if (!otp) {
            // Increment attempts for existing OTPs
            const existingOtp = await this.otpRepository.findOne({
                where: {
                    identifier,
                    type,
                    used: false,
                },
            });
            if (existingOtp) {
                await this.otpRepository.updateById(existingOtp.id, {
                    attempts: (existingOtp.attempts || 0) + 1,
                });
                // Lock after 3 failed attempts
                if ((existingOtp.attempts || 0) >= 2) {
                    await this.otpRepository.updateById(existingOtp.id, {
                        used: true,
                        usedAt: new Date(),
                    });
                }
            }
            return false;
        }
        // Mark OTP as used
        await this.otpRepository.updateById(otp.id, {
            used: true,
            usedAt: new Date(),
        });
        return true;
    }
    async generatePasswordResetToken() {
        return crypto.randomBytes(32).toString('hex');
    }
    async generateEmailVerificationToken() {
        return crypto.randomBytes(32).toString('hex');
    }
    async hashPassword(password) {
        return (0, bcryptjs_1.hash)(password, 12);
    }
    async isDisposableEmail(email) {
        // List of common disposable email domains
        const disposableDomains = [
            '10minutemail.com',
            'guerrillamail.com',
            'mailinator.com',
            'tempmail.org',
            'throwaway.email',
            'temp-mail.org',
            'yopmail.com',
            'maildrop.cc',
            'sharklasers.com',
            'guerrillamailblock.com',
        ];
        const domain = email.split('@')[1]?.toLowerCase();
        return disposableDomains.includes(domain);
    }
    async validatePasswordStrength(password) {
        const errors = [];
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
};
exports.SecurityService = SecurityService;
exports.SecurityService = SecurityService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.TRANSIENT }),
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.OtpRepository)),
    tslib_1.__metadata("design:paramtypes", [repositories_1.UserRepository,
        repositories_1.OtpRepository])
], SecurityService);
//# sourceMappingURL=security.service.js.map