#!/usr/bin/env node

/**
 * Test Live Backend Database Connection
 */

const axios = require('axios');

async function testLiveBackend() {
  console.log('🔍 TESTING LIVE BACKEND DATABASE');
  console.log('=================================\n');

  try {
    // Test if backend is running
    console.log('🔍 Testing backend health...');
    const pingResponse = await axios.get('http://localhost:3002/ping');
    console.log('✅ Backend is running');
    console.log('   Response time:', pingResponse.headers.date);

    // Test login with correct credentials
    console.log('\n🔍 Testing login with PostgreSQL credentials...');
    try {
      const loginResponse = await axios.post('http://localhost:3002/auth/login', {
        email: '<EMAIL>',
        password: 'Test123!@#'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'http://localhost:3001'
        }
      });
      
      console.log('✅ Login successful!');
      console.log('   Token received:', loginResponse.data.token ? 'Yes' : 'No');
      console.log('   User data:', loginResponse.data.user ? loginResponse.data.user.email : 'None');
      
    } catch (loginError) {
      console.log('❌ Login failed:', loginError.response?.data || loginError.message);
      console.log('   Status:', loginError.response?.status);
      
      // If login fails, the backend might be using memory DB
      console.log('\n💡 Backend might be using in-memory database');
      console.log('   Let\'s test with a new user signup to see which DB it\'s using...');
      
      try {
        const signupResponse = await axios.post('http://localhost:3002/auth/signup', {
          email: '<EMAIL>',
          password: 'TestMemory123!',
          firstName: 'Memory',
          lastName: 'Test'
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Origin': 'http://localhost:3001'
          }
        });
        
        console.log('✅ Signup successful - backend is working but using wrong DB');
        console.log('   User ID:', signupResponse.data.userId);
        
      } catch (signupError) {
        console.log('❌ Signup also failed:', signupError.response?.data || signupError.message);
      }
    }

    console.log('\n🎉 Live backend test completed!');

  } catch (error) {
    console.error('❌ Error testing live backend:', error.message);
  }
}

if (require.main === module) {
  testLiveBackend().catch(console.error);
}

module.exports = { testLiveBackend };
