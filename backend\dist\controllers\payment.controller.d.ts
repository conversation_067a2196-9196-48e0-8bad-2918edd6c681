import { UserProfile } from '@loopback/security';
import { PaymentRepository } from '../repositories';
import { PaymentService } from '../services';
import { Payment } from '../models';
export declare class PaymentController {
    currentUserProfile: UserProfile;
    protected paymentRepository: PaymentRepository;
    paymentService: PaymentService;
    constructor(currentUserProfile: UserProfile, paymentRepository: PaymentRepository, paymentService: PaymentService);
    createOrder(request: {
        amount: number;
        currency: string;
        description?: string;
    }): Promise<{
        orderId: string;
        amount: number;
        currency: string;
        key: string;
    }>;
    verifyPayment(request: {
        orderId: string;
        paymentId: string;
        signature: string;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    getPaymentStatus(orderId: string): Promise<{
        payment: Payment | null;
    }>;
    getMyPayments(): Promise<{
        payments: Payment[];
    }>;
    refundPayment(request: {
        paymentId: string;
        amount?: number;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
}
export declare class PaymentWebhookController {
    paymentService: PaymentService;
    constructor(paymentService: PaymentService);
    handleWebhook(payload: any, signature: string): Promise<{
        status: string;
    }>;
}
