"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const tslib_1 = require("tslib");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const core_1 = require("@loopback/core");
const authentication_jwt_1 = require("@loopback/authentication-jwt");
const security_1 = require("@loopback/security");
const models_1 = require("../models");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let AuthController = class AuthController {
    constructor(jwtService, userService, user, userRepository, securityService, emailService, smsService) {
        this.jwtService = jwtService;
        this.userService = userService;
        this.user = user;
        this.userRepository = userRepository;
        this.securityService = securityService;
        this.emailService = emailService;
        this.smsService = smsService;
    }
    async signUp(newUserRequest) {
        // Check if email is disposable
        const isDisposable = await this.securityService.isDisposableEmail(newUserRequest.email);
        if (isDisposable) {
            throw new rest_1.HttpErrors.BadRequest('Disposable email addresses are not allowed');
        }
        // Validate password strength
        const passwordValidation = await this.securityService.validatePasswordStrength(newUserRequest.password);
        if (!passwordValidation.isValid) {
            throw new rest_1.HttpErrors.BadRequest(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
        }
        // Check if user already exists
        const foundUser = await this.userRepository.findOne({
            where: { email: newUserRequest.email },
        });
        if (foundUser) {
            throw new rest_1.HttpErrors.Conflict('Email already exists');
        }
        // Hash password
        const password = await this.securityService.hashPassword(newUserRequest.password);
        // Generate email verification token
        const emailVerificationToken = await this.securityService.generateEmailVerificationToken();
        const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
        // Create user
        const savedUser = await this.userRepository.create({
            email: newUserRequest.email,
            firstName: newUserRequest.firstName,
            lastName: newUserRequest.lastName,
            phone: newUserRequest.phone,
            password: password,
            emailVerificationToken,
            emailVerificationExpires,
            roles: ['user'],
        });
        // Send verification email
        try {
            await this.emailService.sendVerificationEmail(savedUser.email, emailVerificationToken);
        }
        catch (error) {
            console.error('Failed to send verification email:', error);
            // Don't fail registration if email sending fails
        }
        return {
            message: 'User registered successfully. Please check your email for verification.',
            userId: savedUser.id,
        };
    }
    async login(credentials) {
        // Ensure the user exists, and the password is correct
        const user = await this.userService.verifyCredentials(credentials);
        // Check if email is verified
        if (!user.emailVerified) {
            throw new rest_1.HttpErrors.Unauthorized('Please verify your email before logging in');
        }
        // Check if 2FA is enabled
        if (user.twoFactorEnabled) {
            if (!credentials.twoFactorToken) {
                return {
                    token: '',
                    user,
                    requiresTwoFactor: true,
                };
            }
            // Verify 2FA token
            const isValid2FA = await this.securityService.verifyTwoFactorToken(user.id, credentials.twoFactorToken);
            if (!isValid2FA) {
                throw new rest_1.HttpErrors.Unauthorized('Invalid two-factor authentication token');
            }
        }
        // Convert a User object into a UserProfile object (reduced set of properties)
        const userProfile = this.userService.convertToUserProfile(user);
        // Create a JSON Web Token based on the user profile
        const token = await this.jwtService.generateToken(userProfile);
        return { token, user };
    }
    async verifyEmail(request) {
        const user = await this.userRepository.findOne({
            where: {
                emailVerificationToken: request.token,
                emailVerificationExpires: { gt: new Date() },
            },
        });
        if (!user) {
            throw new rest_1.HttpErrors.BadRequest('Invalid or expired verification token');
        }
        await this.userRepository.updateById(user.id, {
            emailVerified: true,
            emailVerificationToken: undefined,
            emailVerificationExpires: undefined,
            updatedAt: new Date(),
        });
        return { message: 'Email verified successfully' };
    }
    async forgotPassword(request) {
        const user = await this.userRepository.findOne({
            where: { email: request.email },
        });
        if (!user) {
            // Don't reveal if email exists or not
            return { message: 'If the email exists, a password reset link has been sent' };
        }
        const resetToken = await this.securityService.generatePasswordResetToken();
        const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
        await this.userRepository.updateById(user.id, {
            passwordResetToken: resetToken,
            passwordResetExpires: resetExpires,
            updatedAt: new Date(),
        });
        try {
            await this.emailService.sendPasswordResetEmail(user.email, resetToken);
        }
        catch (error) {
            console.error('Failed to send password reset email:', error);
        }
        return { message: 'If the email exists, a password reset link has been sent' };
    }
    async resetPassword(request) {
        const user = await this.userRepository.findOne({
            where: {
                passwordResetToken: request.token,
                passwordResetExpires: { gt: new Date() },
            },
        });
        if (!user) {
            throw new rest_1.HttpErrors.BadRequest('Invalid or expired reset token');
        }
        // Validate password strength
        const passwordValidation = await this.securityService.validatePasswordStrength(request.password);
        if (!passwordValidation.isValid) {
            throw new rest_1.HttpErrors.BadRequest(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
        }
        // Hash new password
        const hashedPassword = await this.securityService.hashPassword(request.password);
        // Update password
        await this.userRepository.updateById(user.id, {
            password: hashedPassword,
            updatedAt: new Date(),
        });
        // Clear reset token
        await this.userRepository.updateById(user.id, {
            passwordResetToken: undefined,
            passwordResetExpires: undefined,
            updatedAt: new Date(),
        });
        return { message: 'Password reset successfully' };
    }
};
exports.AuthController = AuthController;
tslib_1.__decorate([
    (0, rest_1.post)('/auth/signup'),
    (0, rest_1.response)(200, {
        description: 'User registration',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        userId: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: (0, rest_1.getModelSchemaRef)(models_1.User, {
                    title: 'NewUser',
                    exclude: ['id', 'emailVerified', 'phoneVerified', 'twoFactorEnabled', 'roles', 'isActive', 'createdAt', 'updatedAt'],
                }),
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "signUp", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/login'),
    (0, rest_1.response)(200, {
        description: 'Token',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        token: { type: 'string' },
                        user: (0, rest_1.getModelSchemaRef)(models_1.User, { exclude: ['password'] }),
                        requiresTwoFactor: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['email', 'password'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                        password: { type: 'string', minLength: 8 },
                        twoFactorToken: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/verify-email'),
    (0, rest_1.response)(200, {
        description: 'Email verification',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token'],
                    properties: {
                        token: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "verifyEmail", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/forgot-password'),
    (0, rest_1.response)(200, {
        description: 'Password reset request',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['email'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "forgotPassword", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/reset-password'),
    (0, rest_1.response)(200, {
        description: 'Password reset',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token', 'password'],
                    properties: {
                        token: { type: 'string' },
                        password: { type: 'string', minLength: 8 },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "resetPassword", null);
exports.AuthController = AuthController = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)(authentication_jwt_1.TokenServiceBindings.TOKEN_SERVICE)),
    tslib_1.__param(1, (0, core_1.inject)(authentication_jwt_1.UserServiceBindings.USER_SERVICE)),
    tslib_1.__param(2, (0, core_1.inject)(security_1.SecurityBindings.USER, { optional: true })),
    tslib_1.__param(3, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(4, (0, core_1.inject)('services.SecurityService')),
    tslib_1.__param(5, (0, core_1.inject)('services.EmailService')),
    tslib_1.__param(6, (0, core_1.inject)('services.SmsService')),
    tslib_1.__metadata("design:paramtypes", [Object, services_1.MyUserService, Object, repositories_1.UserRepository,
        services_1.SecurityService,
        services_1.EmailService,
        services_1.SmsService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map