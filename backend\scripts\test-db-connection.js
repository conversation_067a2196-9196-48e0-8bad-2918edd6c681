#!/usr/bin/env node

/**
 * Database Connection Test Script
 * This script tests the database connection and verifies data
 */

require('dotenv').config();

async function testDatabaseConnection() {
  console.log('🔍 TESTING DATABASE CONNECTION');
  console.log('==============================\n');

  try {
    // Test environment variables
    console.log('📋 Environment Variables:');
    console.log('USE_POSTGRESQL:', process.env.USE_POSTGRESQL);
    console.log('DATABASE_URL:', process.env.DATABASE_URL);
    console.log('DB_HOST:', process.env.DB_HOST);
    console.log('DB_PORT:', process.env.DB_PORT);
    console.log('DB_NAME:', process.env.DB_NAME);
    console.log('DB_USER:', process.env.DB_USER);
    console.log('');

    // Test PostgreSQL connection directly
    console.log('🐘 Testing PostgreSQL Connection:');
    const { Client } = require('pg');
    const client = new Client({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password',
      database: process.env.DB_NAME || 'secure_backend',
    });

    await client.connect();
    console.log('✅ PostgreSQL connection successful');

    // Test data
    const result = await client.query('SELECT email, first_name, last_name FROM public."user"');
    console.log('📊 Users in PostgreSQL:');
    result.rows.forEach(user => {
      console.log(`   - ${user.email} (${user.first_name} ${user.last_name})`);
    });

    await client.end();
    console.log('');

    // Test LoopBack application
    console.log('🚀 Testing LoopBack Application:');
    
    // Force PostgreSQL usage
    process.env.USE_POSTGRESQL = 'true';
    
    const { SecureBackendApplication } = require('../dist/application');
    const app = new SecureBackendApplication({
      rest: {
        port: 0, // Use random port for testing
        host: 'localhost',
      },
    });

    await app.boot();
    console.log('✅ LoopBack application booted');

    // Test datasource
    const datasource = await app.get('datasources.db');
    console.log('📊 Datasource connector:', datasource.connector.name);
    console.log('📊 Datasource settings:', {
      host: datasource.settings.host,
      port: datasource.settings.port,
      database: datasource.settings.database,
      user: datasource.settings.user
    });

    // Test repository
    const { UserRepository } = require('../dist/repositories');
    const userRepository = new UserRepository(datasource);
    
    const users = await userRepository.find();
    console.log('📊 Users from LoopBack:');
    users.forEach(user => {
      console.log(`   - ${user.email} (${user.firstName} ${user.lastName})`);
    });

    await app.stop();
    console.log('✅ LoopBack application stopped');

    console.log('\n🎉 Database connection test completed successfully!');

  } catch (error) {
    console.error('❌ Database connection test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

if (require.main === module) {
  testDatabaseConnection().catch(console.error);
}

module.exports = { testDatabaseConnection };
