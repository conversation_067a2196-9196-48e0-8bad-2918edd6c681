"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const rest_1 = require("@loopback/rest");
const security_1 = require("@loopback/security");
const jwt = tslib_1.__importStar(require("jsonwebtoken"));
let JwtService = class JwtService {
    constructor() {
        this.jwtSecret = process.env.JWT_SECRET || 'fallback-secret-for-development-only';
        this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';
        this.refreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '7d';
        this.algorithm = process.env.JWT_ALGORITHM || 'HS256';
        this.issuer = process.env.JWT_ISSUER || 'secure-backend';
        this.audience = process.env.JWT_AUDIENCE || 'secure-frontend';
        // Validate JWT secret in production
        if (process.env.NODE_ENV === 'production' && this.jwtSecret.length < 32) {
            throw new Error('JWT_SECRET must be at least 32 characters long in production');
        }
    }
    /**
     * Generate access token for user
     */
    async generateToken(userProfile) {
        if (!userProfile[security_1.securityId]) {
            throw new rest_1.HttpErrors.Unauthorized('User profile is missing security ID');
        }
        const payload = {
            [security_1.securityId]: userProfile[security_1.securityId],
            id: userProfile.id || userProfile[security_1.securityId],
            email: userProfile.email || '',
            roles: userProfile.roles || ['user'],
        };
        try {
            const token = jwt.sign(payload, this.jwtSecret, {
                expiresIn: this.jwtExpiresIn,
                algorithm: this.algorithm,
                issuer: this.issuer,
                audience: this.audience,
                subject: userProfile[security_1.securityId],
            });
            return token;
        }
        catch (error) {
            throw new rest_1.HttpErrors.InternalServerError('Error generating JWT token');
        }
    }
    /**
     * Generate refresh token for user
     */
    async generateRefreshToken(userId) {
        const tokenId = this.generateTokenId();
        const payload = {
            userId,
            tokenId,
        };
        try {
            const refreshToken = jwt.sign(payload, this.jwtSecret, {
                expiresIn: this.refreshExpiresIn,
                algorithm: this.algorithm,
                issuer: this.issuer,
                audience: this.audience,
                subject: userId,
            });
            return refreshToken;
        }
        catch (error) {
            throw new rest_1.HttpErrors.InternalServerError('Error generating refresh token');
        }
    }
    /**
     * Verify and decode access token
     */
    async verifyToken(token) {
        if (!token) {
            throw new rest_1.HttpErrors.Unauthorized('Token is required');
        }
        try {
            const decoded = jwt.verify(token, this.jwtSecret, {
                algorithms: [this.algorithm],
                issuer: this.issuer,
                audience: this.audience,
            });
            // Create user profile from token payload
            const userProfile = {
                [security_1.securityId]: decoded[security_1.securityId],
                id: decoded.id,
                email: decoded.email,
                roles: decoded.roles,
            };
            return userProfile;
        }
        catch (error) {
            if (error instanceof jwt.JsonWebTokenError) {
                throw new rest_1.HttpErrors.Unauthorized('Invalid token');
            }
            else if (error instanceof jwt.TokenExpiredError) {
                throw new rest_1.HttpErrors.Unauthorized('Token expired');
            }
            else if (error instanceof jwt.NotBeforeError) {
                throw new rest_1.HttpErrors.Unauthorized('Token not active');
            }
            else {
                throw new rest_1.HttpErrors.Unauthorized('Token verification failed');
            }
        }
    }
    /**
     * Verify refresh token
     */
    async verifyRefreshToken(refreshToken) {
        if (!refreshToken) {
            throw new rest_1.HttpErrors.Unauthorized('Refresh token is required');
        }
        try {
            const decoded = jwt.verify(refreshToken, this.jwtSecret, {
                algorithms: [this.algorithm],
                issuer: this.issuer,
                audience: this.audience,
            });
            return decoded;
        }
        catch (error) {
            throw new rest_1.HttpErrors.Unauthorized('Invalid refresh token');
        }
    }
    /**
     * Extract token from Authorization header
     */
    extractTokenFromHeader(authHeader) {
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new rest_1.HttpErrors.Unauthorized('Invalid authorization header format');
        }
        return authHeader.substring(7); // Remove 'Bearer ' prefix
    }
    /**
     * Check if token is expired without throwing error
     */
    isTokenExpired(token) {
        try {
            jwt.verify(token, this.jwtSecret);
            return false;
        }
        catch (error) {
            return error instanceof jwt.TokenExpiredError;
        }
    }
    /**
     * Get token expiration time
     */
    getTokenExpiration(token) {
        try {
            const decoded = jwt.decode(token);
            if (decoded && decoded.exp) {
                return new Date(decoded.exp * 1000);
            }
            return null;
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Generate unique token ID for refresh tokens
     */
    generateTokenId() {
        return Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15) +
            Date.now().toString(36);
    }
    /**
     * Blacklist token (for logout functionality)
     * In production, implement with Redis or database
     */
    async blacklistToken(token) {
        // TODO: Implement token blacklisting with Redis or database
        // For now, this is a placeholder
        console.log(`Token blacklisted: ${token.substring(0, 20)}...`);
    }
};
exports.JwtService = JwtService;
exports.JwtService = JwtService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.TRANSIENT }),
    tslib_1.__metadata("design:paramtypes", [])
], JwtService);
//# sourceMappingURL=jwt.service.js.map