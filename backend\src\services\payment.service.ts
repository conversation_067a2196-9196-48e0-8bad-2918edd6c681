import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import Razorpay from 'razorpay';
import * as crypto from 'crypto';
import {PaymentRepository} from '../repositories';
import {Payment} from '../models';

@injectable({scope: BindingScope.TRANSIENT})
export class PaymentService {
  private razorpay: Razorpay;

  constructor(
    @repository(PaymentRepository) public paymentRepository: PaymentRepository,
  ) {
    this.razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID || 'your_key_id',
      key_secret: process.env.RAZORPAY_KEY_SECRET || 'your_key_secret',
    });
  }

  async createOrder(
    amount: number,
    currency: string,
    userId: string,
    description?: string,
  ): Promise<{orderId: string; amount: number; currency: string}> {
    console.log('🔍 Payment order creation started');
    console.log('   Amount:', amount);
    console.log('   Currency:', currency);
    console.log('   User ID:', userId);
    console.log('   Description:', description);
    console.log('   Razorpay Key ID:', process.env.RAZORPAY_KEY_ID);
    console.log('   Razorpay Key Secret:', process.env.RAZORPAY_KEY_SECRET ? 'Set' : 'Not set');

    try {
      // Convert amount to smallest currency unit (paise for INR, cents for USD)
      const amountInSmallestUnit = currency === 'INR' ? amount * 100 : amount * 100;
      console.log('   Amount in smallest unit:', amountInSmallestUnit);

      const options = {
        amount: amountInSmallestUnit,
        currency: currency.toUpperCase(),
        receipt: `receipt_${Date.now()}`,
        notes: {
          userId,
          description: description || 'Payment for services',
        },
      };

      console.log('   Razorpay order options:', options);
      console.log('   Creating Razorpay order...');
      const order = await this.razorpay.orders.create(options);
      console.log('   Razorpay order created:', order.id);

      // Save payment record
      await this.paymentRepository.create({
        razorpayOrderId: order.id,
        amount,
        currency: currency.toUpperCase(),
        status: 'pending',
        description,
        userId,
        metadata: {
          receipt: options.receipt,
        },
      });

      return {
        orderId: order.id,
        amount: amount,
        currency: currency.toUpperCase(),
      };
    } catch (error) {
      console.error('❌ Payment order creation failed:', error);
      console.error('   Error message:', error.message);
      console.error('   Error stack:', error.stack);
      if (error.response) {
        console.error('   Response status:', error.response.status);
        console.error('   Response data:', error.response.data);
      }
      throw new HttpErrors.BadRequest('Failed to create payment order');
    }
  }

  async verifyPayment(
    orderId: string,
    paymentId: string,
    signature: string,
  ): Promise<boolean> {
    try {
      // Find payment record
      const payment = await this.paymentRepository.findOne({
        where: {razorpayOrderId: orderId},
      });

      if (!payment) {
        throw new HttpErrors.NotFound('Payment record not found');
      }

      // Verify signature
      const body = orderId + '|' + paymentId;
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET || 'your_key_secret')
        .update(body.toString())
        .digest('hex');

      const isAuthentic = expectedSignature === signature;

      if (isAuthentic) {
        // Update payment status
        await this.paymentRepository.updateById(payment.id, {
          razorpayPaymentId: paymentId,
          razorpaySignature: signature,
          status: 'paid',
          paidAt: new Date(),
          updatedAt: new Date(),
        });

        return true;
      } else {
        // Update payment status to failed
        await this.paymentRepository.updateById(payment.id, {
          status: 'failed',
          updatedAt: new Date(),
        });

        return false;
      }
    } catch (error) {
      console.error('Payment verification failed:', error);
      throw new HttpErrors.BadRequest('Payment verification failed');
    }
  }

  async getPaymentStatus(orderId: string): Promise<Payment | null> {
    try {
      const payment = await this.paymentRepository.findOne({
        where: {razorpayOrderId: orderId},
      });

      return payment;
    } catch (error) {
      console.error('Failed to get payment status:', error);
      return null;
    }
  }

  async getUserPayments(userId: string): Promise<Payment[]> {
    try {
      return await this.paymentRepository.find({
        where: {userId},
        order: ['createdAt DESC'],
      });
    } catch (error) {
      console.error('Failed to get user payments:', error);
      return [];
    }
  }

  async refundPayment(paymentId: string, amount?: number): Promise<boolean> {
    try {
      const payment = await this.paymentRepository.findOne({
        where: {razorpayPaymentId: paymentId},
      });

      if (!payment || payment.status !== 'paid') {
        throw new HttpErrors.BadRequest('Payment not found or not eligible for refund');
      }

      const refundAmount = amount || payment.amount;
      const refundAmountInSmallestUnit = payment.currency === 'INR' ? refundAmount * 100 : refundAmount * 100;

      const refund = await this.razorpay.payments.refund(paymentId, {
        amount: refundAmountInSmallestUnit,
      });

      if (refund.status === 'processed') {
        await this.paymentRepository.updateById(payment.id, {
          status: 'refunded',
          updatedAt: new Date(),
          metadata: {
            ...payment.metadata,
            refundId: refund.id,
            refundAmount,
          },
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error('Refund failed:', error);
      throw new HttpErrors.BadRequest('Refund processing failed');
    }
  }

  async handleWebhook(payload: any, signature: string): Promise<void> {
    try {
      // Verify webhook signature
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET || 'your_webhook_secret')
        .update(JSON.stringify(payload))
        .digest('hex');

      if (expectedSignature !== signature) {
        throw new HttpErrors.Unauthorized('Invalid webhook signature');
      }

      const event = payload.event;
      const paymentEntity = payload.payload.payment.entity;

      switch (event) {
        case 'payment.captured':
          await this.handlePaymentCaptured(paymentEntity);
          break;
        case 'payment.failed':
          await this.handlePaymentFailed(paymentEntity);
          break;
        default:
          console.log(`Unhandled webhook event: ${event}`);
      }
    } catch (error) {
      console.error('Webhook handling failed:', error);
      throw error;
    }
  }

  private async handlePaymentCaptured(paymentEntity: any): Promise<void> {
    const payment = await this.paymentRepository.findOne({
      where: {razorpayOrderId: paymentEntity.order_id},
    });

    if (payment) {
      await this.paymentRepository.updateById(payment.id, {
        razorpayPaymentId: paymentEntity.id,
        status: 'paid',
        paidAt: new Date(),
        updatedAt: new Date(),
      });
    }
  }

  private async handlePaymentFailed(paymentEntity: any): Promise<void> {
    const payment = await this.paymentRepository.findOne({
      where: {razorpayOrderId: paymentEntity.order_id},
    });

    if (payment) {
      await this.paymentRepository.updateById(payment.id, {
        razorpayPaymentId: paymentEntity.id,
        status: 'failed',
        updatedAt: new Date(),
      });
    }
  }
}
