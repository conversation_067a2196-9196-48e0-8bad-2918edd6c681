{"ast": null, "code": "export const timeoutProvider = {\n  setTimeout(handler, timeout, ...args) {\n    const {\n      delegate\n    } = timeoutProvider;\n    if (delegate === null || delegate === void 0 ? void 0 : delegate.setTimeout) {\n      return delegate.setTimeout(handler, timeout, ...args);\n    }\n    return setTimeout(handler, timeout, ...args);\n  },\n  clearTimeout(handle) {\n    const {\n      delegate\n    } = timeoutProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearTimeout) || clearTimeout)(handle);\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["timeout<PERSON>rovider", "setTimeout", "handler", "timeout", "args", "delegate", "clearTimeout", "handle", "undefined"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/rxjs/dist/esm/internal/scheduler/timeoutProvider.js"], "sourcesContent": ["export const timeoutProvider = {\n    setTimeout(handler, timeout, ...args) {\n        const { delegate } = timeoutProvider;\n        if (delegate === null || delegate === void 0 ? void 0 : delegate.setTimeout) {\n            return delegate.setTimeout(handler, timeout, ...args);\n        }\n        return setTimeout(handler, timeout, ...args);\n    },\n    clearTimeout(handle) {\n        const { delegate } = timeoutProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearTimeout) || clearTimeout)(handle);\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,OAAO,MAAMA,eAAe,GAAG;EAC3BC,UAAUA,CAACC,OAAO,EAAEC,OAAO,EAAE,GAAGC,IAAI,EAAE;IAClC,MAAM;MAAEC;IAAS,CAAC,GAAGL,eAAe;IACpC,IAAIK,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACJ,UAAU,EAAE;MACzE,OAAOI,QAAQ,CAACJ,UAAU,CAACC,OAAO,EAAEC,OAAO,EAAE,GAAGC,IAAI,CAAC;IACzD;IACA,OAAOH,UAAU,CAACC,OAAO,EAAEC,OAAO,EAAE,GAAGC,IAAI,CAAC;EAChD,CAAC;EACDE,YAAYA,CAACC,MAAM,EAAE;IACjB,MAAM;MAAEF;IAAS,CAAC,GAAGL,eAAe;IACpC,OAAO,CAAC,CAACK,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,YAAY,KAAKA,YAAY,EAAEC,MAAM,CAAC;EAChH,CAAC;EACDF,QAAQ,EAAEG;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}