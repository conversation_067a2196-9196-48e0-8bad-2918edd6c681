#!/usr/bin/env node

/**
 * Test Current Running Backend Database
 */

const axios = require('axios');

async function testCurrentBackend() {
  console.log('🔍 TESTING CURRENT BACKEND DATABASE CONNECTION');
  console.log('===============================================\n');

  try {
    // Test signup with a unique email to see which database it uses
    const testEmail = `test-${Date.now()}@example.com`;
    console.log('🔍 Testing signup to determine database...');
    console.log('   Test email:', testEmail);

    try {
      const signupResponse = await axios.post('http://localhost:3002/auth/signup', {
        email: testEmail,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'User'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'http://localhost:3001'
        }
      });
      
      console.log('✅ Signup successful!');
      console.log('   User ID:', signupResponse.data.userId);
      console.log('   Message:', signupResponse.data.message);
      
      // Now try to login with the new user
      console.log('\n🔍 Testing login with new user...');
      try {
        const loginResponse = await axios.post('http://localhost:3002/auth/login', {
          email: testEmail,
          password: 'TestPassword123!'
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Origin': 'http://localhost:3001'
          }
        });
        
        console.log('✅ Login successful with new user!');
        console.log('   Token received:', loginResponse.data.token ? 'Yes' : 'No');
        console.log('   User email:', loginResponse.data.user?.email);
        
        // Backend is working but using in-memory DB
        console.log('\n💡 Backend is working but using IN-MEMORY database');
        console.log('   This explains why PostgreSQL users cannot login');
        
      } catch (loginError) {
        console.log('❌ Login failed with new user:', loginError.response?.data || loginError.message);
      }
      
    } catch (signupError) {
      console.log('❌ Signup failed:', signupError.response?.data || signupError.message);
      console.log('   Status:', signupError.response?.status);
      
      if (signupError.response?.status === 500) {
        console.log('\n💡 500 error suggests service binding issues');
      }
    }

    // Test with PostgreSQL user to confirm
    console.log('\n🔍 Testing login with PostgreSQL user...');
    try {
      const pgLoginResponse = await axios.post('http://localhost:3002/auth/login', {
        email: '<EMAIL>',
        password: 'Test123!@#'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'http://localhost:3001'
        }
      });
      
      console.log('✅ PostgreSQL user login successful!');
      console.log('   Backend is using PostgreSQL');
      
    } catch (pgLoginError) {
      console.log('❌ PostgreSQL user login failed:', pgLoginError.response?.data || pgLoginError.message);
      console.log('   Confirms backend is NOT using PostgreSQL');
    }

    console.log('\n🎉 Current backend test completed!');

  } catch (error) {
    console.error('❌ Error testing current backend:', error.message);
  }
}

if (require.main === module) {
  testCurrentBackend().catch(console.error);
}

module.exports = { testCurrentBackend };
