"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},__assign.apply(this,arguments)};
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){var e=globalThis;function t(t){return(e.__Zone_symbol_prefix||"__zone_symbol__")+t}function n(){var n=e.performance;function r(e){n&&n.mark&&n.mark(e)}function o(e,t){n&&n.measure&&n.measure(e,t)}r("Zone");var a,i=function(){function n(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new c(this,this._parent&&this._parent._zoneDelegate,t)}return n.assertZonePatched=function(){if(e.Promise!==D.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")},Object.defineProperty(n,"root",{get:function(){for(var e=n.current;e.parent;)e=e.parent;return e},enumerable:!1,configurable:!0}),Object.defineProperty(n,"current",{get:function(){return C.zone},enumerable:!1,configurable:!0}),Object.defineProperty(n,"currentTask",{get:function(){return j},enumerable:!1,configurable:!0}),n.__load_patch=function(a,i,s){if(void 0===s&&(s=!1),D.hasOwnProperty(a)){var c=!0===e[t("forceDuplicateZoneCheck")];if(!s&&c)throw Error("Already loaded patch: "+a)}else if(!e["__Zone_disable_"+a]){var u="Zone:"+a;r(u),D[a]=i(e,n,O),o(u,u)}},Object.defineProperty(n.prototype,"parent",{get:function(){return this._parent},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),n.prototype.get=function(e){var t=this.getZoneWith(e);if(t)return t._properties[e]},n.prototype.getZoneWith=function(e){for(var t=this;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null},n.prototype.fork=function(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)},n.prototype.wrap=function(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);var n=this._zoneDelegate.intercept(this,e,t),r=this;return function(){return r.runGuarded(n,this,arguments,t)}},n.prototype.run=function(e,t,n,r){C={parent:C,zone:this};try{return this._zoneDelegate.invoke(this,e,t,n,r)}finally{C=C.parent}},n.prototype.runGuarded=function(e,t,n,r){void 0===t&&(t=null),C={parent:C,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,n,r)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{C=C.parent}},n.prototype.runTask=function(e,t,n){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||k).name+"; Execution: "+this.name+")");var r=e,o=e.type,a=e.data,i=void 0===a?{}:a,s=i.isPeriodic,c=void 0!==s&&s,u=i.isRefreshable,l=void 0!==u&&u;if(e.state!==y||o!==Z&&o!==P){var f=e.state!=b;f&&r._transitionTo(b,m);var h=j;j=r,C={parent:C,zone:this};try{o!=P||!e.data||c||l||(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,r,t,n)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{var p=e.state;if(p!==y&&p!==w)if(o==Z||c||l&&p===T)f&&r._transitionTo(m,b,T);else{var v=r._zoneDelegates;this._updateTaskCount(r,-1),f&&r._transitionTo(y,b,y),l&&(r._zoneDelegates=v)}C=C.parent,j=h}}},n.prototype.scheduleTask=function(e){if(e.zone&&e.zone!==this)for(var t=this;t;){if(t===e.zone)throw Error("can not reschedule task to ".concat(this.name," which is descendants of the original zone ").concat(e.zone.name));t=t.parent}e._transitionTo(T,y);var n=[];e._zoneDelegates=n,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(t){throw e._transitionTo(w,T,y),this._zoneDelegate.handleError(this,t),t}return e._zoneDelegates===n&&this._updateTaskCount(e,1),e.state==T&&e._transitionTo(m,T),e},n.prototype.scheduleMicroTask=function(e,t,n,r){return this.scheduleTask(new u(S,e,t,n,r,void 0))},n.prototype.scheduleMacroTask=function(e,t,n,r,o){return this.scheduleTask(new u(P,e,t,n,r,o))},n.prototype.scheduleEventTask=function(e,t,n,r,o){return this.scheduleTask(new u(Z,e,t,n,r,o))},n.prototype.cancelTask=function(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||k).name+"; Execution: "+this.name+")");if(e.state===m||e.state===b){e._transitionTo(E,m,b);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(w,E),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(y,E),e.runCount=-1,e}},n.prototype._updateTaskCount=function(e,t){var n=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(var r=0;r<n.length;r++)n[r]._updateTaskCount(e.type,t)},n.__symbol__=t,n}(),s={name:"",onHasTask:function(e,t,n,r){return e.hasTask(n,r)},onScheduleTask:function(e,t,n,r){return e.scheduleTask(n,r)},onInvokeTask:function(e,t,n,r,o,a){return e.invokeTask(n,r,o,a)},onCancelTask:function(e,t,n,r){return e.cancelTask(n,r)}},c=function(){function e(e,t,n){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this._zone=e,this._parentDelegate=t,this._forkZS=n&&(n&&n.onFork?n:t._forkZS),this._forkDlgt=n&&(n.onFork?t:t._forkDlgt),this._forkCurrZone=n&&(n.onFork?this._zone:t._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:t._interceptZS),this._interceptDlgt=n&&(n.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this._zone:t._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:t._invokeZS),this._invokeDlgt=n&&(n.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this._zone:t._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:t._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this._zone:t._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:t._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this._zone:t._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:t._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this._zone:t._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:t._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this._zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;var r=n&&n.onHasTask;(r||t&&t._hasTaskZS)&&(this._hasTaskZS=r?n:s,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,n.onScheduleTask||(this._scheduleTaskZS=s,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this._zone),n.onInvokeTask||(this._invokeTaskZS=s,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this._zone),n.onCancelTask||(this._cancelTaskZS=s,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this._zone))}return Object.defineProperty(e.prototype,"zone",{get:function(){return this._zone},enumerable:!1,configurable:!0}),e.prototype.fork=function(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new i(e,t)},e.prototype.intercept=function(e,t,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,n):t},e.prototype.invoke=function(e,t,n,r,o){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,n,r,o):t.apply(n,r)},e.prototype.handleError=function(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)},e.prototype.scheduleTask=function(e,t){var n=t;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),(n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t))||(n=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=S)throw new Error("Task is missing scheduleFn.");_(t)}return n},e.prototype.invokeTask=function(e,t,n,r){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,n,r):t.callback.apply(n,r)},e.prototype.cancelTask=function(e,t){var n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");n=t.cancelFn(t)}return n},e.prototype.hasTask=function(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(t){this.handleError(e,t)}},e.prototype._updateTaskCount=function(e,t){var n=this._taskCounts,r=n[e],o=n[e]=r+t;if(o<0)throw new Error("More tasks executed then were scheduled.");0!=r&&0!=o||this.hasTask(this._zone,{microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:e})},e}(),u=function(){function t(n,r,o,a,i,s){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=n,this.source=r,this.data=a,this.scheduleFn=i,this.cancelFn=s,!o)throw new Error("callback is not defined");this.callback=o;var c=this;this.invoke=n===Z&&a&&a.useG?t.invokeTask:function(){return t.invokeTask.call(e,c,this,arguments)}}return t.invokeTask=function(e,t,n){e||(e=this),z++;try{return e.runCount++,e.zone.runTask(e,t,n)}finally{1==z&&g(),z--}},Object.defineProperty(t.prototype,"zone",{get:function(){return this._zone},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),t.prototype.cancelScheduleRequest=function(){this._transitionTo(y,T)},t.prototype._transitionTo=function(e,t,n){if(this._state!==t&&this._state!==n)throw new Error("".concat(this.type," '").concat(this.source,"': can not transition to '").concat(e,"', expecting state '").concat(t,"'").concat(n?" or '"+n+"'":"",", was '").concat(this._state,"'."));this._state=e,e==y&&(this._zoneDelegates=null)},t.prototype.toString=function(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)},t.prototype.toJSON=function(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}},t}(),l=t("setTimeout"),f=t("Promise"),h=t("then"),p=[],v=!1;function d(t){if(a||e[f]&&(a=e[f].resolve(0)),a){var n=a[h];n||(n=a.then),n.call(a,t)}else e[l](t,0)}function _(e){0===z&&0===p.length&&d(g),e&&p.push(e)}function g(){if(!v){for(v=!0;p.length;){var e=p;p=[];for(var t=0;t<e.length;t++){var n=e[t];try{n.zone.runTask(n,null,null)}catch(e){O.onUnhandledError(e)}}}O.microtaskDrainDone(),v=!1}}var k={name:"NO ZONE"},y="notScheduled",T="scheduling",m="scheduled",b="running",E="canceling",w="unknown",S="microTask",P="macroTask",Z="eventTask",D={},O={symbol:t,currentZoneFrame:function(){return C},onUnhandledError:R,microtaskDrainDone:R,scheduleMicroTask:_,showUncaughtError:function(){return!i[t("ignoreConsoleErrorUncaughtError")]},patchEventTarget:function(){return[]},patchOnProperties:R,patchMethod:function(){return R},bindArguments:function(){return[]},patchThen:function(){return R},patchMacroTask:function(){return R},patchEventPrototype:function(){return R},isIEOrEdge:function(){return!1},getGlobalObjects:function(){},ObjectDefineProperty:function(){return R},ObjectGetOwnPropertyDescriptor:function(){},ObjectCreate:function(){},ArraySlice:function(){return[]},patchClass:function(){return R},wrapWithCurrentZone:function(){return R},filterProperties:function(){return[]},attachOriginToPatched:function(){return R},_redefineProperty:function(){return R},patchCallbacks:function(){return R},nativeScheduleMicroTask:d},C={parent:null,zone:new i(null,null)},j=null,z=0;function R(){}return o("Zone","Zone"),i}var r=Object.getOwnPropertyDescriptor,o=Object.defineProperty,a=Object.getPrototypeOf,i=Object.create,s=Array.prototype.slice,c="addEventListener",u="removeEventListener",l=t(c),f=t(u),h="true",p="false",v=t("");function d(e,t){return Zone.current.wrap(e,t)}function _(e,t,n,r,o){return Zone.current.scheduleMacroTask(e,t,n,r,o)}var g=t,k="undefined"!=typeof window,y=k?window:void 0,T=k&&y||globalThis;function m(e,t){for(var n=e.length-1;n>=0;n--)"function"==typeof e[n]&&(e[n]=d(e[n],t+"_"+n));return e}function b(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}var E="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,w=!("nw"in T)&&void 0!==T.process&&"[object process]"===T.process.toString(),S=!w&&!E&&!(!k||!y.HTMLElement),P=void 0!==T.process&&"[object process]"===T.process.toString()&&!E&&!(!k||!y.HTMLElement),Z={},D=g("enable_beforeunload"),O=function(e){if(e=e||T.event){var t=Z[e.type];t||(t=Z[e.type]=g("ON_PROPERTY"+e.type));var n,r=this||e.target||T,o=r[t];return S&&r===y&&"error"===e.type?!0===(n=o&&o.call(this,e.message,e.filename,e.lineno,e.colno,e.error))&&e.preventDefault():(n=o&&o.apply(this,arguments),"beforeunload"===e.type&&T[D]&&"string"==typeof n?e.returnValue=n:null==n||n||e.preventDefault()),n}};function C(e,t,n){var a=r(e,t);if(!a&&n&&r(n,t)&&(a={enumerable:!0,configurable:!0}),a&&a.configurable){var i=g("on"+t+"patched");if(!e.hasOwnProperty(i)||!e[i]){delete a.writable,delete a.value;var s=a.get,c=a.set,u=t.slice(2),l=Z[u];l||(l=Z[u]=g("ON_PROPERTY"+u)),a.set=function(t){var n=this;n||e!==T||(n=T),n&&("function"==typeof n[l]&&n.removeEventListener(u,O),null==c||c.call(n,null),n[l]=t,"function"==typeof t&&n.addEventListener(u,O,!1))},a.get=function(){var n=this;if(n||e!==T||(n=T),!n)return null;var r=n[l];if(r)return r;if(s){var o=s.call(this);if(o)return a.set.call(this,o),"function"==typeof n.removeAttribute&&n.removeAttribute(t),o}return null},o(e,t,a),e[i]=!0}}}function j(e,t,n){if(t)for(var r=0;r<t.length;r++)C(e,"on"+t[r],n);else{var o=[];for(var a in e)"on"==a.slice(0,2)&&o.push(a);for(var i=0;i<o.length;i++)C(e,o[i],n)}}var z=g("originalInstance");function R(e){var t=T[e];if(t){T[g(e)]=t,T[e]=function(){var n=m(arguments,e);switch(n.length){case 0:this[z]=new t;break;case 1:this[z]=new t(n[0]);break;case 2:this[z]=new t(n[0],n[1]);break;case 3:this[z]=new t(n[0],n[1],n[2]);break;case 4:this[z]=new t(n[0],n[1],n[2],n[3]);break;default:throw new Error("Arg list too long.")}},N(T[e],t);var n,r=new t((function(){}));for(n in r)"XMLHttpRequest"===e&&"responseBlob"===n||function(t){"function"==typeof r[t]?T[e].prototype[t]=function(){return this[z][t].apply(this[z],arguments)}:o(T[e].prototype,t,{set:function(n){"function"==typeof n?(this[z][t]=d(n,e+"."+t),N(this[z][t],n)):this[z][t]=n},get:function(){return this[z][t]}})}(n);for(n in t)"prototype"!==n&&t.hasOwnProperty(n)&&(T[e][n]=t[n])}}function I(e,t,n){for(var o=e;o&&!o.hasOwnProperty(t);)o=a(o);!o&&e[t]&&(o=e);var i=g(t),s=null;if(o&&(!(s=o[i])||!o.hasOwnProperty(i))&&(s=o[i]=o[t],b(o&&r(o,t)))){var c=n(s,i,t);o[t]=function(){return c(this,arguments)},N(o[t],s)}return s}function M(e,t,n){var r=null;function o(e){var t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},r.apply(t.target,t.args),e}r=I(e,t,(function(e){return function(t,r){var a=n(t,r);return a.cbIdx>=0&&"function"==typeof r[a.cbIdx]?_(a.name,r[a.cbIdx],a,o):e.apply(t,r)}}))}function N(e,t){e[g("OriginalDelegate")]=t}var A=!1,L=!1;function H(){if(A)return L;A=!0;try{var e=y.navigator.userAgent;-1===e.indexOf("MSIE ")&&-1===e.indexOf("Trident/")&&-1===e.indexOf("Edge/")||(L=!0)}catch(e){}return L}function x(e){return"function"==typeof e}function F(e){return"number"==typeof e}var q={useG:!0},G={},W={},B=new RegExp("^"+v+"(\\w+)(true|false)$"),U=g("propagationStopped");function V(e,t){var n=(t?t(e):e)+p,r=(t?t(e):e)+h,o=v+n,a=v+r;G[e]={},G[e][p]=o,G[e][h]=a}function X(e,t,n,r){var o=r&&r.add||c,i=r&&r.rm||u,s=r&&r.listeners||"eventListeners",l=r&&r.rmAll||"removeAllListeners",f=g(o),d="."+o+":",_="prependListener",k="."+_+":",y=function(e,t,n){if(!e.isRemoved){var r,o=e.callback;"object"==typeof o&&o.handleEvent&&(e.callback=function(e){return o.handleEvent(e)},e.originalDelegate=o);try{e.invoke(e,t,[n])}catch(e){r=e}var a=e.options;return a&&"object"==typeof a&&a.once&&t[i].call(t,n.type,e.originalDelegate?e.originalDelegate:e.callback,a),r}};function T(n,r,o){if(r=r||e.event){var a=n||r.target||e,i=a[G[r.type][o?h:p]];if(i){var s=[];if(1===i.length)(l=y(i[0],a,r))&&s.push(l);else for(var c=i.slice(),u=0;u<c.length&&(!r||!0!==r[U]);u++){var l;(l=y(c[u],a,r))&&s.push(l)}if(1===s.length)throw s[0];var f=function(e){var n=s[e];t.nativeScheduleMicroTask((function(){throw n}))};for(u=0;u<s.length;u++)f(u)}}}var m=function(e){return T(this,e,!1)},b=function(e){return T(this,e,!0)};function E(t,n){if(!t)return!1;var r=!0;n&&void 0!==n.useG&&(r=n.useG);var c=n&&n.vh,u=!0;n&&void 0!==n.chkDup&&(u=n.chkDup);var y=!1;n&&void 0!==n.rt&&(y=n.rt);for(var T=t;T&&!T.hasOwnProperty(o);)T=a(T);if(!T&&t[o]&&(T=t),!T)return!1;if(T[f])return!1;var E,S=n&&n.eventNameToString,P={},Z=T[f]=T[o],D=T[g(i)]=T[i],O=T[g(s)]=T[s],C=T[g(l)]=T[l];n&&n.prepend&&(E=T[g(n.prepend)]=T[n.prepend]);var j=r?function(e){if(!P.isExisting)return Z.call(P.target,P.eventName,P.capture?b:m,P.options)}:function(e){return Z.call(P.target,P.eventName,e.invoke,P.options)},z=r?function(e){if(!e.isRemoved){var t=G[e.eventName],n=void 0;t&&(n=t[e.capture?h:p]);var r=n&&e.target[n];if(r)for(var o=0;o<r.length;o++)if(r[o]===e){r.splice(o,1),e.isRemoved=!0,e.removeAbortListener&&(e.removeAbortListener(),e.removeAbortListener=null),0===r.length&&(e.allRemoved=!0,e.target[n]=null);break}}if(e.allRemoved)return D.call(e.target,e.eventName,e.capture?b:m,e.options)}:function(e){return D.call(e.target,e.eventName,e.invoke,e.options)},R=(null==n?void 0:n.diff)||function(e,t){var n=typeof t;return"function"===n&&e.callback===t||"object"===n&&e.originalDelegate===t},I=Zone[g("UNPATCHED_EVENTS")],M=e[g("PASSIVE_EVENTS")],A=function(t,o,a,i,s,l){return void 0===s&&(s=!1),void 0===l&&(l=!1),function(){var f=this||e,v=arguments[0];n&&n.transferEventName&&(v=n.transferEventName(v));var d=arguments[1];if(!d)return t.apply(this,arguments);if(w&&"uncaughtException"===v)return t.apply(this,arguments);var _=!1;if("function"!=typeof d){if(!d.handleEvent)return t.apply(this,arguments);_=!0}if(!c||c(t,d,f,arguments)){var g=!!M&&-1!==M.indexOf(v),k=function n(e){if("object"==typeof e&&null!==e){var t=__assign({},e);return e.signal&&(t.signal=e.signal),t}return e}(function e(t,n){return n?"boolean"==typeof t?{capture:t,passive:!0}:t?"object"==typeof t&&!1!==t.passive?__assign(__assign({},t),{passive:!0}):t:{passive:!0}:t}(arguments[2],g)),y=null==k?void 0:k.signal;if(!(null==y?void 0:y.aborted)){if(I)for(var T=0;T<I.length;T++)if(v===I[T])return g?t.call(f,v,d,k):t.apply(this,arguments);var m=!!k&&("boolean"==typeof k||k.capture),b=!(!k||"object"!=typeof k)&&k.once,E=Zone.current,Z=G[v];Z||(V(v,S),Z=G[v]);var D,O=Z[m?h:p],C=f[O],j=!1;if(C){if(j=!0,u)for(T=0;T<C.length;T++)if(R(C[T],d))return}else C=f[O]=[];var z=f.constructor.name,N=W[z];N&&(D=N[v]),D||(D=z+o+(S?S(v):v)),P.options=k,b&&(P.options.once=!1),P.target=f,P.capture=m,P.eventName=v,P.isExisting=j;var A=r?q:void 0;A&&(A.taskData=P),y&&(P.options.signal=void 0);var L=E.scheduleEventTask(D,d,A,a,i);if(y){P.options.signal=y;var H=function(){return L.zone.cancelTask(L)};t.call(y,"abort",H,{once:!0}),L.removeAbortListener=function(){return y.removeEventListener("abort",H)}}return P.target=null,A&&(A.taskData=null),b&&(P.options.once=!0),"boolean"!=typeof L.options&&(L.options=k),L.target=f,L.capture=m,L.eventName=v,_&&(L.originalDelegate=d),l?C.unshift(L):C.push(L),s?f:void 0}}}};return T[o]=A(Z,d,j,z,y),E&&(T[_]=A(E,k,(function(e){return E.call(P.target,P.eventName,e.invoke,P.options)}),z,y,!0)),T[i]=function(){var t=this||e,r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));var o=arguments[2],a=!!o&&("boolean"==typeof o||o.capture),i=arguments[1];if(!i)return D.apply(this,arguments);if(!c||c(D,i,t,arguments)){var s,u=G[r];u&&(s=u[a?h:p]);var l=s&&t[s];if(l)for(var f=0;f<l.length;f++){var d=l[f];if(R(d,i))return l.splice(f,1),d.isRemoved=!0,0===l.length&&(d.allRemoved=!0,t[s]=null,a||"string"!=typeof r||(t[v+"ON_PROPERTY"+r]=null)),d.zone.cancelTask(d),y?t:void 0}return D.apply(this,arguments)}},T[s]=function(){var t=this||e,r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));for(var o=[],a=Y(t,S?S(r):r),i=0;i<a.length;i++){var s=a[i];o.push(s.originalDelegate?s.originalDelegate:s.callback)}return o},T[l]=function(){var t=this||e,r=arguments[0];if(r){n&&n.transferEventName&&(r=n.transferEventName(r));var o=G[r];if(o){var a=t[o[p]],s=t[o[h]];if(a){var c=a.slice();for(v=0;v<c.length;v++)this[i].call(this,r,(u=c[v]).originalDelegate?u.originalDelegate:u.callback,u.options)}if(s)for(c=s.slice(),v=0;v<c.length;v++){var u;this[i].call(this,r,(u=c[v]).originalDelegate?u.originalDelegate:u.callback,u.options)}}}else{for(var f=Object.keys(t),v=0;v<f.length;v++){var d=B.exec(f[v]),_=d&&d[1];_&&"removeListener"!==_&&this[l].call(this,_)}this[l].call(this,"removeListener")}if(y)return this},N(T[o],Z),N(T[i],D),C&&N(T[l],C),O&&N(T[s],O),!0}for(var S=[],P=0;P<n.length;P++)S[P]=E(n[P],r);return S}function Y(e,t){if(!t){var n=[];for(var r in e){var o=B.exec(r),a=o&&o[1];if(a&&(!t||a===t)){var i=e[r];if(i)for(var s=0;s<i.length;s++)n.push(i[s])}}return n}var c=G[t];c||(V(t),c=G[t]);var u=e[c[p]],l=e[c[h]];return u?l?u.concat(l):u.slice():l?l.slice():[]}function J(e,t){var n=e.Event;n&&n.prototype&&t.patchMethod(n.prototype,"stopImmediatePropagation",(function(e){return function(t,n){t[U]=!0,e&&e.apply(t,n)}}))}function K(e,t){t.patchMethod(e,"queueMicrotask",(function(e){return function(e,t){Zone.current.scheduleMicroTask("queueMicrotask",t[0])}}))}var $=g("zoneTask");function Q(e,t,n,r){var o=null,a=null;n+=r;var i={};function s(t){var n=t.data;n.args[0]=function(){return t.invoke.apply(this,arguments)};var r=o.apply(e,n.args);return F(r)?n.handleId=r:(n.handle=r,n.isRefreshable=x(r.refresh)),t}function c(t){var n=t.data,r=n.handle;return a.call(e,null!=r?r:n.handleId)}o=I(e,t+=r,(function(n){return function(o,a){var u;if(x(a[0])){var l={isRefreshable:!1,isPeriodic:"Interval"===r,delay:"Timeout"===r||"Interval"===r?a[1]||0:void 0,args:a},f=a[0];a[0]=function e(){try{return f.apply(this,arguments)}finally{var t=l.handle,n=l.handleId;l.isPeriodic||l.isRefreshable||(n?delete i[n]:t&&(t[$]=null))}};var h=_(t,a[0],l,s,c);if(!h)return h;var p=h.data,v=p.handleId,d=p.handle,g=p.isRefreshable,k=p.isPeriodic;if(v)i[v]=h;else if(d&&(d[$]=h,g&&!k)){var y=d.refresh;d.refresh=function(){var e=h.zone,t=h.state;return"notScheduled"===t?(h._state="scheduled",e._updateTaskCount(h,1)):"running"===t&&(h._state="scheduling"),y.call(this)}}return null!==(u=null!=d?d:v)&&void 0!==u?u:h}return n.apply(e,a)}})),a=I(e,n,(function(t){return function(n,r){var o,a=r[0];F(a)?(o=i[a],delete i[a]):(o=null==a?void 0:a[$])?a[$]=null:o=a,(null==o?void 0:o.type)?o.cancelFn&&o.zone.cancelTask(o):t.apply(e,r)}}))}function ee(e,t){if(!Zone[t.symbol("patchEventTarget")]){for(var n=t.getGlobalObjects(),r=n.eventNames,o=n.zoneSymbolEventNames,a=n.TRUE_STR,i=n.FALSE_STR,s=n.ZONE_SYMBOL_PREFIX,c=0;c<r.length;c++){var u=r[c],l=s+(u+i),f=s+(u+a);o[u]={},o[u][i]=l,o[u][a]=f}var h=e.EventTarget;if(h&&h.prototype)return t.patchEventTarget(e,t,[h&&h.prototype]),!0}}function te(e,t,n){if(!n||0===n.length)return t;var r=n.filter((function(t){return t.target===e}));if(0===r.length)return t;var o=r[0].ignoreProperties;return t.filter((function(e){return-1===o.indexOf(e)}))}function ne(e,t,n,r){e&&j(e,te(e,t,n),r)}function re(e){return Object.getOwnPropertyNames(e).filter((function(e){return e.startsWith("on")&&e.length>2})).map((function(e){return e.substring(2)}))}function oe(e,t){if((!w||P)&&!Zone[e.symbol("patchEvents")]){var n=t.__Zone_ignore_on_properties,r=[];if(S){var o=window;r=r.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]),ne(o,re(o),n?n.concat([]):n,a(o))}r=r.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(var i=0;i<r.length;i++){var s=t[r[i]];(null==s?void 0:s.prototype)&&ne(s.prototype,re(s.prototype),n)}}}function ae(e,t,n,r,o){var a=Zone.__symbol__(r);if(!t[a]){var i=t[a]=t[r];t[r]=function(a,s,c){return s&&s.prototype&&o.forEach((function(t){var o="".concat(n,".").concat(r,"::")+t,a=s.prototype;try{if(a.hasOwnProperty(t)){var i=e.ObjectGetOwnPropertyDescriptor(a,t);i&&i.value?(i.value=e.wrapWithCurrentZone(i.value,o),e._redefineProperty(s.prototype,t,i)):a[t]&&(a[t]=e.wrapWithCurrentZone(a[t],o))}else a[t]&&(a[t]=e.wrapWithCurrentZone(a[t],o))}catch(e){}})),i.call(t,a,s,c)},e.attachOriginToPatched(t[r],i)}}var ie=function se(){var e,r=globalThis,o=!0===r[t("forceDuplicateZoneCheck")];if(r.Zone&&(o||"function"!=typeof r.Zone.__symbol__))throw new Error("Zone already loaded.");return null!==(e=r.Zone)&&void 0!==e||(r.Zone=n()),r.Zone}();!function ce(e){(function t(e){e.__load_patch("ZoneAwarePromise",(function(e,t,n){var r=Object.getOwnPropertyDescriptor,o=Object.defineProperty,a=n.symbol,i=[],s=!1!==e[a("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],c=a("Promise"),u=a("then");n.onUnhandledError=function(e){if(n.showUncaughtError()){var t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},n.microtaskDrainDone=function(){for(var e=function(){var e=i.shift();try{e.zone.runGuarded((function(){if(e.throwOriginal)throw e.rejection;throw e}))}catch(e){!function r(e){n.onUnhandledError(e);try{var r=t[l];"function"==typeof r&&r.call(this,e)}catch(e){}}(e)}};i.length;)e()};var l=a("unhandledPromiseRejectionHandler");function f(e){return e&&"function"==typeof e.then}function h(e){return e}function p(e){return z.reject(e)}var v=a("state"),d=a("value"),_=a("finally"),g=a("parentPromiseValue"),k=a("parentPromiseState"),y=null,T=!0,m=!1;function b(e,t){return function(n){try{P(e,t,n)}catch(t){P(e,!1,t)}}}var E=function(){var e=!1;return function t(n){return function(){e||(e=!0,n.apply(null,arguments))}}},w="Promise resolved with itself",S=a("currentTaskTrace");function P(e,r,a){var c=E();if(e===a)throw new TypeError(w);if(e[v]===y){var u=null;try{"object"!=typeof a&&"function"!=typeof a||(u=a&&a.then)}catch(t){return c((function(){P(e,!1,t)}))(),e}if(r!==m&&a instanceof z&&a.hasOwnProperty(v)&&a.hasOwnProperty(d)&&a[v]!==y)D(a),P(e,a[v],a[d]);else if(r!==m&&"function"==typeof u)try{u.call(a,c(b(e,r)),c(b(e,!1)))}catch(t){c((function(){P(e,!1,t)}))()}else{e[v]=r;var l=e[d];if(e[d]=a,e[_]===_&&r===T&&(e[v]=e[k],e[d]=e[g]),r===m&&a instanceof Error){var f=t.currentTask&&t.currentTask.data&&t.currentTask.data.__creationTrace__;f&&o(a,S,{configurable:!0,enumerable:!1,writable:!0,value:f})}for(var h=0;h<l.length;)O(e,l[h++],l[h++],l[h++],l[h++]);if(0==l.length&&r==m){e[v]=0;var p=a;try{throw new Error("Uncaught (in promise): "+function e(t){return t&&t.toString===Object.prototype.toString?(t.constructor&&t.constructor.name||"")+": "+JSON.stringify(t):t?t.toString():Object.prototype.toString.call(t)}(a)+(a&&a.stack?"\n"+a.stack:""))}catch(e){p=e}s&&(p.throwOriginal=!0),p.rejection=a,p.promise=e,p.zone=t.current,p.task=t.currentTask,i.push(p),n.scheduleMicroTask()}}}return e}var Z=a("rejectionHandledHandler");function D(e){if(0===e[v]){try{var n=t[Z];n&&"function"==typeof n&&n.call(this,{rejection:e[d],promise:e})}catch(e){}e[v]=m;for(var r=0;r<i.length;r++)e===i[r].promise&&i.splice(r,1)}}function O(e,t,n,r,o){D(e);var a=e[v],i=a?"function"==typeof r?r:h:"function"==typeof o?o:p;t.scheduleMicroTask("Promise.then",(function(){try{var r=e[d],o=!!n&&_===n[_];o&&(n[g]=r,n[k]=a);var s=t.run(i,void 0,o&&i!==p&&i!==h?[]:[r]);P(n,!0,s)}catch(e){P(n,!1,e)}}),n)}var C=function(){},j=e.AggregateError,z=function(){function e(t){var n=this;if(!(n instanceof e))throw new Error("Must be an instanceof Promise.");n[v]=y,n[d]=[];try{var r=E();t&&t(r(b(n,T)),r(b(n,m)))}catch(e){P(n,!1,e)}}return e.toString=function(){return"function ZoneAwarePromise() { [native code] }"},e.resolve=function(t){return t instanceof e?t:P(new this(null),T,t)},e.reject=function(e){return P(new this(null),m,e)},e.withResolvers=function(){var t={};return t.promise=new e((function(e,n){t.resolve=e,t.reject=n})),t},e.any=function(t){if(!t||"function"!=typeof t[Symbol.iterator])return Promise.reject(new j([],"All promises were rejected"));var n=[],r=0;try{for(var o=0,a=t;o<a.length;o++)r++,n.push(e.resolve(a[o]))}catch(e){return Promise.reject(new j([],"All promises were rejected"))}if(0===r)return Promise.reject(new j([],"All promises were rejected"));var i=!1,s=[];return new e((function(e,t){for(var o=0;o<n.length;o++)n[o].then((function(t){i||(i=!0,e(t))}),(function(e){s.push(e),0===--r&&(i=!0,t(new j(s,"All promises were rejected")))}))}))},e.race=function(e){var t,n,r=new this((function(e,r){t=e,n=r}));function o(e){t(e)}function a(e){n(e)}for(var i=0,s=e;i<s.length;i++){var c=s[i];f(c)||(c=this.resolve(c)),c.then(o,a)}return r},e.all=function(t){return e.allWithCallback(t)},e.allSettled=function(t){return(this&&this.prototype instanceof e?this:e).allWithCallback(t,{thenCallback:function(e){return{status:"fulfilled",value:e}},errorCallback:function(e){return{status:"rejected",reason:e}}})},e.allWithCallback=function(e,t){for(var n,r,o=new this((function(e,t){n=e,r=t})),a=2,i=0,s=[],c=function(e){f(e)||(e=u.resolve(e));var o=i;try{e.then((function(e){s[o]=t?t.thenCallback(e):e,0===--a&&n(s)}),(function(e){t?(s[o]=t.errorCallback(e),0===--a&&n(s)):r(e)}))}catch(e){r(e)}a++,i++},u=this,l=0,h=e;l<h.length;l++)c(h[l]);return 0==(a-=2)&&n(s),o},Object.defineProperty(e.prototype,Symbol.toStringTag,{get:function(){return"Promise"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Symbol.species,{get:function(){return e},enumerable:!1,configurable:!0}),e.prototype.then=function(n,r){var o,a=null===(o=this.constructor)||void 0===o?void 0:o[Symbol.species];a&&"function"==typeof a||(a=this.constructor||e);var i=new a(C),s=t.current;return this[v]==y?this[d].push(s,i,n,r):O(this,s,i,n,r),i},e.prototype.catch=function(e){return this.then(null,e)},e.prototype.finally=function(n){var r,o=null===(r=this.constructor)||void 0===r?void 0:r[Symbol.species];o&&"function"==typeof o||(o=e);var a=new o(C);a[_]=_;var i=t.current;return this[v]==y?this[d].push(i,a,n,n):O(this,i,a,n,n),a},e}();z.resolve=z.resolve,z.reject=z.reject,z.race=z.race,z.all=z.all;var R=e[c]=e.Promise;e.Promise=z;var M=a("thenPatched");function N(e){var t=e.prototype,n=r(t,"then");if(!n||!1!==n.writable&&n.configurable){var o=t.then;t[u]=o,e.prototype.then=function(e,t){var n=this;return new z((function(e,t){o.call(n,e,t)})).then(e,t)},e[M]=!0}}return n.patchThen=N,R&&(N(R),I(e,"fetch",(function(e){return function t(e){return function(t,n){var r=e.apply(t,n);if(r instanceof z)return r;var o=r.constructor;return o[M]||N(o),r}}(e)}))),Promise[t.__symbol__("uncaughtPromiseErrors")]=i,z}))})(e),function n(e){e.__load_patch("toString",(function(e){var t=Function.prototype.toString,n=g("OriginalDelegate"),r=g("Promise"),o=g("Error"),a=function a(){if("function"==typeof this){var i=this[n];if(i)return"function"==typeof i?t.call(i):Object.prototype.toString.call(i);if(this===Promise){var s=e[r];if(s)return t.call(s)}if(this===Error){var c=e[o];if(c)return t.call(c)}}return t.call(this)};a[n]=t,Function.prototype.toString=a;var i=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":i.call(this)}}))}(e),function a(e){e.__load_patch("util",(function(e,t,n){var a=re(e);n.patchOnProperties=j,n.patchMethod=I,n.bindArguments=m,n.patchMacroTask=M;var l=t.__symbol__("BLACK_LISTED_EVENTS"),f=t.__symbol__("UNPATCHED_EVENTS");e[f]&&(e[l]=e[f]),e[l]&&(t[l]=t[f]=e[l]),n.patchEventPrototype=J,n.patchEventTarget=X,n.isIEOrEdge=H,n.ObjectDefineProperty=o,n.ObjectGetOwnPropertyDescriptor=r,n.ObjectCreate=i,n.ArraySlice=s,n.patchClass=R,n.wrapWithCurrentZone=d,n.filterProperties=te,n.attachOriginToPatched=N,n._redefineProperty=Object.defineProperty,n.patchCallbacks=ae,n.getGlobalObjects=function(){return{globalSources:W,zoneSymbolEventNames:G,eventNames:a,isBrowser:S,isMix:P,isNode:w,TRUE_STR:h,FALSE_STR:p,ZONE_SYMBOL_PREFIX:v,ADD_EVENT_LISTENER_STR:c,REMOVE_EVENT_LISTENER_STR:u}}}))}(e)}(ie),function ue(e){e.__load_patch("legacy",(function(t){var n=t[e.__symbol__("legacyPatch")];n&&n()})),e.__load_patch("timers",(function(e){var t="set",n="clear";Q(e,t,n,"Timeout"),Q(e,t,n,"Interval"),Q(e,t,n,"Immediate")})),e.__load_patch("requestAnimationFrame",(function(e){Q(e,"request","cancel","AnimationFrame"),Q(e,"mozRequest","mozCancel","AnimationFrame"),Q(e,"webkitRequest","webkitCancel","AnimationFrame")})),e.__load_patch("blocking",(function(e,t){for(var n=["alert","prompt","confirm"],r=0;r<n.length;r++)I(e,n[r],(function(n,r,o){return function(r,a){return t.current.run(n,e,a,o)}}))})),e.__load_patch("EventTarget",(function(e,t,n){!function r(e,t){t.patchEventPrototype(e,t)}(e,n),ee(e,n);var o=e.XMLHttpRequestEventTarget;o&&o.prototype&&n.patchEventTarget(e,n,[o.prototype])})),e.__load_patch("MutationObserver",(function(e,t,n){R("MutationObserver"),R("WebKitMutationObserver")})),e.__load_patch("IntersectionObserver",(function(e,t,n){R("IntersectionObserver")})),e.__load_patch("FileReader",(function(e,t,n){R("FileReader")})),e.__load_patch("on_property",(function(e,t,n){oe(n,e)})),e.__load_patch("customElements",(function(e,t,n){!function r(e,t){var n=t.getGlobalObjects();(n.isBrowser||n.isMix)&&e.customElements&&"customElements"in e&&t.patchCallbacks(t,e.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"])}(e,n)})),e.__load_patch("XHR",(function(e,t){!function n(e){var n=e.XMLHttpRequest;if(n){var u=n.prototype,h=u[l],p=u[f];if(!h){var v=e.XMLHttpRequestEventTarget;if(v){var d=v.prototype;h=d[l],p=d[f]}}var k="readystatechange",y="scheduled",T=I(u,"open",(function(){return function(e,t){return e[o]=0==t[2],e[s]=t[1],T.apply(e,t)}})),m=g("fetchTaskAborting"),b=g("fetchTaskScheduling"),E=I(u,"send",(function(){return function(e,n){if(!0===t.current[b])return E.apply(e,n);if(e[o])return E.apply(e,n);var r={target:e,url:e[s],isPeriodic:!1,args:n,aborted:!1},a=_("XMLHttpRequest.send",P,r,S,Z);e&&!0===e[c]&&!r.aborted&&a.state===y&&a.invoke()}})),w=I(u,"abort",(function(){return function(e,n){var o=function a(e){return e[r]}(e);if(o&&"string"==typeof o.type){if(null==o.cancelFn||o.data&&o.data.aborted)return;o.zone.cancelTask(o)}else if(!0===t.current[m])return w.apply(e,n)}}))}function S(e){var n=e.data,o=n.target;o[i]=!1,o[c]=!1;var s=o[a];h||(h=o[l],p=o[f]),s&&p.call(o,k,s);var u=o[a]=function(){if(o.readyState===o.DONE)if(!n.aborted&&o[i]&&e.state===y){var r=o[t.__symbol__("loadfalse")];if(0!==o.status&&r&&r.length>0){var a=e.invoke;e.invoke=function(){for(var r=o[t.__symbol__("loadfalse")],i=0;i<r.length;i++)r[i]===e&&r.splice(i,1);n.aborted||e.state!==y||a.call(e)},r.push(e)}else e.invoke()}else n.aborted||!1!==o[i]||(o[c]=!0)};return h.call(o,k,u),o[r]||(o[r]=e),E.apply(o,n.args),o[i]=!0,e}function P(){}function Z(e){var t=e.data;return t.aborted=!0,w.apply(t.target,t.args)}}(e);var r=g("xhrTask"),o=g("xhrSync"),a=g("xhrListener"),i=g("xhrScheduled"),s=g("xhrURL"),c=g("xhrErrorBeforeScheduled")})),e.__load_patch("geolocation",(function(e){e.navigator&&e.navigator.geolocation&&function t(e,n){for(var o=e.constructor.name,a=function(t){var a=n[t],i=e[a];if(i){if(!b(r(e,a)))return"continue";e[a]=function(e){var t=function(){return e.apply(this,m(arguments,o+"."+a))};return N(t,e),t}(i)}},i=0;i<n.length;i++)a(i)}(e.navigator.geolocation,["getCurrentPosition","watchPosition"])})),e.__load_patch("PromiseRejectionEvent",(function(e,t){function n(t){return function(n){Y(e,t).forEach((function(r){var o=e.PromiseRejectionEvent;if(o){var a=new o(t,{promise:n.promise,reason:n.rejection});r.invoke(a)}}))}}e.PromiseRejectionEvent&&(t[g("unhandledPromiseRejectionHandler")]=n("unhandledrejection"),t[g("rejectionHandledHandler")]=n("rejectionhandled"))})),e.__load_patch("queueMicrotask",(function(e,t,n){K(e,n)}))}(ie)}));