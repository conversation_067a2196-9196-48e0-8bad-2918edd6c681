{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n  _listeners = [];\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id, name) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener) {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter(registered => {\n        return listener !== registered;\n      });\n    };\n  }\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n  static ɵfac = function UniqueSelectionDispatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UniqueSelectionDispatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: UniqueSelectionDispatcher,\n    factory: UniqueSelectionDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UniqueSelectionDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { UniqueSelectionDispatcher as U };", "map": {"version": 3, "names": ["i0", "Injectable", "UniqueSelectionDispatcher", "_listeners", "notify", "id", "name", "listener", "listen", "push", "filter", "registered", "ngOnDestroy", "ɵfac", "UniqueSelectionDispatcher_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "U"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/cdk/fesm2022/unique-selection-dispatcher-Cewa_Eg3.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n    _listeners = [];\n    /**\n     * Notify other items that selection for the given name has been set.\n     * @param id ID of the item.\n     * @param name Name of the item.\n     */\n    notify(id, name) {\n        for (let listener of this._listeners) {\n            listener(id, name);\n        }\n    }\n    /**\n     * Listen for future changes to item selection.\n     * @return Function used to deregister listener\n     */\n    listen(listener) {\n        this._listeners.push(listener);\n        return () => {\n            this._listeners = this._listeners.filter((registered) => {\n                return listener !== registered;\n            });\n        };\n    }\n    ngOnDestroy() {\n        this._listeners = [];\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: UniqueSelectionDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: UniqueSelectionDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: UniqueSelectionDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nexport { UniqueSelectionDispatcher as U };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BC,UAAU,GAAG,EAAE;EACf;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAACC,EAAE,EAAEC,IAAI,EAAE;IACb,KAAK,IAAIC,QAAQ,IAAI,IAAI,CAACJ,UAAU,EAAE;MAClCI,QAAQ,CAACF,EAAE,EAAEC,IAAI,CAAC;IACtB;EACJ;EACA;AACJ;AACA;AACA;EACIE,MAAMA,CAACD,QAAQ,EAAE;IACb,IAAI,CAACJ,UAAU,CAACM,IAAI,CAACF,QAAQ,CAAC;IAC9B,OAAO,MAAM;MACT,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACA,UAAU,CAACO,MAAM,CAAEC,UAAU,IAAK;QACrD,OAAOJ,QAAQ,KAAKI,UAAU;MAClC,CAAC,CAAC;IACN,CAAC;EACL;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACT,UAAU,GAAG,EAAE;EACxB;EACA,OAAOU,IAAI,YAAAC,kCAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFb,yBAAyB;EAAA;EAC5H,OAAOc,KAAK,kBAD6EhB,EAAE,CAAAiB,kBAAA;IAAAC,KAAA,EACYhB,yBAAyB;IAAAiB,OAAA,EAAzBjB,yBAAyB,CAAAW,IAAA;IAAAO,UAAA,EAAc;EAAM;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FrB,EAAE,CAAAsB,iBAAA,CAGJpB,yBAAyB,EAAc,CAAC;IACvHqB,IAAI,EAAEtB,UAAU;IAChBuB,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,SAASlB,yBAAyB,IAAIuB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}