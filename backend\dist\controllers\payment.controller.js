"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentWebhookController = exports.PaymentController = void 0;
const tslib_1 = require("tslib");
const authentication_1 = require("@loopback/authentication");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const security_1 = require("@loopback/security");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let PaymentController = class PaymentController {
    constructor(currentUserProfile, paymentRepository, paymentService) {
        this.currentUserProfile = currentUserProfile;
        this.paymentRepository = paymentRepository;
        this.paymentService = paymentService;
    }
    async createOrder(request) {
        if (!this.paymentService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('Payment service is not available');
        }
        const userId = this.currentUserProfile[security_1.securityId];
        const order = await this.paymentService.createOrder(request.amount, request.currency, userId, request.description);
        return {
            ...order,
            key: process.env.RAZORPAY_KEY_ID || 'your_key_id',
        };
    }
    async verifyPayment(request) {
        if (!this.paymentService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('Payment service is not available');
        }
        try {
            const isValid = await this.paymentService.verifyPayment(request.orderId, request.paymentId, request.signature);
            if (isValid) {
                return {
                    success: true,
                    message: 'Payment verified successfully',
                };
            }
            else {
                return {
                    success: false,
                    message: 'Payment verification failed',
                };
            }
        }
        catch (error) {
            throw new rest_1.HttpErrors.BadRequest('Payment verification failed');
        }
    }
    async getPaymentStatus(orderId) {
        if (!this.paymentService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('Payment service is not available');
        }
        const payment = await this.paymentService.getPaymentStatus(orderId);
        // Ensure user can only access their own payments
        if (payment && payment.userId !== this.currentUserProfile[security_1.securityId]) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        return { payment };
    }
    async getMyPayments() {
        if (!this.paymentService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('Payment service is not available');
        }
        const userId = this.currentUserProfile[security_1.securityId];
        const payments = await this.paymentService.getUserPayments(userId);
        return { payments };
    }
    async refundPayment(request) {
        if (!this.paymentService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('Payment service is not available');
        }
        try {
            // Verify the payment belongs to the current user
            const payment = await this.paymentRepository.findOne({
                where: { razorpayPaymentId: request.paymentId },
            });
            if (!payment || payment.userId !== this.currentUserProfile[security_1.securityId]) {
                throw new rest_1.HttpErrors.Forbidden('Access denied');
            }
            const success = await this.paymentService.refundPayment(request.paymentId, request.amount);
            return {
                success,
                message: success ? 'Refund processed successfully' : 'Refund processing failed',
            };
        }
        catch (error) {
            throw new rest_1.HttpErrors.BadRequest('Refund processing failed');
        }
    }
};
exports.PaymentController = PaymentController;
tslib_1.__decorate([
    (0, rest_1.post)('/payments/create-order'),
    (0, rest_1.response)(200, {
        description: 'Create payment order',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        orderId: { type: 'string' },
                        amount: { type: 'number' },
                        currency: { type: 'string' },
                        key: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['amount', 'currency'],
                    properties: {
                        amount: { type: 'number', minimum: 1, maximum: 1000000 },
                        currency: { type: 'string', enum: ['INR', 'USD'] },
                        description: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], PaymentController.prototype, "createOrder", null);
tslib_1.__decorate([
    (0, rest_1.post)('/payments/verify'),
    (0, rest_1.response)(200, {
        description: 'Verify payment',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean' },
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['orderId', 'paymentId', 'signature'],
                    properties: {
                        orderId: { type: 'string' },
                        paymentId: { type: 'string' },
                        signature: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], PaymentController.prototype, "verifyPayment", null);
tslib_1.__decorate([
    (0, rest_1.get)('/payments/status/{orderId}'),
    (0, rest_1.response)(200, {
        description: 'Get payment status',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        payment: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                status: { type: 'string' },
                                amount: { type: 'number' },
                                currency: { type: 'string' },
                                createdAt: { type: 'string' },
                                paidAt: { type: 'string' },
                            },
                        },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('orderId')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String]),
    tslib_1.__metadata("design:returntype", Promise)
], PaymentController.prototype, "getPaymentStatus", null);
tslib_1.__decorate([
    (0, rest_1.get)('/payments/my-payments'),
    (0, rest_1.response)(200, {
        description: 'Get user payments',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        payments: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    status: { type: 'string' },
                                    amount: { type: 'number' },
                                    currency: { type: 'string' },
                                    description: { type: 'string' },
                                    createdAt: { type: 'string' },
                                    paidAt: { type: 'string' },
                                },
                            },
                        },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], PaymentController.prototype, "getMyPayments", null);
tslib_1.__decorate([
    (0, rest_1.post)('/payments/refund'),
    (0, rest_1.response)(200, {
        description: 'Refund payment',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean' },
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['paymentId'],
                    properties: {
                        paymentId: { type: 'string' },
                        amount: { type: 'number' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], PaymentController.prototype, "refundPayment", null);
exports.PaymentController = PaymentController = tslib_1.__decorate([
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.PaymentRepository)),
    tslib_1.__param(2, (0, core_1.inject)('services.PaymentService', { optional: true })),
    tslib_1.__metadata("design:paramtypes", [Object, repositories_1.PaymentRepository,
        services_1.PaymentService])
], PaymentController);
// Webhook controller (separate from authenticated routes)
let PaymentWebhookController = class PaymentWebhookController {
    constructor(paymentService) {
        this.paymentService = paymentService;
    }
    async handleWebhook(payload, signature) {
        try {
            await this.paymentService.handleWebhook(payload, signature);
            return { status: 'success' };
        }
        catch (error) {
            throw new rest_1.HttpErrors.BadRequest('Webhook processing failed');
        }
    }
};
exports.PaymentWebhookController = PaymentWebhookController;
tslib_1.__decorate([
    (0, rest_1.post)('/payments/webhook'),
    (0, rest_1.response)(200, {
        description: 'Payment webhook',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        status: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: { type: 'object' },
            },
        },
    })),
    tslib_1.__param(1, rest_1.param.header.string('x-razorpay-signature')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, String]),
    tslib_1.__metadata("design:returntype", Promise)
], PaymentWebhookController.prototype, "handleWebhook", null);
exports.PaymentWebhookController = PaymentWebhookController = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)('services.PaymentService')),
    tslib_1.__metadata("design:paramtypes", [services_1.PaymentService])
], PaymentWebhookController);
//# sourceMappingURL=payment.controller.js.map