{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction RegisterComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.passwordStrength, \"%\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getPasswordStrengthColor());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getPasswordStrengthColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.passwordStrengthText);\n  }\n}\nfunction RegisterComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldError(\"acceptTerms\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_spinner_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 33);\n  }\n}\nfunction RegisterComponent_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Account\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RegisterComponent {\n  constructor(formBuilder, authService, router, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.loading = false;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.passwordStrength = 0;\n    this.passwordStrengthText = '';\n    this.registerForm = this.formBuilder.group({\n      firstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      lastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.pattern(/^\\+?[1-9]\\d{1,14}$/)]],\n      password: ['', [Validators.required, Validators.minLength(8), this.passwordValidator]],\n      confirmPassword: ['', [Validators.required]],\n      acceptTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  ngOnInit() {\n    if (this.authService.isAuthenticated) {\n      this.router.navigate(['/dashboard']);\n    }\n    this.registerForm.get('password')?.valueChanges.subscribe(password => {\n      this.updatePasswordStrength(password);\n    });\n  }\n  onSubmit() {\n    if (this.registerForm.invalid) {\n      this.markFormGroupTouched(this.registerForm);\n      return;\n    }\n    this.loading = true;\n    const userData = this.registerForm.value;\n    this.authService.register(userData).subscribe({\n      next: response => {\n        this.snackBar.open('Registration successful! Please check your email for verification.', 'Close', {\n          duration: 8000\n        });\n        this.router.navigate(['/auth/login'], {\n          queryParams: {\n            message: 'Please verify your email before logging in.'\n          }\n        });\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Registration failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  getFieldError(fieldName) {\n    const field = this.registerForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${this.getFieldLabel(fieldName)} is required`;\n      if (field.errors['email']) return 'Please enter a valid email address';\n      if (field.errors['minlength']) return `${this.getFieldLabel(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['maxlength']) return `${this.getFieldLabel(fieldName)} must not exceed ${field.errors['maxlength'].requiredLength} characters`;\n      if (field.errors['pattern']) {\n        if (fieldName === 'phone') return 'Please enter a valid phone number';\n        return 'Please enter a valid format';\n      }\n      if (field.errors['passwordStrength']) return field.errors['passwordStrength'];\n      if (field.errors['passwordMismatch']) return 'Passwords do not match';\n      if (field.errors['requiredTrue']) return 'You must accept the terms and conditions';\n    }\n    return '';\n  }\n  getFieldLabel(fieldName) {\n    const labels = {\n      firstName: 'First name',\n      lastName: 'Last name',\n      email: 'Email',\n      phone: 'Phone number',\n      password: 'Password',\n      confirmPassword: 'Confirm password'\n    };\n    return labels[fieldName] || fieldName;\n  }\n  passwordValidator(control) {\n    const password = control.value;\n    if (!password) return null;\n    const errors = [];\n    if (password.length < 8) {\n      errors.push('at least 8 characters');\n    }\n    if (!/[A-Z]/.test(password)) {\n      errors.push('one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n      errors.push('one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n      errors.push('one number');\n    }\n    if (!/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)) {\n      errors.push('one special character');\n    }\n    if (errors.length > 0) {\n      return {\n        passwordStrength: `Password must contain ${errors.join(', ')}`\n      };\n    }\n    return null;\n  }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    if (!password || !confirmPassword) return null;\n    if (password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n      return {\n        passwordMismatch: true\n      };\n    }\n    if (confirmPassword.errors?.['passwordMismatch']) {\n      delete confirmPassword.errors['passwordMismatch'];\n      if (Object.keys(confirmPassword.errors).length === 0) {\n        confirmPassword.setErrors(null);\n      }\n    }\n    return null;\n  }\n  updatePasswordStrength(password) {\n    if (!password) {\n      this.passwordStrength = 0;\n      this.passwordStrengthText = '';\n      return;\n    }\n    let strength = 0;\n    const checks = [password.length >= 8, /[A-Z]/.test(password), /[a-z]/.test(password), /\\d/.test(password), /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)];\n    strength = checks.filter(check => check).length;\n    this.passwordStrength = strength / 5 * 100;\n    const strengthTexts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];\n    this.passwordStrengthText = strengthTexts[strength - 1] || 'Very Weak';\n  }\n  getPasswordStrengthColor() {\n    if (this.passwordStrength < 20) return 'warn';\n    if (this.passwordStrength < 40) return 'accent';\n    if (this.passwordStrength < 60) return 'primary';\n    if (this.passwordStrength < 80) return 'primary';\n    return 'primary';\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  static #_ = this.ɵfac = function RegisterComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RegisterComponent,\n    selectors: [[\"app-register\"]],\n    standalone: false,\n    decls: 84,\n    vars: 16,\n    consts: [[1, \"auth-container\"], [1, \"auth-card\", \"fade-in\"], [1, \"auth-header\"], [1, \"security-badge\"], [1, \"auth-content\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"name-row\"], [\"appearance\", \"outline\", 1, \"form-field\", \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"autocomplete\", \"given-name\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"type\", \"tel\", \"formControlName\", \"phone\", \"placeholder\", \"+1234567890\", \"autocomplete\", \"tel\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"class\", \"password-strength\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [1, \"checkbox-field\"], [\"formControlName\", \"acceptTerms\"], [\"href\", \"/terms\", \"target\", \"_blank\"], [\"href\", \"/privacy\", \"target\", \"_blank\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\", \"mt-3\"], [\"routerLink\", \"/auth/login\", 1, \"text-primary\"], [1, \"password-strength\"], [1, \"strength-bar\"], [1, \"strength-fill\", 3, \"ngClass\"], [1, \"strength-text\", 3, \"ngClass\"], [1, \"error-message\"], [\"diameter\", \"20\"]],\n    template: function RegisterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n        i0.ɵɵtext(4, \"Create Account\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\");\n        i0.ɵɵtext(6, \"Join our secure platform today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-icon\");\n        i0.ɵɵtext(9, \"verified_user\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(10, \" Secure Registration \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 4)(12, \"form\", 5);\n        i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_12_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(13, \"div\", 6)(14, \"mat-form-field\", 7)(15, \"mat-label\");\n        i0.ɵɵtext(16, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(17, \"input\", 8);\n        i0.ɵɵelementStart(18, \"mat-icon\", 9);\n        i0.ɵɵtext(19, \"person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"mat-error\");\n        i0.ɵɵtext(21);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"mat-form-field\", 7)(23, \"mat-label\");\n        i0.ɵɵtext(24, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(25, \"input\", 10);\n        i0.ɵɵelementStart(26, \"mat-icon\", 9);\n        i0.ɵɵtext(27, \"person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"mat-error\");\n        i0.ɵɵtext(29);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(30, \"mat-form-field\", 11)(31, \"mat-label\");\n        i0.ɵɵtext(32, \"Email Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(33, \"input\", 12);\n        i0.ɵɵelementStart(34, \"mat-icon\", 9);\n        i0.ɵɵtext(35, \"email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"mat-error\");\n        i0.ɵɵtext(37);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"mat-form-field\", 11)(39, \"mat-label\");\n        i0.ɵɵtext(40, \"Phone Number (Optional)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(41, \"input\", 13);\n        i0.ɵɵelementStart(42, \"mat-icon\", 9);\n        i0.ɵɵtext(43, \"phone\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"mat-error\");\n        i0.ɵɵtext(45);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(46, \"mat-hint\");\n        i0.ɵɵtext(47, \"For SMS verification and 2FA\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(48, \"mat-form-field\", 11)(49, \"mat-label\");\n        i0.ɵɵtext(50, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(51, \"input\", 14);\n        i0.ɵɵelementStart(52, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_52_listener() {\n          return ctx.hidePassword = !ctx.hidePassword;\n        });\n        i0.ɵɵelementStart(53, \"mat-icon\");\n        i0.ɵɵtext(54);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(55, \"mat-error\");\n        i0.ɵɵtext(56);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(57, RegisterComponent_div_57_Template, 5, 5, \"div\", 16);\n        i0.ɵɵelementStart(58, \"mat-form-field\", 11)(59, \"mat-label\");\n        i0.ɵɵtext(60, \"Confirm Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(61, \"input\", 17);\n        i0.ɵɵelementStart(62, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_62_listener() {\n          return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n        });\n        i0.ɵɵelementStart(63, \"mat-icon\");\n        i0.ɵɵtext(64);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(65, \"mat-error\");\n        i0.ɵɵtext(66);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(67, \"div\", 18)(68, \"mat-checkbox\", 19);\n        i0.ɵɵtext(69, \" I agree to the \");\n        i0.ɵɵelementStart(70, \"a\", 20);\n        i0.ɵɵtext(71, \"Terms of Service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(72, \" and \");\n        i0.ɵɵelementStart(73, \"a\", 21);\n        i0.ɵɵtext(74, \"Privacy Policy\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(75, RegisterComponent_div_75_Template, 2, 1, \"div\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(76, \"button\", 23);\n        i0.ɵɵtemplate(77, RegisterComponent_mat_spinner_77_Template, 1, 0, \"mat-spinner\", 24)(78, RegisterComponent_span_78_Template, 2, 0, \"span\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(79, \"div\", 26)(80, \"span\");\n        i0.ɵɵtext(81, \"Already have an account? \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(82, \"a\", 27);\n        i0.ɵɵtext(83, \"Sign In\");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        let tmp_8_0;\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"firstName\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"lastName\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"email\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"phone\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"password\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.value);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"confirmPassword\"));\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", ctx.getFieldError(\"acceptTerms\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"disabled\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i6.MatFormField, i6.MatLabel, i6.MatHint, i6.MatError, i6.MatSuffix, i7.MatInput, i8.MatButton, i8.MatIconButton, i9.MatIcon, i10.MatCheckbox, i11.MatProgressSpinner],\n    styles: [\".name-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.name-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.password-strength[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 4px;\\n  background: #e0e0e0;\\n  border-radius: 2px;\\n  overflow: hidden;\\n  margin-bottom: 0.5rem;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: width 0.3s ease, background-color 0.3s ease;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.warn[_ngcontent-%COMP%] {\\n  background: #f44336;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.accent[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.primary[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-text.warn[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-text.accent[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-text.primary[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.checkbox-field[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0;\\n}\\n.checkbox-field[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.checkbox-field[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  text-decoration: none;\\n}\\n.checkbox-field[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n@media (max-width: 768px) {\\n  .name-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9hdXRoL3JlZ2lzdGVyL3JlZ2lzdGVyLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vTW9kdWxhciUyMGJhY2tlbmQlMjBzZWN1cmUlMjB1c2VyJTIwc3lzdGVtJTIwYW5kJTIwcGF5bWVudC9mcm9udGVuZC9zcmMvYXBwL2NvbXBvbmVudHMvYXV0aC9yZWdpc3Rlci9yZWdpc3Rlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFQTtFQUNFLGFBQUE7RUFDQSxTQUFBO0FDREY7QURHRTtFQUNFLE9BQUE7QUNESjs7QURLQTtFQUNFLG1CQUFBO0FDRkY7QURJRTtFQUNFLFdBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7QUNGSjtBRElJO0VBQ0UsWUFBQTtFQUNBLHVEQUFBO0FDRk47QURJTTtFQUNFLG1CQUFBO0FDRlI7QURLTTtFQUNFLG1CQUFBO0FDSFI7QURNTTtFQUNFLG1CQUFBO0FDSlI7QURTRTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7QUNQSjtBRFNJO0VBQ0UsY0FBQTtBQ1BOO0FEVUk7RUFDRSxjQUFBO0FDUk47QURXSTtFQUNFLGNBQUE7QUNUTjs7QURjQTtFQUNFLGdCQUFBO0FDWEY7QURhRTtFQUNFLG1CQUFBO0FDWEo7QURjRTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtBQ1pKO0FEY0k7RUFDRSwwQkFBQTtBQ1pOOztBRGlCQTtFQUNFO0lBQ0Usc0JBQUE7SUFDQSxNQUFBO0VDZEY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi8vIFVzZSBnbG9iYWwgc3R5bGVzXG5cbi5uYW1lLXJvdyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMXJlbTtcbiAgXG4gIC5oYWxmLXdpZHRoIHtcbiAgICBmbGV4OiAxO1xuICB9XG59XG5cbi5wYXNzd29yZC1zdHJlbmd0aCB7XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG4gIFxuICAuc3RyZW5ndGgtYmFyIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBoZWlnaHQ6IDRweDtcbiAgICBiYWNrZ3JvdW5kOiAjZTBlMGUwO1xuICAgIGJvcmRlci1yYWRpdXM6IDJweDtcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiAgICBcbiAgICAuc3RyZW5ndGgtZmlsbCB7XG4gICAgICBoZWlnaHQ6IDEwMCU7XG4gICAgICB0cmFuc2l0aW9uOiB3aWR0aCAwLjNzIGVhc2UsIGJhY2tncm91bmQtY29sb3IgMC4zcyBlYXNlO1xuICAgICAgXG4gICAgICAmLndhcm4ge1xuICAgICAgICBiYWNrZ3JvdW5kOiAjZjQ0MzM2O1xuICAgICAgfVxuICAgICAgXG4gICAgICAmLmFjY2VudCB7XG4gICAgICAgIGJhY2tncm91bmQ6ICNmZjk4MDA7XG4gICAgICB9XG4gICAgICBcbiAgICAgICYucHJpbWFyeSB7XG4gICAgICAgIGJhY2tncm91bmQ6ICM0Y2FmNTA7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIFxuICAuc3RyZW5ndGgtdGV4dCB7XG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIFxuICAgICYud2FybiB7XG4gICAgICBjb2xvcjogI2Y0NDMzNjtcbiAgICB9XG4gICAgXG4gICAgJi5hY2NlbnQge1xuICAgICAgY29sb3I6ICNmZjk4MDA7XG4gICAgfVxuICAgIFxuICAgICYucHJpbWFyeSB7XG4gICAgICBjb2xvcjogIzRjYWY1MDtcbiAgICB9XG4gIH1cbn1cblxuLmNoZWNrYm94LWZpZWxkIHtcbiAgbWFyZ2luOiAxLjVyZW0gMDtcbiAgXG4gIG1hdC1jaGVja2JveCB7XG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgfVxuICBcbiAgYSB7XG4gICAgY29sb3I6ICMzZjUxYjU7XG4gICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgIFxuICAgICY6aG92ZXIge1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG4gICAgfVxuICB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAubmFtZS1yb3cge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiAwO1xuICB9XG59XG4iLCIubmFtZS1yb3cge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDFyZW07XG59XG4ubmFtZS1yb3cgLmhhbGYtd2lkdGgge1xuICBmbGV4OiAxO1xufVxuXG4ucGFzc3dvcmQtc3RyZW5ndGgge1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xufVxuLnBhc3N3b3JkLXN0cmVuZ3RoIC5zdHJlbmd0aC1iYXIge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiA0cHg7XG4gIGJhY2tncm91bmQ6ICNlMGUwZTA7XG4gIGJvcmRlci1yYWRpdXM6IDJweDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xufVxuLnBhc3N3b3JkLXN0cmVuZ3RoIC5zdHJlbmd0aC1iYXIgLnN0cmVuZ3RoLWZpbGwge1xuICBoZWlnaHQ6IDEwMCU7XG4gIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZSwgYmFja2dyb3VuZC1jb2xvciAwLjNzIGVhc2U7XG59XG4ucGFzc3dvcmQtc3RyZW5ndGggLnN0cmVuZ3RoLWJhciAuc3RyZW5ndGgtZmlsbC53YXJuIHtcbiAgYmFja2dyb3VuZDogI2Y0NDMzNjtcbn1cbi5wYXNzd29yZC1zdHJlbmd0aCAuc3RyZW5ndGgtYmFyIC5zdHJlbmd0aC1maWxsLmFjY2VudCB7XG4gIGJhY2tncm91bmQ6ICNmZjk4MDA7XG59XG4ucGFzc3dvcmQtc3RyZW5ndGggLnN0cmVuZ3RoLWJhciAuc3RyZW5ndGgtZmlsbC5wcmltYXJ5IHtcbiAgYmFja2dyb3VuZDogIzRjYWY1MDtcbn1cbi5wYXNzd29yZC1zdHJlbmd0aCAuc3RyZW5ndGgtdGV4dCB7XG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG59XG4ucGFzc3dvcmQtc3RyZW5ndGggLnN0cmVuZ3RoLXRleHQud2FybiB7XG4gIGNvbG9yOiAjZjQ0MzM2O1xufVxuLnBhc3N3b3JkLXN0cmVuZ3RoIC5zdHJlbmd0aC10ZXh0LmFjY2VudCB7XG4gIGNvbG9yOiAjZmY5ODAwO1xufVxuLnBhc3N3b3JkLXN0cmVuZ3RoIC5zdHJlbmd0aC10ZXh0LnByaW1hcnkge1xuICBjb2xvcjogIzRjYWY1MDtcbn1cblxuLmNoZWNrYm94LWZpZWxkIHtcbiAgbWFyZ2luOiAxLjVyZW0gMDtcbn1cbi5jaGVja2JveC1maWVsZCBtYXQtY2hlY2tib3gge1xuICBmb250LXNpemU6IDAuODc1cmVtO1xufVxuLmNoZWNrYm94LWZpZWxkIGEge1xuICBjb2xvcjogIzNmNTFiNTtcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xufVxuLmNoZWNrYm94LWZpZWxkIGE6aG92ZXIge1xuICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5uYW1lLXJvdyB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBnYXA6IDA7XG4gIH1cbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵstyleProp", "ctx_r0", "passwordStrength", "ɵɵproperty", "getPasswordStrengthColor", "ɵɵtextInterpolate", "passwordStrengthText", "ɵɵtextInterpolate1", "getFieldError", "RegisterComponent", "constructor", "formBuilder", "authService", "router", "snackBar", "loading", "hidePassword", "hideConfirmPassword", "registerForm", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "lastName", "email", "phone", "pattern", "password", "passwordValidator", "confirmPassword", "acceptTerms", "requiredTrue", "validators", "passwordMatchValidator", "ngOnInit", "isAuthenticated", "navigate", "get", "valueChanges", "subscribe", "updatePasswordStrength", "onSubmit", "invalid", "markFormGroupTouched", "userData", "value", "register", "next", "response", "open", "duration", "queryParams", "message", "error", "fieldName", "field", "errors", "touched", "getFieldLabel", "<PERSON><PERSON><PERSON><PERSON>", "labels", "control", "length", "push", "test", "join", "form", "setErrors", "passwordMismatch", "Object", "keys", "strength", "checks", "filter", "check", "strengthTexts", "formGroup", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_12_listener", "RegisterComponent_Template_button_click_52_listener", "ɵɵtemplate", "RegisterComponent_div_57_Template", "RegisterComponent_Template_button_click_62_listener", "RegisterComponent_div_75_Template", "RegisterComponent_mat_spinner_77_Template", "RegisterComponent_span_78_Template", "tmp_8_0"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\register\\register.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\register\\register.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../../services/auth.service';\nimport { UserRegistration } from '../../../models/user.model';\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.scss'],\n  standalone: false\n})\nexport class RegisterComponent implements OnInit {\n  registerForm: FormGroup;\n  loading = false;\n  hidePassword = true;\n  hideConfirmPassword = true;\n  passwordStrength = 0;\n  passwordStrengthText = '';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {\n    this.registerForm = this.formBuilder.group({\n      firstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      lastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.pattern(/^\\+?[1-9]\\d{1,14}$/)]],\n      password: ['', [Validators.required, Validators.minLength(8), this.passwordValidator]],\n      confirmPassword: ['', [Validators.required]],\n      acceptTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n\n  ngOnInit(): void {\n    if (this.authService.isAuthenticated) {\n      this.router.navigate(['/dashboard']);\n    }\n\n    this.registerForm.get('password')?.valueChanges.subscribe(password => {\n      this.updatePasswordStrength(password);\n    });\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.invalid) {\n      this.markFormGroupTouched(this.registerForm);\n      return;\n    }\n\n    this.loading = true;\n    const userData: UserRegistration = this.registerForm.value;\n\n    this.authService.register(userData).subscribe({\n      next: (response) => {\n        this.snackBar.open(\n          'Registration successful! Please check your email for verification.',\n          'Close',\n          { duration: 8000 }\n        );\n        this.router.navigate(['/auth/login'], {\n          queryParams: { message: 'Please verify your email before logging in.' }\n        });\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Registration failed', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.registerForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${this.getFieldLabel(fieldName)} is required`;\n      if (field.errors['email']) return 'Please enter a valid email address';\n      if (field.errors['minlength']) return `${this.getFieldLabel(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['maxlength']) return `${this.getFieldLabel(fieldName)} must not exceed ${field.errors['maxlength'].requiredLength} characters`;\n      if (field.errors['pattern']) {\n        if (fieldName === 'phone') return 'Please enter a valid phone number';\n        return 'Please enter a valid format';\n      }\n      if (field.errors['passwordStrength']) return field.errors['passwordStrength'];\n      if (field.errors['passwordMismatch']) return 'Passwords do not match';\n      if (field.errors['requiredTrue']) return 'You must accept the terms and conditions';\n    }\n    return '';\n  }\n\n  private getFieldLabel(fieldName: string): string {\n    const labels: { [key: string]: string } = {\n      firstName: 'First name',\n      lastName: 'Last name',\n      email: 'Email',\n      phone: 'Phone number',\n      password: 'Password',\n      confirmPassword: 'Confirm password'\n    };\n    return labels[fieldName] || fieldName;\n  }\n\n  private passwordValidator(control: AbstractControl): { [key: string]: any } | null {\n    const password = control.value;\n    if (!password) return null;\n\n    const errors: string[] = [];\n\n    if (password.length < 8) {\n      errors.push('at least 8 characters');\n    }\n    if (!/[A-Z]/.test(password)) {\n      errors.push('one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n      errors.push('one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n      errors.push('one number');\n    }\n    if (!/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)) {\n      errors.push('one special character');\n    }\n\n    if (errors.length > 0) {\n      return { passwordStrength: `Password must contain ${errors.join(', ')}` };\n    }\n\n    return null;\n  }\n\n  private passwordMatchValidator(form: AbstractControl): { [key: string]: any } | null {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n\n    if (!password || !confirmPassword) return null;\n\n    if (password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n      return { passwordMismatch: true };\n    }\n\n    if (confirmPassword.errors?.['passwordMismatch']) {\n      delete confirmPassword.errors['passwordMismatch'];\n      if (Object.keys(confirmPassword.errors).length === 0) {\n        confirmPassword.setErrors(null);\n      }\n    }\n\n    return null;\n  }\n\n  private updatePasswordStrength(password: string): void {\n    if (!password) {\n      this.passwordStrength = 0;\n      this.passwordStrengthText = '';\n      return;\n    }\n\n    let strength = 0;\n    const checks = [\n      password.length >= 8,\n      /[A-Z]/.test(password),\n      /[a-z]/.test(password),\n      /\\d/.test(password),\n      /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)\n    ];\n\n    strength = checks.filter(check => check).length;\n    this.passwordStrength = (strength / 5) * 100;\n\n    const strengthTexts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];\n    this.passwordStrengthText = strengthTexts[strength - 1] || 'Very Weak';\n  }\n\n  getPasswordStrengthColor(): string {\n    if (this.passwordStrength < 20) return 'warn';\n    if (this.passwordStrength < 40) return 'accent';\n    if (this.passwordStrength < 60) return 'primary';\n    if (this.passwordStrength < 80) return 'primary';\n    return 'primary';\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n}\n", "<div class=\"auth-container\">\n  <div class=\"auth-card fade-in\">\n    <!-- Header -->\n    <div class=\"auth-header\">\n      <h1>Create Account</h1>\n      <p>Join our secure platform today</p>\n      <div class=\"security-badge\">\n        <mat-icon>verified_user</mat-icon>\n        Secure Registration\n      </div>\n    </div>\n\n    <div class=\"auth-content\">\n      <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\">\n        <!-- Name Fields -->\n        <div class=\"name-row\">\n          <mat-form-field class=\"form-field half-width\" appearance=\"outline\">\n            <mat-label>First Name</mat-label>\n            <input matInput formControlName=\"firstName\" autocomplete=\"given-name\">\n            <mat-icon matSuffix>person</mat-icon>\n            <mat-error>{{ getFieldError('firstName') }}</mat-error>\n          </mat-form-field>\n\n          <mat-form-field class=\"form-field half-width\" appearance=\"outline\">\n            <mat-label>Last Name</mat-label>\n            <input matInput formControlName=\"lastName\" autocomplete=\"family-name\">\n            <mat-icon matSuffix>person</mat-icon>\n            <mat-error>{{ getFieldError('lastName') }}</mat-error>\n          </mat-form-field>\n        </div>\n\n        <!-- Email -->\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Email Address</mat-label>\n          <input matInput type=\"email\" formControlName=\"email\" autocomplete=\"email\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error>{{ getFieldError('email') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Phone (Optional) -->\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Phone Number (Optional)</mat-label>\n          <input matInput type=\"tel\" formControlName=\"phone\" placeholder=\"+1234567890\" autocomplete=\"tel\">\n          <mat-icon matSuffix>phone</mat-icon>\n          <mat-error>{{ getFieldError('phone') }}</mat-error>\n          <mat-hint>For SMS verification and 2FA</mat-hint>\n        </mat-form-field>\n\n        <!-- Password -->\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Password</mat-label>\n          <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\" autocomplete=\"new-password\">\n          <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError('password') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Password Strength Indicator -->\n        <div *ngIf=\"registerForm.get('password')?.value\" class=\"password-strength\">\n          <div class=\"strength-bar\">\n            <div class=\"strength-fill\" [style.width.%]=\"passwordStrength\" [ngClass]=\"getPasswordStrengthColor()\"></div>\n          </div>\n          <span class=\"strength-text\" [ngClass]=\"getPasswordStrengthColor()\">{{ passwordStrengthText }}</span>\n        </div>\n\n        <!-- Confirm Password -->\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Confirm Password</mat-label>\n          <input matInput [type]=\"hideConfirmPassword ? 'password' : 'text'\" formControlName=\"confirmPassword\" autocomplete=\"new-password\">\n          <button mat-icon-button matSuffix (click)=\"hideConfirmPassword = !hideConfirmPassword\" type=\"button\">\n            <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Terms and Conditions -->\n        <div class=\"checkbox-field\">\n          <mat-checkbox formControlName=\"acceptTerms\">\n            I agree to the <a href=\"/terms\" target=\"_blank\">Terms of Service</a> and \n            <a href=\"/privacy\" target=\"_blank\">Privacy Policy</a>\n          </mat-checkbox>\n          <div class=\"error-message\" *ngIf=\"getFieldError('acceptTerms')\">\n            {{ getFieldError('acceptTerms') }}\n          </div>\n        </div>\n\n        <!-- Submit Button -->\n        <button mat-raised-button color=\"primary\" type=\"submit\" class=\"submit-button\" [disabled]=\"loading\">\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!loading\">Create Account</span>\n        </button>\n\n        <!-- Login Link -->\n        <div class=\"text-center mt-3\">\n          <span>Already have an account? </span>\n          <a routerLink=\"/auth/login\" class=\"text-primary\">Sign In</a>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAyB,gBAAgB;;;;;;;;;;;;;;;IC2D1EC,EADF,CAAAC,cAAA,cAA2E,cAC/C;IACxBD,EAAA,CAAAE,SAAA,cAA2G;IAC7GF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAmE;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IAC/FJ,EAD+F,CAAAG,YAAA,EAAO,EAChG;;;;IAHyBH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,WAAA,UAAAC,MAAA,CAAAC,gBAAA,MAAkC;IAACR,EAAA,CAAAS,UAAA,YAAAF,MAAA,CAAAG,wBAAA,GAAsC;IAE1EV,EAAA,CAAAK,SAAA,EAAsC;IAAtCL,EAAA,CAAAS,UAAA,YAAAF,MAAA,CAAAG,wBAAA,GAAsC;IAACV,EAAA,CAAAK,SAAA,EAA0B;IAA1BL,EAAA,CAAAW,iBAAA,CAAAJ,MAAA,CAAAK,oBAAA,CAA0B;;;;;IAmB7FZ,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAa,kBAAA,MAAAN,MAAA,CAAAO,aAAA,qBACF;;;;;IAKAd,EAAA,CAAAE,SAAA,sBAAyD;;;;;IACzDF,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAI,MAAA,qBAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;AD7EtD,OAAM,MAAOY,iBAAiB;EAQ5BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAVlB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAf,gBAAgB,GAAG,CAAC;IACpB,KAAAI,oBAAoB,GAAG,EAAE;IAQvB,IAAI,CAACY,YAAY,GAAG,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC;MACzCC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAAC6B,SAAS,CAAC,CAAC,CAAC,EAAE7B,UAAU,CAAC8B,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACzFC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC/B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAAC6B,SAAS,CAAC,CAAC,CAAC,EAAE7B,UAAU,CAAC8B,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACxFE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACgC,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjC,UAAU,CAACkC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC;MACvDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAAC6B,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAACO,iBAAiB,CAAC,CAAC;MACtFC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAAC4B,QAAQ,CAAC,CAAC;MAC5CU,WAAW,EAAE,CAAC,KAAK,EAAE,CAACtC,UAAU,CAACuC,YAAY,CAAC;KAC/C,EAAE;MACDC,UAAU,EAAE,IAAI,CAACC;KAClB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACvB,WAAW,CAACwB,eAAe,EAAE;MACpC,IAAI,CAACvB,MAAM,CAACwB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;IACtC;IAEA,IAAI,CAACnB,YAAY,CAACoB,GAAG,CAAC,UAAU,CAAC,EAAEC,YAAY,CAACC,SAAS,CAACZ,QAAQ,IAAG;MACnE,IAAI,CAACa,sBAAsB,CAACb,QAAQ,CAAC;IACvC,CAAC,CAAC;EACJ;EAEAc,QAAQA,CAAA;IACN,IAAI,IAAI,CAACxB,YAAY,CAACyB,OAAO,EAAE;MAC7B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC1B,YAAY,CAAC;MAC5C;IACF;IAEA,IAAI,CAACH,OAAO,GAAG,IAAI;IACnB,MAAM8B,QAAQ,GAAqB,IAAI,CAAC3B,YAAY,CAAC4B,KAAK;IAE1D,IAAI,CAAClC,WAAW,CAACmC,QAAQ,CAACF,QAAQ,CAAC,CAACL,SAAS,CAAC;MAC5CQ,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACnC,QAAQ,CAACoC,IAAI,CAChB,oEAAoE,EACpE,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;QACD,IAAI,CAACtC,MAAM,CAACwB,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;UACpCe,WAAW,EAAE;YAAEC,OAAO,EAAE;UAA6C;SACtE,CAAC;QACF,IAAI,CAACtC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDuC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACxC,QAAQ,CAACoC,IAAI,CAACI,KAAK,CAACD,OAAO,IAAI,qBAAqB,EAAE,OAAO,EAAE;UAAEF,QAAQ,EAAE;QAAI,CAAE,CAAC;QACvF,IAAI,CAACpC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAP,aAAaA,CAAC+C,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAACtC,YAAY,CAACoB,GAAG,CAACiB,SAAS,CAAC;IAC9C,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAG,IAAI,CAACE,aAAa,CAACJ,SAAS,CAAC,cAAc;MACnF,IAAIC,KAAK,CAACC,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,oCAAoC;MACtE,IAAID,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAG,IAAI,CAACE,aAAa,CAACJ,SAAS,CAAC,qBAAqBC,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,CAACG,cAAc,aAAa;MAChJ,IAAIJ,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAG,IAAI,CAACE,aAAa,CAACJ,SAAS,CAAC,oBAAoBC,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,CAACG,cAAc,aAAa;MAC/I,IAAIJ,KAAK,CAACC,MAAM,CAAC,SAAS,CAAC,EAAE;QAC3B,IAAIF,SAAS,KAAK,OAAO,EAAE,OAAO,mCAAmC;QACrE,OAAO,6BAA6B;MACtC;MACA,IAAIC,KAAK,CAACC,MAAM,CAAC,kBAAkB,CAAC,EAAE,OAAOD,KAAK,CAACC,MAAM,CAAC,kBAAkB,CAAC;MAC7E,IAAID,KAAK,CAACC,MAAM,CAAC,kBAAkB,CAAC,EAAE,OAAO,wBAAwB;MACrE,IAAID,KAAK,CAACC,MAAM,CAAC,cAAc,CAAC,EAAE,OAAO,0CAA0C;IACrF;IACA,OAAO,EAAE;EACX;EAEQE,aAAaA,CAACJ,SAAiB;IACrC,MAAMM,MAAM,GAA8B;MACxCzC,SAAS,EAAE,YAAY;MACvBI,QAAQ,EAAE,WAAW;MACrBC,KAAK,EAAE,OAAO;MACdC,KAAK,EAAE,cAAc;MACrBE,QAAQ,EAAE,UAAU;MACpBE,eAAe,EAAE;KAClB;IACD,OAAO+B,MAAM,CAACN,SAAS,CAAC,IAAIA,SAAS;EACvC;EAEQ1B,iBAAiBA,CAACiC,OAAwB;IAChD,MAAMlC,QAAQ,GAAGkC,OAAO,CAAChB,KAAK;IAC9B,IAAI,CAAClB,QAAQ,EAAE,OAAO,IAAI;IAE1B,MAAM6B,MAAM,GAAa,EAAE;IAE3B,IAAI7B,QAAQ,CAACmC,MAAM,GAAG,CAAC,EAAE;MACvBN,MAAM,CAACO,IAAI,CAAC,uBAAuB,CAAC;IACtC;IACA,IAAI,CAAC,OAAO,CAACC,IAAI,CAACrC,QAAQ,CAAC,EAAE;MAC3B6B,MAAM,CAACO,IAAI,CAAC,sBAAsB,CAAC;IACrC;IACA,IAAI,CAAC,OAAO,CAACC,IAAI,CAACrC,QAAQ,CAAC,EAAE;MAC3B6B,MAAM,CAACO,IAAI,CAAC,sBAAsB,CAAC;IACrC;IACA,IAAI,CAAC,IAAI,CAACC,IAAI,CAACrC,QAAQ,CAAC,EAAE;MACxB6B,MAAM,CAACO,IAAI,CAAC,YAAY,CAAC;IAC3B;IACA,IAAI,CAAC,uCAAuC,CAACC,IAAI,CAACrC,QAAQ,CAAC,EAAE;MAC3D6B,MAAM,CAACO,IAAI,CAAC,uBAAuB,CAAC;IACtC;IAEA,IAAIP,MAAM,CAACM,MAAM,GAAG,CAAC,EAAE;MACrB,OAAO;QAAE7D,gBAAgB,EAAE,yBAAyBuD,MAAM,CAACS,IAAI,CAAC,IAAI,CAAC;MAAE,CAAE;IAC3E;IAEA,OAAO,IAAI;EACb;EAEQhC,sBAAsBA,CAACiC,IAAqB;IAClD,MAAMvC,QAAQ,GAAGuC,IAAI,CAAC7B,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMR,eAAe,GAAGqC,IAAI,CAAC7B,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAI,CAACV,QAAQ,IAAI,CAACE,eAAe,EAAE,OAAO,IAAI;IAE9C,IAAIF,QAAQ,CAACkB,KAAK,KAAKhB,eAAe,CAACgB,KAAK,EAAE;MAC5ChB,eAAe,CAACsC,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;MACrD,OAAO;QAAEA,gBAAgB,EAAE;MAAI,CAAE;IACnC;IAEA,IAAIvC,eAAe,CAAC2B,MAAM,GAAG,kBAAkB,CAAC,EAAE;MAChD,OAAO3B,eAAe,CAAC2B,MAAM,CAAC,kBAAkB,CAAC;MACjD,IAAIa,MAAM,CAACC,IAAI,CAACzC,eAAe,CAAC2B,MAAM,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE;QACpDjC,eAAe,CAACsC,SAAS,CAAC,IAAI,CAAC;MACjC;IACF;IAEA,OAAO,IAAI;EACb;EAEQ3B,sBAAsBA,CAACb,QAAgB;IAC7C,IAAI,CAACA,QAAQ,EAAE;MACb,IAAI,CAAC1B,gBAAgB,GAAG,CAAC;MACzB,IAAI,CAACI,oBAAoB,GAAG,EAAE;MAC9B;IACF;IAEA,IAAIkE,QAAQ,GAAG,CAAC;IAChB,MAAMC,MAAM,GAAG,CACb7C,QAAQ,CAACmC,MAAM,IAAI,CAAC,EACpB,OAAO,CAACE,IAAI,CAACrC,QAAQ,CAAC,EACtB,OAAO,CAACqC,IAAI,CAACrC,QAAQ,CAAC,EACtB,IAAI,CAACqC,IAAI,CAACrC,QAAQ,CAAC,EACnB,uCAAuC,CAACqC,IAAI,CAACrC,QAAQ,CAAC,CACvD;IAED4C,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC,CAACZ,MAAM;IAC/C,IAAI,CAAC7D,gBAAgB,GAAIsE,QAAQ,GAAG,CAAC,GAAI,GAAG;IAE5C,MAAMI,aAAa,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;IACrE,IAAI,CAACtE,oBAAoB,GAAGsE,aAAa,CAACJ,QAAQ,GAAG,CAAC,CAAC,IAAI,WAAW;EACxE;EAEApE,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAACF,gBAAgB,GAAG,EAAE,EAAE,OAAO,MAAM;IAC7C,IAAI,IAAI,CAACA,gBAAgB,GAAG,EAAE,EAAE,OAAO,QAAQ;IAC/C,IAAI,IAAI,CAACA,gBAAgB,GAAG,EAAE,EAAE,OAAO,SAAS;IAChD,IAAI,IAAI,CAACA,gBAAgB,GAAG,EAAE,EAAE,OAAO,SAAS;IAChD,OAAO,SAAS;EAClB;EAEQ0C,oBAAoBA,CAACiC,SAAoB;IAC/CP,MAAM,CAACC,IAAI,CAACM,SAAS,CAACC,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMlB,OAAO,GAAGe,SAAS,CAACvC,GAAG,CAAC0C,GAAG,CAAC;MAClClB,OAAO,EAAEmB,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qCArLUzE,iBAAiB,EAAAf,EAAA,CAAAyF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3F,EAAA,CAAAyF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA7F,EAAA,CAAAyF,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA/F,EAAA,CAAAyF,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBnF,iBAAiB;IAAAoF,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTxB1G,EAJN,CAAAC,cAAA,aAA4B,aACK,aAEJ,SACnB;QAAAD,EAAA,CAAAI,MAAA,qBAAc;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACvBH,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAI,MAAA,qCAA8B;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEnCH,EADF,CAAAC,cAAA,aAA4B,eAChB;QAAAD,EAAA,CAAAI,MAAA,oBAAa;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QAClCH,EAAA,CAAAI,MAAA,6BACF;QACFJ,EADE,CAAAG,YAAA,EAAM,EACF;QAGJH,EADF,CAAAC,cAAA,cAA0B,eACiC;QAAxBD,EAAA,CAAA4G,UAAA,sBAAAC,qDAAA;UAAA,OAAYF,GAAA,CAAA3D,QAAA,EAAU;QAAA,EAAC;QAIlDhD,EAFJ,CAAAC,cAAA,cAAsB,yBAC+C,iBACtD;QAAAD,EAAA,CAAAI,MAAA,kBAAU;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAE,SAAA,gBAAsE;QACtEF,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAI,MAAA,cAAM;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QACrCH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAAgC;QAC7CJ,EAD6C,CAAAG,YAAA,EAAY,EACxC;QAGfH,EADF,CAAAC,cAAA,yBAAmE,iBACtD;QAAAD,EAAA,CAAAI,MAAA,iBAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QAChCH,EAAA,CAAAE,SAAA,iBAAsE;QACtEF,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAI,MAAA,cAAM;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QACrCH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAA+B;QAE9CJ,EAF8C,CAAAG,YAAA,EAAY,EACvC,EACb;QAIJH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;QAAAD,EAAA,CAAAI,MAAA,qBAAa;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAE,SAAA,iBAA0E;QAC1EF,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAI,MAAA,aAAK;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QACpCH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAA4B;QACzCJ,EADyC,CAAAG,YAAA,EAAY,EACpC;QAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;QAAAD,EAAA,CAAAI,MAAA,+BAAuB;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QAC9CH,EAAA,CAAAE,SAAA,iBAAgG;QAChGF,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAI,MAAA,aAAK;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QACpCH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAA4B;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QACnDH,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAI,MAAA,oCAA4B;QACxCJ,EADwC,CAAAG,YAAA,EAAW,EAClC;QAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;QAAAD,EAAA,CAAAI,MAAA,gBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAE,SAAA,iBAAmH;QACnHF,EAAA,CAAAC,cAAA,kBAAuF;QAArDD,EAAA,CAAA4G,UAAA,mBAAAE,oDAAA;UAAA,OAAAH,GAAA,CAAArF,YAAA,IAAAqF,GAAA,CAAArF,YAAA;QAAA,EAAsC;QACtEtB,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAI,MAAA,IAAoD;QAChEJ,EADgE,CAAAG,YAAA,EAAW,EAClE;QACTH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAA+B;QAC5CJ,EAD4C,CAAAG,YAAA,EAAY,EACvC;QAGjBH,EAAA,CAAA+G,UAAA,KAAAC,iCAAA,kBAA2E;QASzEhH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;QAAAD,EAAA,CAAAI,MAAA,wBAAgB;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QACvCH,EAAA,CAAAE,SAAA,iBAAiI;QACjIF,EAAA,CAAAC,cAAA,kBAAqG;QAAnED,EAAA,CAAA4G,UAAA,mBAAAK,oDAAA;UAAA,OAAAN,GAAA,CAAApF,mBAAA,IAAAoF,GAAA,CAAApF,mBAAA;QAAA,EAAoD;QACpFvB,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAI,MAAA,IAA2D;QACvEJ,EADuE,CAAAG,YAAA,EAAW,EACzE;QACTH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAAsC;QACnDJ,EADmD,CAAAG,YAAA,EAAY,EAC9C;QAIfH,EADF,CAAAC,cAAA,eAA4B,wBACkB;QAC1CD,EAAA,CAAAI,MAAA,wBAAe;QAAAJ,EAAA,CAAAC,cAAA,aAAiC;QAAAD,EAAA,CAAAI,MAAA,wBAAgB;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAACH,EAAA,CAAAI,MAAA,aACrE;QAAAJ,EAAA,CAAAC,cAAA,aAAmC;QAAAD,EAAA,CAAAI,MAAA,sBAAc;QACnDJ,EADmD,CAAAG,YAAA,EAAI,EACxC;QACfH,EAAA,CAAA+G,UAAA,KAAAG,iCAAA,kBAAgE;QAGlElH,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,kBAAmG;QAEjGD,EADA,CAAA+G,UAAA,KAAAI,yCAAA,0BAA2C,KAAAC,kCAAA,mBACpB;QACzBpH,EAAA,CAAAG,YAAA,EAAS;QAIPH,EADF,CAAAC,cAAA,eAA8B,YACtB;QAAAD,EAAA,CAAAI,MAAA,iCAAyB;QAAAJ,EAAA,CAAAG,YAAA,EAAO;QACtCH,EAAA,CAAAC,cAAA,aAAiD;QAAAD,EAAA,CAAAI,MAAA,eAAO;QAKlEJ,EALkE,CAAAG,YAAA,EAAI,EACxD,EACD,EACH,EACF,EACF;;;;QAxFMH,EAAA,CAAAK,SAAA,IAA0B;QAA1BL,EAAA,CAAAS,UAAA,cAAAkG,GAAA,CAAAnF,YAAA,CAA0B;QAOfxB,EAAA,CAAAK,SAAA,GAAgC;QAAhCL,EAAA,CAAAW,iBAAA,CAAAgG,GAAA,CAAA7F,aAAA,cAAgC;QAOhCd,EAAA,CAAAK,SAAA,GAA+B;QAA/BL,EAAA,CAAAW,iBAAA,CAAAgG,GAAA,CAAA7F,aAAA,aAA+B;QASjCd,EAAA,CAAAK,SAAA,GAA4B;QAA5BL,EAAA,CAAAW,iBAAA,CAAAgG,GAAA,CAAA7F,aAAA,UAA4B;QAQ5Bd,EAAA,CAAAK,SAAA,GAA4B;QAA5BL,EAAA,CAAAW,iBAAA,CAAAgG,GAAA,CAAA7F,aAAA,UAA4B;QAOvBd,EAAA,CAAAK,SAAA,GAA2C;QAA3CL,EAAA,CAAAS,UAAA,SAAAkG,GAAA,CAAArF,YAAA,uBAA2C;QAE/CtB,EAAA,CAAAK,SAAA,GAAoD;QAApDL,EAAA,CAAAW,iBAAA,CAAAgG,GAAA,CAAArF,YAAA,mCAAoD;QAErDtB,EAAA,CAAAK,SAAA,GAA+B;QAA/BL,EAAA,CAAAW,iBAAA,CAAAgG,GAAA,CAAA7F,aAAA,aAA+B;QAItCd,EAAA,CAAAK,SAAA,EAAyC;QAAzCL,EAAA,CAAAS,UAAA,UAAA4G,OAAA,GAAAV,GAAA,CAAAnF,YAAA,CAAAoB,GAAA,+BAAAyE,OAAA,CAAAjE,KAAA,CAAyC;QAU7BpD,EAAA,CAAAK,SAAA,GAAkD;QAAlDL,EAAA,CAAAS,UAAA,SAAAkG,GAAA,CAAApF,mBAAA,uBAAkD;QAEtDvB,EAAA,CAAAK,SAAA,GAA2D;QAA3DL,EAAA,CAAAW,iBAAA,CAAAgG,GAAA,CAAApF,mBAAA,mCAA2D;QAE5DvB,EAAA,CAAAK,SAAA,GAAsC;QAAtCL,EAAA,CAAAW,iBAAA,CAAAgG,GAAA,CAAA7F,aAAA,oBAAsC;QASrBd,EAAA,CAAAK,SAAA,GAAkC;QAAlCL,EAAA,CAAAS,UAAA,SAAAkG,GAAA,CAAA7F,aAAA,gBAAkC;QAMcd,EAAA,CAAAK,SAAA,EAAoB;QAApBL,EAAA,CAAAS,UAAA,aAAAkG,GAAA,CAAAtF,OAAA,CAAoB;QAClFrB,EAAA,CAAAK,SAAA,EAAa;QAAbL,EAAA,CAAAS,UAAA,SAAAkG,GAAA,CAAAtF,OAAA,CAAa;QACpBrB,EAAA,CAAAK,SAAA,EAAc;QAAdL,EAAA,CAAAS,UAAA,UAAAkG,GAAA,CAAAtF,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}