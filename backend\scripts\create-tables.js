#!/usr/bin/env node

/**
 * Manual Table Creation Script for PostgreSQL
 * This script manually creates all required tables
 */

require('dotenv').config();
const { Client } = require('pg');

async function createTables() {
  console.log('🗄️  CREATING DATABASE TABLES');
  console.log('============================\n');

  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'secure_backend',
  });

  try {
    console.log('🔌 Connecting to PostgreSQL...');
    await client.connect();
    console.log('✅ Connected to database\n');

    // Drop existing tables (for clean migration)
    console.log('🧹 Dropping existing tables...');
    await client.query('DROP TABLE IF EXISTS public.payment CASCADE');
    await client.query('DROP TABLE IF EXISTS public.otp CASCADE');
    await client.query('DROP TABLE IF EXISTS public.user_credentials CASCADE');
    await client.query('DROP TABLE IF EXISTS public."user" CASCADE');
    console.log('✅ Existing tables dropped\n');

    // Create User table
    console.log('👤 Creating User table...');
    await client.query(`
      CREATE TABLE public."user" (
        id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid()::text,
        email VARCHAR(255) UNIQUE NOT NULL,
        first_name VARCHAR(255) NOT NULL,
        last_name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        password VARCHAR(255) NOT NULL,
        email_verified BOOLEAN DEFAULT FALSE,
        phone_verified BOOLEAN DEFAULT FALSE,
        two_factor_enabled BOOLEAN DEFAULT FALSE,
        two_factor_secret VARCHAR(255),
        two_factor_method VARCHAR(50),
        roles JSONB DEFAULT '["user"]',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login_at TIMESTAMP,
        login_attempts INTEGER DEFAULT 0,
        lock_until TIMESTAMP,
        email_verification_token VARCHAR(255),
        email_verification_expires TIMESTAMP,
        password_reset_token VARCHAR(255),
        password_reset_expires TIMESTAMP
      )
    `);
    console.log('✅ User table created');

    // Create UserCredentials table
    console.log('🔐 Creating UserCredentials table...');
    await client.query(`
      CREATE TABLE public.user_credentials (
        id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid()::text,
        password VARCHAR(255) NOT NULL,
        otp_secret VARCHAR(255),
        backup_codes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        user_id VARCHAR(255) REFERENCES public."user"(id) ON DELETE CASCADE
      )
    `);
    console.log('✅ UserCredentials table created');

    // Create OTP table
    console.log('📱 Creating OTP table...');
    await client.query(`
      CREATE TABLE public.otp (
        id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid()::text,
        identifier VARCHAR(255) NOT NULL,
        code VARCHAR(10) NOT NULL,
        type VARCHAR(50) NOT NULL CHECK (type IN ('email', 'sms', 'login', 'verification', 'password_reset')),
        expires_at TIMESTAMP NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        attempts INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        used_at TIMESTAMP
      )
    `);
    console.log('✅ OTP table created');

    // Create Payment table
    console.log('💳 Creating Payment table...');
    await client.query(`
      CREATE TABLE public.payment (
        id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid()::text,
        razorpay_order_id VARCHAR(255) NOT NULL,
        razorpay_payment_id VARCHAR(255),
        razorpay_signature VARCHAR(255),
        amount INTEGER NOT NULL CHECK (amount >= 1 AND amount <= 1000000),
        currency VARCHAR(3) DEFAULT 'INR' CHECK (currency IN ('INR', 'USD')),
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'failed', 'cancelled', 'refunded')),
        description TEXT,
        metadata JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        paid_at TIMESTAMP,
        user_id VARCHAR(255) REFERENCES public."user"(id) ON DELETE CASCADE
      )
    `);
    console.log('✅ Payment table created');

    // Create indexes for performance
    console.log('📊 Creating indexes...');
    await client.query('CREATE INDEX idx_user_email ON public."user"(email)');
    await client.query('CREATE INDEX idx_user_phone ON public."user"(phone)');
    await client.query('CREATE INDEX idx_user_active ON public."user"(is_active)');
    await client.query('CREATE INDEX idx_otp_identifier ON public.otp(identifier)');
    await client.query('CREATE INDEX idx_otp_expires ON public.otp(expires_at)');
    await client.query('CREATE INDEX idx_payment_user ON public.payment(user_id)');
    await client.query('CREATE INDEX idx_payment_status ON public.payment(status)');
    console.log('✅ Indexes created');

    // Create triggers for updated_at
    console.log('⚡ Creating triggers...');
    await client.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    await client.query(`
      CREATE TRIGGER update_user_updated_at BEFORE UPDATE ON public."user"
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `);

    await client.query(`
      CREATE TRIGGER update_user_credentials_updated_at BEFORE UPDATE ON public.user_credentials
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `);

    await client.query(`
      CREATE TRIGGER update_payment_updated_at BEFORE UPDATE ON public.payment
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `);
    console.log('✅ Triggers created');

    // Verify tables
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);

    console.log('\n📋 Created tables:');
    result.rows.forEach(row => console.log(`   ✅ ${row.table_name}`));

    console.log('\n🎉 Database tables created successfully!');

  } catch (error) {
    console.error('❌ Error creating tables:', error.message);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

if (require.main === module) {
  createTables().catch(console.error);
}

module.exports = { createTables };
