#!/usr/bin/env node

/**
 * API Testing Script
 * This script tests all the backend APIs
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3002';

async function testAPI() {
  console.log('🧪 TESTING ALL BACKEND APIS');
  console.log('============================\n');

  try {
    // Test 1: Health Check
    console.log('🔍 Test 1: Health Check');
    const pingResponse = await axios.get(`${BASE_URL}/ping`);
    console.log('✅ Ping successful:', pingResponse.data);
    console.log('');

    // Test 2: User Signup
    console.log('🔍 Test 2: User Signup');
    try {
      const signupData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'API',
        lastName: 'Test',
        phone: '+**********'
      };
      
      const signupResponse = await axios.post(`${BASE_URL}/auth/signup`, signupData);
      console.log('✅ Signup successful:', signupResponse.data);
    } catch (error) {
      console.log('❌ Signup failed:', error.response?.data || error.message);
      console.log('Status:', error.response?.status);
      console.log('Headers:', error.response?.headers);
    }
    console.log('');

    // Test 3: User Login with existing user
    console.log('🔍 Test 3: User Login (existing user)');
    try {
      const loginData = {
        email: '<EMAIL>',
        password: 'Test123!@#'
      };
      
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, loginData);
      console.log('✅ Login successful:', loginResponse.data);
      
      // Store token for further tests
      const token = loginResponse.data.token;
      
      // Test 4: Protected endpoint
      console.log('🔍 Test 4: Protected Endpoint');
      const protectedResponse = await axios.get(`${BASE_URL}/users/me`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Protected endpoint successful:', protectedResponse.data);
      
    } catch (error) {
      console.log('❌ Login failed:', error.response?.data || error.message);
    }
    console.log('');

    // Test 5: Payment API
    console.log('🔍 Test 5: Payment API');
    try {
      const paymentData = {
        amount: 1000,
        currency: 'INR'
      };
      
      const paymentResponse = await axios.post(`${BASE_URL}/payments/create-order`, paymentData);
      console.log('✅ Payment order creation successful:', paymentResponse.data);
    } catch (error) {
      console.log('❌ Payment API failed:', error.response?.data || error.message);
    }
    console.log('');

    // Test 6: OTP API
    console.log('🔍 Test 6: OTP API');
    try {
      const otpData = {
        identifier: '<EMAIL>',
        type: 'login'
      };
      
      const otpResponse = await axios.post(`${BASE_URL}/otp/generate`, otpData);
      console.log('✅ OTP generation successful:', otpResponse.data);
    } catch (error) {
      console.log('❌ OTP API failed:', error.response?.data || error.message);
    }
    console.log('');

    // Test 7: 2FA Setup
    console.log('🔍 Test 7: 2FA Setup');
    try {
      const twoFAResponse = await axios.post(`${BASE_URL}/2fa/setup`, {
        method: 'totp'
      });
      console.log('✅ 2FA setup successful:', twoFAResponse.data);
    } catch (error) {
      console.log('❌ 2FA API failed:', error.response?.data || error.message);
    }
    console.log('');

    console.log('🎉 API Testing completed!');

  } catch (error) {
    console.error('❌ API Testing failed:', error.message);
  }
}

// Install axios if not available
async function ensureAxios() {
  try {
    require('axios');
  } catch (error) {
    console.log('📦 Installing axios...');
    const { execSync } = require('child_process');
    execSync('npm install axios', { stdio: 'inherit' });
    console.log('✅ Axios installed');
  }
}

if (require.main === module) {
  ensureAxios().then(() => testAPI()).catch(console.error);
}

module.exports = { testAPI };
