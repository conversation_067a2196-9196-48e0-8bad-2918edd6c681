{"ast": null, "code": "import { ObserversModule } from '@angular/cdk/observers';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { j as <PERSON><PERSON><PERSON><PERSON>ield, M as <PERSON><PERSON><PERSON><PERSON>, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON><PERSON>x, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-C9DZXojn.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nclass MatFormFieldModule {\n  static ɵfac = function MatFormFieldModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatFormFieldModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatFormFieldModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, ObserversModule, MatFormField, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, ObserversModule, MatFormField, MatLabel, MatError, MatHint, MatPrefix, MatSuffix],\n      exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatFormFieldModule as M };", "map": {"version": 3, "names": ["ObserversModule", "i0", "NgModule", "j", "MatFormField", "M", "<PERSON><PERSON><PERSON><PERSON>", "b", "<PERSON><PERSON><PERSON><PERSON>", "c", "MatHint", "e", "MatPrefix", "g", "MatSuffix", "MatCommonModule", "MatFormFieldModule", "ɵfac", "MatFormFieldModule_Factory", "__ngFactoryType__", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "imports", "ngDevMode", "ɵsetClassMetadata", "args", "exports"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/material/fesm2022/module-DzZHEh7B.mjs"], "sourcesContent": ["import { ObserversModule } from '@angular/cdk/observers';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { j as <PERSON><PERSON><PERSON><PERSON>ield, M as <PERSON><PERSON><PERSON><PERSON>, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON><PERSON><PERSON>, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-C9DZXojn.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\n\nclass MatFormFieldModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldModule, imports: [MatCommonModule,\n            ObserversModule,\n            MatFormField,\n            MatLabel,\n            Mat<PERSON>rror,\n            MatHint,\n            MatPrefix,\n            MatSuffix], exports: [MatFormField, MatLabel, Mat<PERSON>int, <PERSON><PERSON>rror, MatPrefix, MatSuffix, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldModule, imports: [MatCommonModule,\n            ObserversModule,\n            MatFormField, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        ObserversModule,\n                        MatFormField,\n                        MatLabel,\n                        MatError,\n                        MatHint,\n                        MatPrefix,\n                        MatSuffix,\n                    ],\n                    exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule],\n                }]\n        }] });\n\nexport { MatFormFieldModule as M };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,wBAAwB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,SAAS,QAAQ,2BAA2B;AACzI,SAAST,CAAC,IAAIU,eAAe,QAAQ,8BAA8B;AAEnE,MAAMC,kBAAkB,CAAC;EACrB,OAAOC,IAAI,YAAAC,2BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFH,kBAAkB;EAAA;EACrH,OAAOI,IAAI,kBAD8EnB,EAAE,CAAAoB,gBAAA;IAAAC,IAAA,EACSN;EAAkB;EAQtH,OAAOO,IAAI,kBAT8EtB,EAAE,CAAAuB,gBAAA;IAAAC,OAAA,GASuCV,eAAe,EACzIf,eAAe,EACfI,YAAY,EAAEW,eAAe;EAAA;AACzC;AACA;EAAA,QAAAW,SAAA,oBAAAA,SAAA,KAb6FzB,EAAE,CAAA0B,iBAAA,CAaJX,kBAAkB,EAAc,CAAC;IAChHM,IAAI,EAAEpB,QAAQ;IACd0B,IAAI,EAAE,CAAC;MACCH,OAAO,EAAE,CACLV,eAAe,EACff,eAAe,EACfI,YAAY,EACZE,QAAQ,EACRE,QAAQ,EACRE,OAAO,EACPE,SAAS,EACTE,SAAS,CACZ;MACDe,OAAO,EAAE,CAACzB,YAAY,EAAEE,QAAQ,EAAEI,OAAO,EAAEF,QAAQ,EAAEI,SAAS,EAAEE,SAAS,EAAEC,eAAe;IAC9F,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASC,kBAAkB,IAAIX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}