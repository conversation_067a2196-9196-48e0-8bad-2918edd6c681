{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { A, Z, b as ZERO, N as NINE } from './keycodes-CpHkExLC.mjs';\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n  _letterKeyStream = new Subject();\n  _items = [];\n  _selectedItemIndex = -1;\n  /** Buffer for the letters that the user has pressed */\n  _pressedLetters = [];\n  _skipPredicateFn;\n  _selectedItem = new Subject();\n  selectedItem = this._selectedItem;\n  constructor(initialItems, config) {\n    const typeAheadInterval = typeof config?.debounceInterval === 'number' ? config.debounceInterval : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n    if (config?.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && initialItems.length && initialItems.some(item => typeof item.getLabel !== 'function')) {\n      throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n    this.setItems(initialItems);\n    this._setupKeyHandler(typeAheadInterval);\n  }\n  destroy() {\n    this._pressedLetters = [];\n    this._letterKeyStream.complete();\n    this._selectedItem.complete();\n  }\n  setCurrentSelectedItemIndex(index) {\n    this._selectedItemIndex = index;\n  }\n  setItems(items) {\n    this._items = items;\n  }\n  handleKey(event) {\n    const keyCode = event.keyCode;\n    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n    if (event.key && event.key.length === 1) {\n      this._letterKeyStream.next(event.key.toLocaleUpperCase());\n    } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n      this._letterKeyStream.next(String.fromCharCode(keyCode));\n    }\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Resets the currently stored sequence of typed letters. */\n  reset() {\n    this._pressedLetters = [];\n  }\n  _setupKeyHandler(typeAheadInterval) {\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase())).subscribe(inputString => {\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < this._items.length + 1; i++) {\n        const index = (this._selectedItemIndex + i) % this._items.length;\n        const item = this._items[index];\n        if (!this._skipPredicateFn?.(item) && item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0) {\n          this._selectedItem.next(item);\n          break;\n        }\n      }\n      this._pressedLetters = [];\n    });\n  }\n}\nexport { Typeahead as T };", "map": {"version": 3, "names": ["Subject", "tap", "debounceTime", "filter", "map", "A", "Z", "b", "ZERO", "N", "NINE", "DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS", "Typeahead", "_letterKeyStream", "_items", "_selectedItemIndex", "_pressedLetters", "_skipPredicateFn", "_selectedItem", "selectedItem", "constructor", "initialItems", "config", "typeAheadInterval", "debounceInterval", "skipPredicate", "ngDevMode", "length", "some", "item", "get<PERSON><PERSON><PERSON>", "Error", "setItems", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destroy", "complete", "setCurrentSelectedItemIndex", "index", "items", "handle<PERSON>ey", "event", "keyCode", "key", "next", "toLocaleUpperCase", "String", "fromCharCode", "isTyping", "reset", "pipe", "letter", "push", "join", "subscribe", "inputString", "i", "trim", "indexOf", "T"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/cdk/fesm2022/typeahead-9ZW4Dtsf.mjs"], "sourcesContent": ["import { Subject } from 'rxjs';\nimport { tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { A, Z, b as ZERO, N as NINE } from './keycodes-CpHkExLC.mjs';\n\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n    _letterKeyStream = new Subject();\n    _items = [];\n    _selectedItemIndex = -1;\n    /** Buffer for the letters that the user has pressed */\n    _pressedLetters = [];\n    _skipPredicateFn;\n    _selectedItem = new Subject();\n    selectedItem = this._selectedItem;\n    constructor(initialItems, config) {\n        const typeAheadInterval = typeof config?.debounceInterval === 'number'\n            ? config.debounceInterval\n            : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n        if (config?.skipPredicate) {\n            this._skipPredicateFn = config.skipPredicate;\n        }\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n            initialItems.length &&\n            initialItems.some(item => typeof item.getLabel !== 'function')) {\n            throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n        }\n        this.setItems(initialItems);\n        this._setupKeyHandler(typeAheadInterval);\n    }\n    destroy() {\n        this._pressedLetters = [];\n        this._letterKeyStream.complete();\n        this._selectedItem.complete();\n    }\n    setCurrentSelectedItemIndex(index) {\n        this._selectedItemIndex = index;\n    }\n    setItems(items) {\n        this._items = items;\n    }\n    handleKey(event) {\n        const keyCode = event.keyCode;\n        // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n        // otherwise fall back to resolving alphanumeric characters via the keyCode.\n        if (event.key && event.key.length === 1) {\n            this._letterKeyStream.next(event.key.toLocaleUpperCase());\n        }\n        else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n            this._letterKeyStream.next(String.fromCharCode(keyCode));\n        }\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return this._pressedLetters.length > 0;\n    }\n    /** Resets the currently stored sequence of typed letters. */\n    reset() {\n        this._pressedLetters = [];\n    }\n    _setupKeyHandler(typeAheadInterval) {\n        // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n        // and convert those letters back into a string. Afterwards find the first item that starts\n        // with that string and select it.\n        this._letterKeyStream\n            .pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase()))\n            .subscribe(inputString => {\n            // Start at 1 because we want to start searching at the item immediately\n            // following the current active item.\n            for (let i = 1; i < this._items.length + 1; i++) {\n                const index = (this._selectedItemIndex + i) % this._items.length;\n                const item = this._items[index];\n                if (!this._skipPredicateFn?.(item) &&\n                    item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0) {\n                    this._selectedItem.next(item);\n                    break;\n                }\n            }\n            this._pressedLetters = [];\n        });\n    }\n}\n\nexport { Typeahead as T };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,GAAG,EAAEC,YAAY,EAAEC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAC/D,SAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,IAAI,QAAQ,yBAAyB;AAEpE,MAAMC,sCAAsC,GAAG,GAAG;AAClD;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,gBAAgB,GAAG,IAAIb,OAAO,CAAC,CAAC;EAChCc,MAAM,GAAG,EAAE;EACXC,kBAAkB,GAAG,CAAC,CAAC;EACvB;EACAC,eAAe,GAAG,EAAE;EACpBC,gBAAgB;EAChBC,aAAa,GAAG,IAAIlB,OAAO,CAAC,CAAC;EAC7BmB,YAAY,GAAG,IAAI,CAACD,aAAa;EACjCE,WAAWA,CAACC,YAAY,EAAEC,MAAM,EAAE;IAC9B,MAAMC,iBAAiB,GAAG,OAAOD,MAAM,EAAEE,gBAAgB,KAAK,QAAQ,GAChEF,MAAM,CAACE,gBAAgB,GACvBb,sCAAsC;IAC5C,IAAIW,MAAM,EAAEG,aAAa,EAAE;MACvB,IAAI,CAACR,gBAAgB,GAAGK,MAAM,CAACG,aAAa;IAChD;IACA,IAAI,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9CL,YAAY,CAACM,MAAM,IACnBN,YAAY,CAACO,IAAI,CAACC,IAAI,IAAI,OAAOA,IAAI,CAACC,QAAQ,KAAK,UAAU,CAAC,EAAE;MAChE,MAAM,IAAIC,KAAK,CAAC,0EAA0E,CAAC;IAC/F;IACA,IAAI,CAACC,QAAQ,CAACX,YAAY,CAAC;IAC3B,IAAI,CAACY,gBAAgB,CAACV,iBAAiB,CAAC;EAC5C;EACAW,OAAOA,CAAA,EAAG;IACN,IAAI,CAAClB,eAAe,GAAG,EAAE;IACzB,IAAI,CAACH,gBAAgB,CAACsB,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACjB,aAAa,CAACiB,QAAQ,CAAC,CAAC;EACjC;EACAC,2BAA2BA,CAACC,KAAK,EAAE;IAC/B,IAAI,CAACtB,kBAAkB,GAAGsB,KAAK;EACnC;EACAL,QAAQA,CAACM,KAAK,EAAE;IACZ,IAAI,CAACxB,MAAM,GAAGwB,KAAK;EACvB;EACAC,SAASA,CAACC,KAAK,EAAE;IACb,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO;IAC7B;IACA;IACA,IAAID,KAAK,CAACE,GAAG,IAAIF,KAAK,CAACE,GAAG,CAACf,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACd,gBAAgB,CAAC8B,IAAI,CAACH,KAAK,CAACE,GAAG,CAACE,iBAAiB,CAAC,CAAC,CAAC;IAC7D,CAAC,MACI,IAAKH,OAAO,IAAIpC,CAAC,IAAIoC,OAAO,IAAInC,CAAC,IAAMmC,OAAO,IAAIjC,IAAI,IAAIiC,OAAO,IAAI/B,IAAK,EAAE;MAC7E,IAAI,CAACG,gBAAgB,CAAC8B,IAAI,CAACE,MAAM,CAACC,YAAY,CAACL,OAAO,CAAC,CAAC;IAC5D;EACJ;EACA;EACAM,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC/B,eAAe,CAACW,MAAM,GAAG,CAAC;EAC1C;EACA;EACAqB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAChC,eAAe,GAAG,EAAE;EAC7B;EACAiB,gBAAgBA,CAACV,iBAAiB,EAAE;IAChC;IACA;IACA;IACA,IAAI,CAACV,gBAAgB,CAChBoC,IAAI,CAAChD,GAAG,CAACiD,MAAM,IAAI,IAAI,CAAClC,eAAe,CAACmC,IAAI,CAACD,MAAM,CAAC,CAAC,EAAEhD,YAAY,CAACqB,iBAAiB,CAAC,EAAEpB,MAAM,CAAC,MAAM,IAAI,CAACa,eAAe,CAACW,MAAM,GAAG,CAAC,CAAC,EAAEvB,GAAG,CAAC,MAAM,IAAI,CAACY,eAAe,CAACoC,IAAI,CAAC,EAAE,CAAC,CAACR,iBAAiB,CAAC,CAAC,CAAC,CAAC,CACpMS,SAAS,CAACC,WAAW,IAAI;MAC1B;MACA;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACzC,MAAM,CAACa,MAAM,GAAG,CAAC,EAAE4B,CAAC,EAAE,EAAE;QAC7C,MAAMlB,KAAK,GAAG,CAAC,IAAI,CAACtB,kBAAkB,GAAGwC,CAAC,IAAI,IAAI,CAACzC,MAAM,CAACa,MAAM;QAChE,MAAME,IAAI,GAAG,IAAI,CAACf,MAAM,CAACuB,KAAK,CAAC;QAC/B,IAAI,CAAC,IAAI,CAACpB,gBAAgB,GAAGY,IAAI,CAAC,IAC9BA,IAAI,CAACC,QAAQ,GAAG,CAAC,CAACc,iBAAiB,CAAC,CAAC,CAACY,IAAI,CAAC,CAAC,CAACC,OAAO,CAACH,WAAW,CAAC,KAAK,CAAC,EAAE;UACzE,IAAI,CAACpC,aAAa,CAACyB,IAAI,CAACd,IAAI,CAAC;UAC7B;QACJ;MACJ;MACA,IAAI,CAACb,eAAe,GAAG,EAAE;IAC7B,CAAC,CAAC;EACN;AACJ;AAEA,SAASJ,SAAS,IAAI8C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}