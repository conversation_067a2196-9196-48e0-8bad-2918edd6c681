#!/usr/bin/env node

/**
 * Database Migration Script
 * This script creates the database schema and initial data
 */

const { SecureBackendApplication } = require('../dist/application');
const { DbDataSource } = require('../dist/datasources');

async function migrate() {
  console.log('🗄️  Starting database migration...\n');
  
  try {
    // Create application instance
    const app = new SecureBackendApplication();
    await app.boot();
    
    // Get datasource
    const datasource = await app.get('datasources.db');
    
    console.log('📋 Database Configuration:');
    console.log(`   Connector: ${datasource.settings.connector}`);
    console.log(`   Database: ${datasource.settings.database || 'In-Memory'}`);
    console.log(`   Host: ${datasource.settings.host || 'N/A'}\n`);
    
    // Auto-migrate database schema
    console.log('🔄 Creating database schema...');
    
    // First check if we can connect to the database
    if (datasource.settings.connector === 'postgresql') {
      console.log('🔗 Testing PostgreSQL connection...');
      await datasource.ping();
      console.log('✅ Database connection successful');
      
      // Force auto-migrate for all models
      console.log('🔧 Force migrating all models...');
      await datasource.automigrate();
      
      // Verify tables were created
      const query = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      `;
      const result = await datasource.execute(query);
      console.log('📋 Created tables:', result.map(row => row.table_name));
    } else {
      await datasource.automigrate();
    }
    
    console.log('✅ Database schema created successfully!\n');
    
    // Create initial data if needed
    await createInitialData(app);
    
    console.log('🎉 Database migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

async function createInitialData(app) {
  console.log('📝 Creating initial data...');
  
  try {
    // Try different ways to get the repository
    let userRepository;
    
    try {
      userRepository = await app.get('repositories.UserRepository');
      console.log('✅ Got repository via repositories.UserRepository');
    } catch (e1) {
      try {
        userRepository = await app.getRepository('UserRepository');
        console.log('✅ Got repository via getRepository method');
      } catch (e2) {
        try {
          // Import and create repository directly
          const { UserRepository } = require('../dist/repositories');
          const datasource = await app.get('datasources.db');
          userRepository = new UserRepository(datasource);
          console.log('✅ Created repository directly');
        } catch (e3) {
          console.error('❌ Failed to get UserRepository:', e1.message, e2.message, e3.message);
          return;
        }
      }
    }
    
    // Check if admin user exists
    const existingAdmin = await userRepository.findOne({
      where: { email: '<EMAIL>' }
    });
    
    if (!existingAdmin) {
      // Create admin user
      const bcrypt = require('bcryptjs');
      const adminPassword = await bcrypt.hash('Admin123!@#', 12);
      
      const adminUser = await userRepository.create({
        email: '<EMAIL>',
        firstName: 'System',
        lastName: 'Administrator',
        password: adminPassword,
        emailVerified: true,
        phoneVerified: false,
        twoFactorEnabled: false,
        roles: ['admin', 'user'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      console.log(`✅ Admin user created: ${adminUser.email}`);
    } else {
      console.log('ℹ️  Admin user already exists');
    }
    
    // Create test user for development
    if (process.env.NODE_ENV !== 'production') {
      const existingTest = await userRepository.findOne({
        where: { email: '<EMAIL>' }
      });
      
      if (!existingTest) {
        const bcrypt = require('bcryptjs');
        const testPassword = await bcrypt.hash('Test123!@#', 12);
        
        const testUser = await userRepository.create({
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          password: testPassword,
          emailVerified: true,
          phoneVerified: false,
          twoFactorEnabled: false,
          roles: ['user'],
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        
        console.log(`✅ Test user created: ${testUser.email}`);
      } else {
        console.log('ℹ️  Test user already exists');
      }
    }
    
    console.log('✅ Initial data setup completed!\n');
    
  } catch (error) {
    console.warn('⚠️  Initial data creation failed:', error.message);
  }
}

// Run migration if called directly
if (require.main === module) {
  migrate().catch(console.error);
}

module.exports = { migrate, createInitialData };
