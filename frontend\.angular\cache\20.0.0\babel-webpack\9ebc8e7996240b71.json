{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/loading.service\";\nexport class AuthInterceptor {\n  constructor(authService, router, loadingService) {\n    this.authService = authService;\n    this.router = router;\n    this.loadingService = loadingService;\n  }\n  intercept(request, next) {\n    // Show loading indicator\n    this.loadingService.show();\n    // Add auth token if available\n    const token = this.authService.getToken();\n    if (token && !this.authService.isTokenExpired()) {\n      request = request.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json',\n          'X-Requested-With': 'XMLHttpRequest'\n        }\n      });\n    }\n    // Add CSRF protection\n    request = request.clone({\n      setHeaders: {\n        'X-CSRF-Token': this.generateCSRFToken()\n      }\n    });\n    return next.handle(request).pipe(catchError(error => {\n      this.handleError(error);\n      return throwError(() => error);\n    }), finalize(() => {\n      // Hide loading indicator\n      this.loadingService.hide();\n    }));\n  }\n  handleError(error) {\n    switch (error.status) {\n      case 401:\n        // Unauthorized - token expired or invalid\n        this.authService.logout();\n        this.router.navigate(['/auth/login'], {\n          queryParams: {\n            message: 'Session expired. Please login again.'\n          }\n        });\n        break;\n      case 403:\n        // Forbidden - insufficient permissions\n        this.router.navigate(['/unauthorized']);\n        break;\n      case 429:\n        // Too many requests - rate limited\n        console.warn('Rate limit exceeded. Please try again later.');\n        break;\n      case 0:\n        // Network error\n        console.error('Network error. Please check your connection.');\n        break;\n      default:\n        console.error('HTTP Error:', error);\n    }\n  }\n  generateCSRFToken() {\n    // Simple CSRF token generation\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  static #_ = this.ɵfac = function AuthInterceptor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.LoadingService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthInterceptor,\n    factory: AuthInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["throwError", "catchError", "finalize", "AuthInterceptor", "constructor", "authService", "router", "loadingService", "intercept", "request", "next", "show", "token", "getToken", "isTokenExpired", "clone", "setHeaders", "Authorization", "generateCSRFToken", "handle", "pipe", "error", "handleError", "hide", "status", "logout", "navigate", "queryParams", "message", "console", "warn", "Math", "random", "toString", "substring", "_", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "i3", "LoadingService", "_2", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, finalize } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\nimport { LoadingService } from '../services/loading.service';\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private loadingService: LoadingService\n  ) {}\n\n  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    // Show loading indicator\n    this.loadingService.show();\n\n    // Add auth token if available\n    const token = this.authService.getToken();\n    if (token && !this.authService.isTokenExpired()) {\n      request = request.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json',\n          'X-Requested-With': 'XMLHttpRequest'\n        }\n      });\n    }\n\n    // Add CSRF protection\n    request = request.clone({\n      setHeaders: {\n        'X-CSRF-Token': this.generateCSRFToken()\n      }\n    });\n\n    return next.handle(request).pipe(\n      catchError((error: HttpErrorResponse) => {\n        this.handleError(error);\n        return throwError(() => error);\n      }),\n      finalize(() => {\n        // Hide loading indicator\n        this.loadingService.hide();\n      })\n    );\n  }\n\n  private handleError(error: HttpErrorResponse): void {\n    switch (error.status) {\n      case 401:\n        // Unauthorized - token expired or invalid\n        this.authService.logout();\n        this.router.navigate(['/auth/login'], {\n          queryParams: { message: 'Session expired. Please login again.' }\n        });\n        break;\n      case 403:\n        // Forbidden - insufficient permissions\n        this.router.navigate(['/unauthorized']);\n        break;\n      case 429:\n        // Too many requests - rate limited\n        console.warn('Rate limit exceeded. Please try again later.');\n        break;\n      case 0:\n        // Network error\n        console.error('Network error. Please check your connection.');\n        break;\n      default:\n        console.error('HTTP Error:', error);\n    }\n  }\n\n  private generateCSRFToken(): string {\n    // Simple CSRF token generation\n    return Math.random().toString(36).substring(2, 15) + \n           Math.random().toString(36).substring(2, 15);\n  }\n}\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,QAAQ,QAAQ,gBAAgB;;;;;AAMrD,OAAM,MAAOC,eAAe;EAC1BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,cAA8B;IAF9B,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;EACrB;EAEHC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD;IACA,IAAI,CAACH,cAAc,CAACI,IAAI,EAAE;IAE1B;IACA,MAAMC,KAAK,GAAG,IAAI,CAACP,WAAW,CAACQ,QAAQ,EAAE;IACzC,IAAID,KAAK,IAAI,CAAC,IAAI,CAACP,WAAW,CAACS,cAAc,EAAE,EAAE;MAC/CL,OAAO,GAAGA,OAAO,CAACM,KAAK,CAAC;QACtBC,UAAU,EAAE;UACVC,aAAa,EAAE,UAAUL,KAAK,EAAE;UAChC,cAAc,EAAE,kBAAkB;UAClC,kBAAkB,EAAE;;OAEvB,CAAC;IACJ;IAEA;IACAH,OAAO,GAAGA,OAAO,CAACM,KAAK,CAAC;MACtBC,UAAU,EAAE;QACV,cAAc,EAAE,IAAI,CAACE,iBAAiB;;KAEzC,CAAC;IAEF,OAAOR,IAAI,CAACS,MAAM,CAACV,OAAO,CAAC,CAACW,IAAI,CAC9BnB,UAAU,CAAEoB,KAAwB,IAAI;MACtC,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;MACvB,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,EACFnB,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACK,cAAc,CAACgB,IAAI,EAAE;IAC5B,CAAC,CAAC,CACH;EACH;EAEQD,WAAWA,CAACD,KAAwB;IAC1C,QAAQA,KAAK,CAACG,MAAM;MAClB,KAAK,GAAG;QACN;QACA,IAAI,CAACnB,WAAW,CAACoB,MAAM,EAAE;QACzB,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;UACpCC,WAAW,EAAE;YAAEC,OAAO,EAAE;UAAsC;SAC/D,CAAC;QACF;MACF,KAAK,GAAG;QACN;QACA,IAAI,CAACtB,MAAM,CAACoB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;QACvC;MACF,KAAK,GAAG;QACN;QACAG,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;QAC5D;MACF,KAAK,CAAC;QACJ;QACAD,OAAO,CAACR,KAAK,CAAC,8CAA8C,CAAC;QAC7D;MACF;QACEQ,OAAO,CAACR,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACvC;EACF;EAEQH,iBAAiBA,CAAA;IACvB;IACA,OAAOa,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAC3CH,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EACpD;EAAC,QAAAC,CAAA,G;qCAxEUhC,eAAe,EAAAiC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfzC,eAAe;IAAA0C,OAAA,EAAf1C,eAAe,CAAA2C;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}