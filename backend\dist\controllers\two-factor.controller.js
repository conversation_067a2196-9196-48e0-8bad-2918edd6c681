"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwoFactorController = void 0;
const tslib_1 = require("tslib");
const authentication_1 = require("@loopback/authentication");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const security_1 = require("@loopback/security");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let TwoFactorController = class TwoFactorController {
    constructor(currentUserProfile, userRepository, securityService, emailService, smsService) {
        this.currentUserProfile = currentUserProfile;
        this.userRepository = userRepository;
        this.securityService = securityService;
        this.emailService = emailService;
        this.smsService = smsService;
    }
    async setup2FA() {
        const userId = this.currentUserProfile[security_1.securityId];
        return await this.securityService.generateTwoFactorSecret(userId);
    }
    async verify2FA(request) {
        const userId = this.currentUserProfile[security_1.securityId];
        await this.securityService.enableTwoFactor(userId, request.token);
        return { message: 'Two-factor authentication enabled successfully' };
    }
    async disable2FA(request) {
        const userId = this.currentUserProfile[security_1.securityId];
        await this.securityService.disableTwoFactor(userId, request.token);
        return { message: 'Two-factor authentication disabled successfully' };
    }
    async get2FAStatus() {
        const userId = this.currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        return { enabled: user.twoFactorEnabled || false };
    }
    async send2FASMS() {
        const userId = this.currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        if (!user.phone) {
            throw new rest_1.HttpErrors.BadRequest('Phone number not provided');
        }
        const otp = await this.securityService.generateOTP(user.phone, '2fa');
        await this.smsService.send2FASMS(user.phone, otp);
        return { message: 'SMS sent successfully' };
    }
    async verify2FASMS(request) {
        const userId = this.currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        if (!user.phone) {
            throw new rest_1.HttpErrors.BadRequest('Phone number not provided');
        }
        const isValid = await this.securityService.verifyOTP(user.phone, request.code, '2fa');
        return { valid: isValid };
    }
    async send2FAEmail() {
        const userId = this.currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        const otp = await this.securityService.generateOTP(user.email, '2fa');
        await this.emailService.sendOTPEmail(user.email, otp, '2fa');
        return { message: 'Email sent successfully' };
    }
    async verify2FAEmail(request) {
        const userId = this.currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        const isValid = await this.securityService.verifyOTP(user.email, request.code, '2fa');
        return { valid: isValid };
    }
};
exports.TwoFactorController = TwoFactorController;
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/setup'),
    (0, rest_1.response)(200, {
        description: 'Setup two-factor authentication',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        secret: { type: 'string' },
                        qrCode: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "setup2FA", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/verify'),
    (0, rest_1.response)(200, {
        description: 'Verify and enable two-factor authentication',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token'],
                    properties: {
                        token: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "verify2FA", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/disable'),
    (0, rest_1.response)(200, {
        description: 'Disable two-factor authentication',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token'],
                    properties: {
                        token: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "disable2FA", null);
tslib_1.__decorate([
    (0, rest_1.get)('/2fa/status'),
    (0, rest_1.response)(200, {
        description: 'Get two-factor authentication status',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        enabled: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "get2FAStatus", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/send-sms'),
    (0, rest_1.response)(200, {
        description: 'Send 2FA code via SMS',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "send2FASMS", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/verify-sms'),
    (0, rest_1.response)(200, {
        description: 'Verify 2FA SMS code',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        valid: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['code'],
                    properties: {
                        code: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "verify2FASMS", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/send-email'),
    (0, rest_1.response)(200, {
        description: 'Send 2FA code via email',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "send2FAEmail", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/verify-email'),
    (0, rest_1.response)(200, {
        description: 'Verify 2FA email code',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        valid: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['code'],
                    properties: {
                        code: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "verify2FAEmail", null);
exports.TwoFactorController = TwoFactorController = tslib_1.__decorate([
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(2, (0, core_1.inject)('services.SecurityService')),
    tslib_1.__param(3, (0, core_1.inject)('services.EmailService')),
    tslib_1.__param(4, (0, core_1.inject)('services.SmsService')),
    tslib_1.__metadata("design:paramtypes", [Object, repositories_1.UserRepository,
        services_1.SecurityService,
        services_1.EmailService,
        services_1.SmsService])
], TwoFactorController);
//# sourceMappingURL=two-factor.controller.js.map