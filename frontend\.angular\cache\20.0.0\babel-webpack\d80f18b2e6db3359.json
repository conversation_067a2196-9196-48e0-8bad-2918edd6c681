{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const EmptyError = createErrorClass(_super => function EmptyErrorImpl() {\n  _super(this);\n  this.name = 'EmptyError';\n  this.message = 'no elements in sequence';\n});", "map": {"version": 3, "names": ["createErrorClass", "EmptyError", "_super", "EmptyErrorImpl", "name", "message"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/rxjs/dist/esm/internal/util/EmptyError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport const EmptyError = createErrorClass((_super) => function EmptyErrorImpl() {\n    _super(this);\n    this.name = 'EmptyError';\n    this.message = 'no elements in sequence';\n});\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,MAAMC,UAAU,GAAGD,gBAAgB,CAAEE,MAAM,IAAK,SAASC,cAAcA,CAAA,EAAG;EAC7ED,MAAM,CAAC,IAAI,CAAC;EACZ,IAAI,CAACE,IAAI,GAAG,YAAY;EACxB,IAAI,CAACC,OAAO,GAAG,yBAAyB;AAC5C,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}