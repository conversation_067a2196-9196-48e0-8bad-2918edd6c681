#!/usr/bin/env node

/**
 * Service Binding Diagnostic Script
 * This script tests if all services are properly bound and working
 */

require('dotenv').config();

async function testServiceBindings() {
  console.log('🔍 TESTING SERVICE BINDINGS');
  console.log('============================\n');

  try {
    // Force PostgreSQL usage
    process.env.USE_POSTGRESQL = 'true';
    
    const { SecureBackendApplication } = require('../dist/application');
    const app = new SecureBackendApplication({
      rest: {
        port: 0, // Use random port for testing
        host: 'localhost',
      },
    });

    console.log('🚀 Booting application...');
    await app.boot();
    console.log('✅ Application booted successfully');

    // Test service bindings
    console.log('\n🔍 Testing service bindings...');

    try {
      const securityService = await app.get('services.SecurityService');
      console.log('✅ SecurityService bound:', !!securityService);
      
      // Test security service methods
      const passwordValidation = await securityService.validatePasswordStrength('TestPassword123!');
      console.log('✅ SecurityService.validatePasswordStrength working:', passwordValidation.isValid);
      
      const hashedPassword = await securityService.hashPassword('TestPassword123!');
      console.log('✅ SecurityService.hashPassword working:', !!hashedPassword);
      
    } catch (error) {
      console.log('❌ SecurityService error:', error.message);
    }

    try {
      const emailService = await app.get('services.EmailService');
      console.log('✅ EmailService bound:', !!emailService);
    } catch (error) {
      console.log('❌ EmailService error:', error.message);
    }

    try {
      const smsService = await app.get('services.SmsService');
      console.log('✅ SmsService bound:', !!smsService);
    } catch (error) {
      console.log('❌ SmsService error:', error.message);
    }

    try {
      const paymentService = await app.get('services.PaymentService');
      console.log('✅ PaymentService bound:', !!paymentService);
    } catch (error) {
      console.log('❌ PaymentService error:', error.message);
    }

    // Test repository bindings
    console.log('\n🔍 Testing repository bindings...');
    
    try {
      const { UserRepository } = require('../dist/repositories');
      const datasource = await app.get('datasources.db');
      const userRepository = new UserRepository(datasource);
      console.log('✅ UserRepository working:', !!userRepository);
      
      // Test database connection
      const userCount = await userRepository.count();
      console.log('✅ Database connection working, user count:', userCount.count);
      
    } catch (error) {
      console.log('❌ Repository error:', error.message);
    }

    // Test signup process step by step
    console.log('\n🔍 Testing signup process step by step...');
    
    try {
      const securityService = await app.get('services.SecurityService');
      const { UserRepository } = require('../dist/repositories');
      const datasource = await app.get('datasources.db');
      const userRepository = new UserRepository(datasource);
      
      const testEmail = `test-${Date.now()}@example.com`;
      const testPassword = 'TestPassword123!';
      
      console.log('📧 Test email:', testEmail);
      
      // Step 1: Check if email exists
      const existingUser = await userRepository.findOne({
        where: { email: testEmail }
      });
      console.log('✅ Email check:', existingUser ? 'exists' : 'available');
      
      // Step 2: Validate password
      const passwordValidation = await securityService.validatePasswordStrength(testPassword);
      console.log('✅ Password validation:', passwordValidation.isValid);
      
      // Step 3: Hash password
      const hashedPassword = await securityService.hashPassword(testPassword);
      console.log('✅ Password hashing:', !!hashedPassword);
      
      // Step 4: Create user
      const newUser = await userRepository.create({
        email: testEmail,
        firstName: 'Test',
        lastName: 'User',
        password: hashedPassword,
        emailVerified: true,
        roles: ['user'],
      });
      console.log('✅ User creation:', !!newUser.id);
      console.log('   User ID:', newUser.id);
      
      // Step 5: Verify user was created
      const createdUser = await userRepository.findById(newUser.id);
      console.log('✅ User verification:', createdUser.email === testEmail);
      
    } catch (error) {
      console.log('❌ Signup process error:', error.message);
      console.log('   Stack:', error.stack);
    }

    await app.stop();
    console.log('\n🎉 Service binding test completed!');

  } catch (error) {
    console.error('❌ Service binding test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

if (require.main === module) {
  testServiceBindings().catch(console.error);
}

module.exports = { testServiceBindings };
