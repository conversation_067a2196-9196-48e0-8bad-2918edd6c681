# Server Configuration
PORT=3000
HOST=localhost
NODE_ENV=development

# Frontend URL
FRONTEND_URL=http://localhost:3001

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Database Configuration (for production)
USE_POSTGRESQL="true"
DB_HOST=localhost
DB_PORT=5432
DB_NAME=secure_backend
DB_USER=postgres
DB_PASSWORD=password

# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret

# Email Configuration (Gmail SMTP)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_SID=your_twilio_sid
TWILIO_TOKEN=your_twilio_token
TWILIO_PHONE=+1234567890

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCK_TIME=30

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
