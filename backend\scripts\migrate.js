#!/usr/bin/env node

/**
 * Database Migration Script
 * This script creates the database schema and initial data
 */

require('dotenv').config();

async function migrate() {
  console.log('🗄️  STARTING DATABASE MIGRATION');
  console.log('================================\n');

  let app;

  try {
    // Import after dotenv is loaded
    const { SecureBackendApplication } = require('../dist/application');

    // Create application instance
    console.log('🚀 Initializing application...');
    app = new SecureBackendApplication();
    await app.boot();
    console.log('✅ Application initialized\n');

    // Get datasource
    console.log('🔌 Connecting to database...');
    const datasource = await app.get('datasources.db');

    console.log('📋 Database Configuration:');
    console.log(`   Connector: ${datasource.settings.connector}`);
    console.log(`   Database: ${datasource.settings.database || 'In-Memory'}`);
    console.log(`   Host: ${datasource.settings.host || 'N/A'}`);
    console.log(`   Port: ${datasource.settings.port || 'N/A'}`);
    console.log(`   User: ${datasource.settings.user || 'N/A'}\n`);
    
    // Test database connection
    console.log('� Testing database connection...');
    try {
      await datasource.ping();
      console.log('✅ Database connection successful\n');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      if (datasource.settings.connector === 'postgresql') {
        console.log('\n� PostgreSQL Connection Troubleshooting:');
        console.log('1. Make sure PostgreSQL is running');
        console.log('2. Check your database credentials in .env');
        console.log('3. Ensure the database exists');
        console.log('4. Verify network connectivity\n');
      }
      throw error;
    }

    // Auto-migrate database schema
    console.log('🔧 Creating/updating database schema...');
    await datasource.automigrate();
    console.log('✅ Database schema migration completed\n');

    // Verify schema creation for PostgreSQL
    if (datasource.settings.connector === 'postgresql') {
      try {
        const query = `
          SELECT table_name
          FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_type = 'BASE TABLE'
          ORDER BY table_name
        `;
        const result = await datasource.execute(query);
        console.log('📋 Database tables:');
        result.forEach(row => console.log(`   - ${row.table_name}`));
        console.log('');
      } catch (error) {
        console.warn('⚠️  Could not verify tables:', error.message);
      }
    }
    
    // Create initial data if needed
    await createInitialData(app);
    
    console.log('🎉 Database migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    // Clean up
    if (app) {
      try {
        await app.stop();
        console.log('🔌 Application stopped gracefully');
      } catch (error) {
        console.warn('⚠️  Error stopping application:', error.message);
      }
    }
  }
}

async function createInitialData(app) {
  console.log('📝 Creating initial data...');

  try {
    // Get the user repository
    let userRepository;

    try {
      // Try to get repository through the application
      const { UserRepository } = require('../dist/repositories');
      const datasource = await app.get('datasources.db');
      userRepository = new UserRepository(datasource);
      console.log('✅ UserRepository initialized');
    } catch (error) {
      console.error('❌ Failed to initialize UserRepository:', error.message);
      return;
    }
    
    // Check if admin user exists
    const existingAdmin = await userRepository.findOne({
      where: { email: '<EMAIL>' }
    });
    
    if (!existingAdmin) {
      // Create admin user
      const bcrypt = require('bcryptjs');
      const adminPassword = await bcrypt.hash('Admin123!@#', 12);
      
      const adminUser = await userRepository.create({
        email: '<EMAIL>',
        firstName: 'System',
        lastName: 'Administrator',
        password: adminPassword,
        emailVerified: true,
        phoneVerified: false,
        twoFactorEnabled: false,
        roles: ['admin', 'user'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      console.log(`✅ Admin user created: ${adminUser.email}`);
    } else {
      console.log('ℹ️  Admin user already exists');
    }
    
    // Create test user for development
    if (process.env.NODE_ENV !== 'production') {
      const existingTest = await userRepository.findOne({
        where: { email: '<EMAIL>' }
      });
      
      if (!existingTest) {
        const bcrypt = require('bcryptjs');
        const testPassword = await bcrypt.hash('Test123!@#', 12);
        
        const testUser = await userRepository.create({
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          password: testPassword,
          emailVerified: true,
          phoneVerified: false,
          twoFactorEnabled: false,
          roles: ['user'],
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        
        console.log(`✅ Test user created: ${testUser.email}`);
      } else {
        console.log('ℹ️  Test user already exists');
      }
    }
    
    console.log('✅ Initial data setup completed!\n');
    
  } catch (error) {
    console.warn('⚠️  Initial data creation failed:', error.message);
  }
}

// Run migration if called directly
if (require.main === module) {
  migrate().catch(console.error);
}

module.exports = { migrate, createInitialData };
