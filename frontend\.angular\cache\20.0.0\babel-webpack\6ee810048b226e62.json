{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The default equality function used for `signal` and `computed`, which uses referential equality.\n */\nfunction defaultEquals(a, b) {\n  return Object.is(a, b);\n}\n\n/**\n * The currently active consumer `ReactiveNode`, if running code in a reactive context.\n *\n * Change this via `setActiveConsumer`.\n */\nlet activeConsumer = null;\nlet inNotificationPhase = false;\n/**\n * Global epoch counter. Incremented whenever a source signal is set.\n */\nlet epoch = 1;\n/**\n * If set, called after a producer `ReactiveNode` is created.\n */\nlet postProducerCreatedFn = null;\n/**\n * Symbol used to tell `Signal`s apart from other functions.\n *\n * This can be used to auto-unwrap signals in various cases, or to auto-wrap non-signal values.\n */\nconst SIGNAL = /* @__PURE__ */Symbol('SIGNAL');\nfunction setActiveConsumer(consumer) {\n  const prev = activeConsumer;\n  activeConsumer = consumer;\n  return prev;\n}\nfunction getActiveConsumer() {\n  return activeConsumer;\n}\nfunction isInNotificationPhase() {\n  return inNotificationPhase;\n}\nfunction isReactive(value) {\n  return value[SIGNAL] !== undefined;\n}\nconst REACTIVE_NODE = {\n  version: 0,\n  lastCleanEpoch: 0,\n  dirty: false,\n  producerNode: undefined,\n  producerLastReadVersion: undefined,\n  producerIndexOfThis: undefined,\n  nextProducerIndex: 0,\n  liveConsumerNode: undefined,\n  liveConsumerIndexOfThis: undefined,\n  consumerAllowSignalWrites: false,\n  consumerIsAlwaysLive: false,\n  kind: 'unknown',\n  producerMustRecompute: () => false,\n  producerRecomputeValue: () => {},\n  consumerMarkedDirty: () => {},\n  consumerOnSignalRead: () => {}\n};\n/**\n * Called by implementations when a producer's signal is read.\n */\nfunction producerAccessed(node) {\n  if (inNotificationPhase) {\n    throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ? `Assertion error: signal read during notification phase` : '');\n  }\n  if (activeConsumer === null) {\n    // Accessed outside of a reactive context, so nothing to record.\n    return;\n  }\n  activeConsumer.consumerOnSignalRead(node);\n  // This producer is the `idx`th dependency of `activeConsumer`.\n  const idx = activeConsumer.nextProducerIndex++;\n  assertConsumerNode(activeConsumer);\n  if (idx < activeConsumer.producerNode.length && activeConsumer.producerNode[idx] !== node) {\n    // There's been a change in producers since the last execution of `activeConsumer`.\n    // `activeConsumer.producerNode[idx]` holds a stale dependency which will be be removed and\n    // replaced with `this`.\n    //\n    // If `activeConsumer` isn't live, then this is a no-op, since we can replace the producer in\n    // `activeConsumer.producerNode` directly. However, if `activeConsumer` is live, then we need\n    // to remove it from the stale producer's `liveConsumer`s.\n    if (consumerIsLive(activeConsumer)) {\n      const staleProducer = activeConsumer.producerNode[idx];\n      producerRemoveLiveConsumerAtIndex(staleProducer, activeConsumer.producerIndexOfThis[idx]);\n      // At this point, the only record of `staleProducer` is the reference at\n      // `activeConsumer.producerNode[idx]` which will be overwritten below.\n    }\n  }\n  if (activeConsumer.producerNode[idx] !== node) {\n    // We're a new dependency of the consumer (at `idx`).\n    activeConsumer.producerNode[idx] = node;\n    // If the active consumer is live, then add it as a live consumer. If not, then use 0 as a\n    // placeholder value.\n    activeConsumer.producerIndexOfThis[idx] = consumerIsLive(activeConsumer) ? producerAddLiveConsumer(node, activeConsumer, idx) : 0;\n  }\n  activeConsumer.producerLastReadVersion[idx] = node.version;\n}\n/**\n * Increment the global epoch counter.\n *\n * Called by source producers (that is, not computeds) whenever their values change.\n */\nfunction producerIncrementEpoch() {\n  epoch++;\n}\n/**\n * Ensure this producer's `version` is up-to-date.\n */\nfunction producerUpdateValueVersion(node) {\n  if (consumerIsLive(node) && !node.dirty) {\n    // A live consumer will be marked dirty by producers, so a clean state means that its version\n    // is guaranteed to be up-to-date.\n    return;\n  }\n  if (!node.dirty && node.lastCleanEpoch === epoch) {\n    // Even non-live consumers can skip polling if they previously found themselves to be clean at\n    // the current epoch, since their dependencies could not possibly have changed (such a change\n    // would've increased the epoch).\n    return;\n  }\n  if (!node.producerMustRecompute(node) && !consumerPollProducersForChange(node)) {\n    // None of our producers report a change since the last time they were read, so no\n    // recomputation of our value is necessary, and we can consider ourselves clean.\n    producerMarkClean(node);\n    return;\n  }\n  node.producerRecomputeValue(node);\n  // After recomputing the value, we're no longer dirty.\n  producerMarkClean(node);\n}\n/**\n * Propagate a dirty notification to live consumers of this producer.\n */\nfunction producerNotifyConsumers(node) {\n  if (node.liveConsumerNode === undefined) {\n    return;\n  }\n  // Prevent signal reads when we're updating the graph\n  const prev = inNotificationPhase;\n  inNotificationPhase = true;\n  try {\n    for (const consumer of node.liveConsumerNode) {\n      if (!consumer.dirty) {\n        consumerMarkDirty(consumer);\n      }\n    }\n  } finally {\n    inNotificationPhase = prev;\n  }\n}\n/**\n * Whether this `ReactiveNode` in its producer capacity is currently allowed to initiate updates,\n * based on the current consumer context.\n */\nfunction producerUpdatesAllowed() {\n  return activeConsumer?.consumerAllowSignalWrites !== false;\n}\nfunction consumerMarkDirty(node) {\n  node.dirty = true;\n  producerNotifyConsumers(node);\n  node.consumerMarkedDirty?.(node);\n}\nfunction producerMarkClean(node) {\n  node.dirty = false;\n  node.lastCleanEpoch = epoch;\n}\n/**\n * Prepare this consumer to run a computation in its reactive context.\n *\n * Must be called by subclasses which represent reactive computations, before those computations\n * begin.\n */\nfunction consumerBeforeComputation(node) {\n  node && (node.nextProducerIndex = 0);\n  return setActiveConsumer(node);\n}\n/**\n * Finalize this consumer's state after a reactive computation has run.\n *\n * Must be called by subclasses which represent reactive computations, after those computations\n * have finished.\n */\nfunction consumerAfterComputation(node, prevConsumer) {\n  setActiveConsumer(prevConsumer);\n  if (!node || node.producerNode === undefined || node.producerIndexOfThis === undefined || node.producerLastReadVersion === undefined) {\n    return;\n  }\n  if (consumerIsLive(node)) {\n    // For live consumers, we need to remove the producer -> consumer edge for any stale producers\n    // which weren't dependencies after the recomputation.\n    for (let i = node.nextProducerIndex; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n  // Truncate the producer tracking arrays.\n  // Perf note: this is essentially truncating the length to `node.nextProducerIndex`, but\n  // benchmarking has shown that individual pop operations are faster.\n  while (node.producerNode.length > node.nextProducerIndex) {\n    node.producerNode.pop();\n    node.producerLastReadVersion.pop();\n    node.producerIndexOfThis.pop();\n  }\n}\n/**\n * Determine whether this consumer has any dependencies which have changed since the last time\n * they were read.\n */\nfunction consumerPollProducersForChange(node) {\n  assertConsumerNode(node);\n  // Poll producers for change.\n  for (let i = 0; i < node.producerNode.length; i++) {\n    const producer = node.producerNode[i];\n    const seenVersion = node.producerLastReadVersion[i];\n    // First check the versions. A mismatch means that the producer's value is known to have\n    // changed since the last time we read it.\n    if (seenVersion !== producer.version) {\n      return true;\n    }\n    // The producer's version is the same as the last time we read it, but it might itself be\n    // stale. Force the producer to recompute its version (calculating a new value if necessary).\n    producerUpdateValueVersion(producer);\n    // Now when we do this check, `producer.version` is guaranteed to be up to date, so if the\n    // versions still match then it has not changed since the last time we read it.\n    if (seenVersion !== producer.version) {\n      return true;\n    }\n  }\n  return false;\n}\n/**\n * Disconnect this consumer from the graph.\n */\nfunction consumerDestroy(node) {\n  assertConsumerNode(node);\n  if (consumerIsLive(node)) {\n    // Drop all connections from the graph to this node.\n    for (let i = 0; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n  // Truncate all the arrays to drop all connection from this node to the graph.\n  node.producerNode.length = node.producerLastReadVersion.length = node.producerIndexOfThis.length = 0;\n  if (node.liveConsumerNode) {\n    node.liveConsumerNode.length = node.liveConsumerIndexOfThis.length = 0;\n  }\n}\n/**\n * Add `consumer` as a live consumer of this node.\n *\n * Note that this operation is potentially transitive. If this node becomes live, then it becomes\n * a live consumer of all of its current producers.\n */\nfunction producerAddLiveConsumer(node, consumer, indexOfThis) {\n  assertProducerNode(node);\n  if (node.liveConsumerNode.length === 0 && isConsumerNode(node)) {\n    // When going from 0 to 1 live consumers, we become a live consumer to our producers.\n    for (let i = 0; i < node.producerNode.length; i++) {\n      node.producerIndexOfThis[i] = producerAddLiveConsumer(node.producerNode[i], node, i);\n    }\n  }\n  node.liveConsumerIndexOfThis.push(indexOfThis);\n  return node.liveConsumerNode.push(consumer) - 1;\n}\n/**\n * Remove the live consumer at `idx`.\n */\nfunction producerRemoveLiveConsumerAtIndex(node, idx) {\n  assertProducerNode(node);\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && idx >= node.liveConsumerNode.length) {\n    throw new Error(`Assertion error: active consumer index ${idx} is out of bounds of ${node.liveConsumerNode.length} consumers)`);\n  }\n  if (node.liveConsumerNode.length === 1 && isConsumerNode(node)) {\n    // When removing the last live consumer, we will no longer be live. We need to remove\n    // ourselves from our producers' tracking (which may cause consumer-producers to lose\n    // liveness as well).\n    for (let i = 0; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n  // Move the last value of `liveConsumers` into `idx`. Note that if there's only a single\n  // live consumer, this is a no-op.\n  const lastIdx = node.liveConsumerNode.length - 1;\n  node.liveConsumerNode[idx] = node.liveConsumerNode[lastIdx];\n  node.liveConsumerIndexOfThis[idx] = node.liveConsumerIndexOfThis[lastIdx];\n  // Truncate the array.\n  node.liveConsumerNode.length--;\n  node.liveConsumerIndexOfThis.length--;\n  // If the index is still valid, then we need to fix the index pointer from the producer to this\n  // consumer, and update it from `lastIdx` to `idx` (accounting for the move above).\n  if (idx < node.liveConsumerNode.length) {\n    const idxProducer = node.liveConsumerIndexOfThis[idx];\n    const consumer = node.liveConsumerNode[idx];\n    assertConsumerNode(consumer);\n    consumer.producerIndexOfThis[idxProducer] = idx;\n  }\n}\nfunction consumerIsLive(node) {\n  return node.consumerIsAlwaysLive || (node?.liveConsumerNode?.length ?? 0) > 0;\n}\nfunction assertConsumerNode(node) {\n  node.producerNode ??= [];\n  node.producerIndexOfThis ??= [];\n  node.producerLastReadVersion ??= [];\n}\nfunction assertProducerNode(node) {\n  node.liveConsumerNode ??= [];\n  node.liveConsumerIndexOfThis ??= [];\n}\nfunction isConsumerNode(node) {\n  return node.producerNode !== undefined;\n}\nfunction runPostProducerCreatedFn(node) {\n  postProducerCreatedFn?.(node);\n}\nfunction setPostProducerCreatedFn(fn) {\n  const prev = postProducerCreatedFn;\n  postProducerCreatedFn = fn;\n  return prev;\n}\n\n/**\n * Create a computed signal which derives a reactive value from an expression.\n */\nfunction createComputed(computation, equal) {\n  const node = Object.create(COMPUTED_NODE);\n  node.computation = computation;\n  if (equal !== undefined) {\n    node.equal = equal;\n  }\n  const computed = () => {\n    // Check if the value needs updating before returning it.\n    producerUpdateValueVersion(node);\n    // Record that someone looked at this signal.\n    producerAccessed(node);\n    if (node.value === ERRORED) {\n      throw node.error;\n    }\n    return node.value;\n  };\n  computed[SIGNAL] = node;\n  if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n    const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n    computed.toString = () => `[Computed${debugName}: ${node.value}]`;\n  }\n  runPostProducerCreatedFn(node);\n  return computed;\n}\n/**\n * A dedicated symbol used before a computed value has been calculated for the first time.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst UNSET = /* @__PURE__ */Symbol('UNSET');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * is in progress. Used to detect cycles in computation chains.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst COMPUTING = /* @__PURE__ */Symbol('COMPUTING');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * failed. The thrown error is cached until the computation gets dirty again.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst ERRORED = /* @__PURE__ */Symbol('ERRORED');\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst COMPUTED_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    value: UNSET,\n    dirty: true,\n    error: null,\n    equal: defaultEquals,\n    kind: 'computed',\n    producerMustRecompute(node) {\n      // Force a recomputation if there's no current value, or if the current value is in the\n      // process of being calculated (which should throw an error).\n      return node.value === UNSET || node.value === COMPUTING;\n    },\n    producerRecomputeValue(node) {\n      if (node.value === COMPUTING) {\n        // Our computation somehow led to a cyclic read of itself.\n        throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ? 'Detected cycle in computations.' : '');\n      }\n      const oldValue = node.value;\n      node.value = COMPUTING;\n      const prevConsumer = consumerBeforeComputation(node);\n      let newValue;\n      let wasEqual = false;\n      try {\n        newValue = node.computation();\n        // We want to mark this node as errored if calling `equal` throws; however, we don't want\n        // to track any reactive reads inside `equal`.\n        setActiveConsumer(null);\n        wasEqual = oldValue !== UNSET && oldValue !== ERRORED && newValue !== ERRORED && node.equal(oldValue, newValue);\n      } catch (err) {\n        newValue = ERRORED;\n        node.error = err;\n      } finally {\n        consumerAfterComputation(node, prevConsumer);\n      }\n      if (wasEqual) {\n        // No change to `valueVersion` - old and new values are\n        // semantically equivalent.\n        node.value = oldValue;\n        return;\n      }\n      node.value = newValue;\n      node.version++;\n    }\n  };\n})();\nfunction defaultThrowError() {\n  throw new Error();\n}\nlet throwInvalidWriteToSignalErrorFn = defaultThrowError;\nfunction throwInvalidWriteToSignalError(node) {\n  throwInvalidWriteToSignalErrorFn(node);\n}\nfunction setThrowInvalidWriteToSignalError(fn) {\n  throwInvalidWriteToSignalErrorFn = fn;\n}\n\n/**\n * If set, called after `WritableSignal`s are updated.\n *\n * This hook can be used to achieve various effects, such as running effects synchronously as part\n * of setting a signal.\n */\nlet postSignalSetFn = null;\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nfunction createSignal(initialValue, equal) {\n  const node = Object.create(SIGNAL_NODE);\n  node.value = initialValue;\n  if (equal !== undefined) {\n    node.equal = equal;\n  }\n  const getter = () => signalGetFn(node);\n  getter[SIGNAL] = node;\n  if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n    const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n    getter.toString = () => `[Signal${debugName}: ${node.value}]`;\n  }\n  runPostProducerCreatedFn(node);\n  return getter;\n}\n/**\n * Creates a `Signal` getter, setter, and updater function.\n */\nfunction createSignalTuple(initialValue, equal) {\n  const getter = createSignal(initialValue, equal);\n  const node = getter[SIGNAL];\n  const set = newValue => signalSetFn(node, newValue);\n  const update = updateFn => signalUpdateFn(node, updateFn);\n  return [getter, set, update];\n}\nfunction setPostSignalSetFn(fn) {\n  const prev = postSignalSetFn;\n  postSignalSetFn = fn;\n  return prev;\n}\nfunction signalGetFn(node) {\n  producerAccessed(node);\n  return node.value;\n}\nfunction signalSetFn(node, newValue) {\n  if (!producerUpdatesAllowed()) {\n    throwInvalidWriteToSignalError(node);\n  }\n  if (!node.equal(node.value, newValue)) {\n    node.value = newValue;\n    signalValueChanged(node);\n  }\n}\nfunction signalUpdateFn(node, updater) {\n  if (!producerUpdatesAllowed()) {\n    throwInvalidWriteToSignalError(node);\n  }\n  signalSetFn(node, updater(node.value));\n}\nfunction runPostSignalSetFn(node) {\n  postSignalSetFn?.(node);\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst SIGNAL_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    equal: defaultEquals,\n    value: undefined,\n    kind: 'signal'\n  };\n})();\nfunction signalValueChanged(node) {\n  node.version++;\n  producerIncrementEpoch();\n  producerNotifyConsumers(node);\n  postSignalSetFn?.(node);\n}\nexport { COMPUTING, ERRORED, REACTIVE_NODE, SIGNAL, SIGNAL_NODE, UNSET, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, createComputed, createSignal, createSignalTuple, defaultEquals, getActiveConsumer, isInNotificationPhase, isReactive, producerAccessed, producerIncrementEpoch, producerMarkClean, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, runPostProducerCreatedFn, runPostSignalSetFn, setActiveConsumer, setPostProducerCreatedFn, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalGetFn, signalSetFn, signalUpdateFn };", "map": {"version": 3, "names": ["defaultEquals", "a", "b", "Object", "is", "activeConsumer", "inNotificationPhase", "epoch", "postProducerCreatedFn", "SIGNAL", "Symbol", "setActiveConsumer", "consumer", "prev", "getActiveConsumer", "isInNotificationPhase", "isReactive", "value", "undefined", "REACTIVE_NODE", "version", "lastCleanEpoch", "dirty", "producerNode", "producerLastReadVersion", "producerIndexOfThis", "nextProducerIndex", "liveConsumerNode", "liveConsumerIndexOfThis", "consumerAllowSignalWrites", "consumerIsAlwaysLive", "kind", "producerMustRecompute", "producerRecomputeValue", "consumerMarkedDirty", "consumerOnSignalRead", "producerAccessed", "node", "Error", "ngDevMode", "idx", "assertConsumerNode", "length", "consumerIsLive", "staleProducer", "producerRemoveLiveConsumerAtIndex", "producerAddLiveConsumer", "producerIncrementEpoch", "producerUpdateValueVersion", "consumerPollProducersForChange", "producerMark<PERSON><PERSON>", "producerNotifyConsumers", "consumerMarkDirty", "producer<PERSON><PERSON>datesAllowed", "consumerBeforeComputation", "consumerAfterComputation", "prevConsumer", "i", "pop", "producer", "seenVersion", "consumerDestroy", "indexOfThis", "assertProducerNode", "isConsumerNode", "push", "lastIdx", "idxProducer", "runPostProducerCreatedFn", "setPostProducerCreatedFn", "fn", "createComputed", "computation", "equal", "create", "COMPUTED_NODE", "computed", "ERRORED", "error", "debugName", "toString", "UNSET", "COMPUTING", "oldValue", "newValue", "wasEqual", "err", "defaultThrowError", "throwInvalidWriteToSignalErrorFn", "throwInvalidWriteToSignalError", "setThrowInvalidWriteToSignalError", "postSignalSetFn", "createSignal", "initialValue", "SIGNAL_NODE", "getter", "signalGetFn", "createSignalTuple", "set", "signalSetFn", "update", "updateFn", "signalUpdateFn", "setPostSignalSetFn", "signalValueChanged", "updater", "runPostSignalSetFn"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/core/fesm2022/signal-ePSl6jXn.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The default equality function used for `signal` and `computed`, which uses referential equality.\n */\nfunction defaultEquals(a, b) {\n    return Object.is(a, b);\n}\n\n/**\n * The currently active consumer `ReactiveNode`, if running code in a reactive context.\n *\n * Change this via `setActiveConsumer`.\n */\nlet activeConsumer = null;\nlet inNotificationPhase = false;\n/**\n * Global epoch counter. Incremented whenever a source signal is set.\n */\nlet epoch = 1;\n/**\n * If set, called after a producer `ReactiveNode` is created.\n */\nlet postProducerCreatedFn = null;\n/**\n * Symbol used to tell `Signal`s apart from other functions.\n *\n * This can be used to auto-unwrap signals in various cases, or to auto-wrap non-signal values.\n */\nconst SIGNAL = /* @__PURE__ */ Symbol('SIGNAL');\nfunction setActiveConsumer(consumer) {\n    const prev = activeConsumer;\n    activeConsumer = consumer;\n    return prev;\n}\nfunction getActiveConsumer() {\n    return activeConsumer;\n}\nfunction isInNotificationPhase() {\n    return inNotificationPhase;\n}\nfunction isReactive(value) {\n    return value[SIGNAL] !== undefined;\n}\nconst REACTIVE_NODE = {\n    version: 0,\n    lastCleanEpoch: 0,\n    dirty: false,\n    producerNode: undefined,\n    producerLastReadVersion: undefined,\n    producerIndexOfThis: undefined,\n    nextProducerIndex: 0,\n    liveConsumerNode: undefined,\n    liveConsumerIndexOfThis: undefined,\n    consumerAllowSignalWrites: false,\n    consumerIsAlwaysLive: false,\n    kind: 'unknown',\n    producerMustRecompute: () => false,\n    producerRecomputeValue: () => { },\n    consumerMarkedDirty: () => { },\n    consumerOnSignalRead: () => { },\n};\n/**\n * Called by implementations when a producer's signal is read.\n */\nfunction producerAccessed(node) {\n    if (inNotificationPhase) {\n        throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode\n            ? `Assertion error: signal read during notification phase`\n            : '');\n    }\n    if (activeConsumer === null) {\n        // Accessed outside of a reactive context, so nothing to record.\n        return;\n    }\n    activeConsumer.consumerOnSignalRead(node);\n    // This producer is the `idx`th dependency of `activeConsumer`.\n    const idx = activeConsumer.nextProducerIndex++;\n    assertConsumerNode(activeConsumer);\n    if (idx < activeConsumer.producerNode.length && activeConsumer.producerNode[idx] !== node) {\n        // There's been a change in producers since the last execution of `activeConsumer`.\n        // `activeConsumer.producerNode[idx]` holds a stale dependency which will be be removed and\n        // replaced with `this`.\n        //\n        // If `activeConsumer` isn't live, then this is a no-op, since we can replace the producer in\n        // `activeConsumer.producerNode` directly. However, if `activeConsumer` is live, then we need\n        // to remove it from the stale producer's `liveConsumer`s.\n        if (consumerIsLive(activeConsumer)) {\n            const staleProducer = activeConsumer.producerNode[idx];\n            producerRemoveLiveConsumerAtIndex(staleProducer, activeConsumer.producerIndexOfThis[idx]);\n            // At this point, the only record of `staleProducer` is the reference at\n            // `activeConsumer.producerNode[idx]` which will be overwritten below.\n        }\n    }\n    if (activeConsumer.producerNode[idx] !== node) {\n        // We're a new dependency of the consumer (at `idx`).\n        activeConsumer.producerNode[idx] = node;\n        // If the active consumer is live, then add it as a live consumer. If not, then use 0 as a\n        // placeholder value.\n        activeConsumer.producerIndexOfThis[idx] = consumerIsLive(activeConsumer)\n            ? producerAddLiveConsumer(node, activeConsumer, idx)\n            : 0;\n    }\n    activeConsumer.producerLastReadVersion[idx] = node.version;\n}\n/**\n * Increment the global epoch counter.\n *\n * Called by source producers (that is, not computeds) whenever their values change.\n */\nfunction producerIncrementEpoch() {\n    epoch++;\n}\n/**\n * Ensure this producer's `version` is up-to-date.\n */\nfunction producerUpdateValueVersion(node) {\n    if (consumerIsLive(node) && !node.dirty) {\n        // A live consumer will be marked dirty by producers, so a clean state means that its version\n        // is guaranteed to be up-to-date.\n        return;\n    }\n    if (!node.dirty && node.lastCleanEpoch === epoch) {\n        // Even non-live consumers can skip polling if they previously found themselves to be clean at\n        // the current epoch, since their dependencies could not possibly have changed (such a change\n        // would've increased the epoch).\n        return;\n    }\n    if (!node.producerMustRecompute(node) && !consumerPollProducersForChange(node)) {\n        // None of our producers report a change since the last time they were read, so no\n        // recomputation of our value is necessary, and we can consider ourselves clean.\n        producerMarkClean(node);\n        return;\n    }\n    node.producerRecomputeValue(node);\n    // After recomputing the value, we're no longer dirty.\n    producerMarkClean(node);\n}\n/**\n * Propagate a dirty notification to live consumers of this producer.\n */\nfunction producerNotifyConsumers(node) {\n    if (node.liveConsumerNode === undefined) {\n        return;\n    }\n    // Prevent signal reads when we're updating the graph\n    const prev = inNotificationPhase;\n    inNotificationPhase = true;\n    try {\n        for (const consumer of node.liveConsumerNode) {\n            if (!consumer.dirty) {\n                consumerMarkDirty(consumer);\n            }\n        }\n    }\n    finally {\n        inNotificationPhase = prev;\n    }\n}\n/**\n * Whether this `ReactiveNode` in its producer capacity is currently allowed to initiate updates,\n * based on the current consumer context.\n */\nfunction producerUpdatesAllowed() {\n    return activeConsumer?.consumerAllowSignalWrites !== false;\n}\nfunction consumerMarkDirty(node) {\n    node.dirty = true;\n    producerNotifyConsumers(node);\n    node.consumerMarkedDirty?.(node);\n}\nfunction producerMarkClean(node) {\n    node.dirty = false;\n    node.lastCleanEpoch = epoch;\n}\n/**\n * Prepare this consumer to run a computation in its reactive context.\n *\n * Must be called by subclasses which represent reactive computations, before those computations\n * begin.\n */\nfunction consumerBeforeComputation(node) {\n    node && (node.nextProducerIndex = 0);\n    return setActiveConsumer(node);\n}\n/**\n * Finalize this consumer's state after a reactive computation has run.\n *\n * Must be called by subclasses which represent reactive computations, after those computations\n * have finished.\n */\nfunction consumerAfterComputation(node, prevConsumer) {\n    setActiveConsumer(prevConsumer);\n    if (!node ||\n        node.producerNode === undefined ||\n        node.producerIndexOfThis === undefined ||\n        node.producerLastReadVersion === undefined) {\n        return;\n    }\n    if (consumerIsLive(node)) {\n        // For live consumers, we need to remove the producer -> consumer edge for any stale producers\n        // which weren't dependencies after the recomputation.\n        for (let i = node.nextProducerIndex; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Truncate the producer tracking arrays.\n    // Perf note: this is essentially truncating the length to `node.nextProducerIndex`, but\n    // benchmarking has shown that individual pop operations are faster.\n    while (node.producerNode.length > node.nextProducerIndex) {\n        node.producerNode.pop();\n        node.producerLastReadVersion.pop();\n        node.producerIndexOfThis.pop();\n    }\n}\n/**\n * Determine whether this consumer has any dependencies which have changed since the last time\n * they were read.\n */\nfunction consumerPollProducersForChange(node) {\n    assertConsumerNode(node);\n    // Poll producers for change.\n    for (let i = 0; i < node.producerNode.length; i++) {\n        const producer = node.producerNode[i];\n        const seenVersion = node.producerLastReadVersion[i];\n        // First check the versions. A mismatch means that the producer's value is known to have\n        // changed since the last time we read it.\n        if (seenVersion !== producer.version) {\n            return true;\n        }\n        // The producer's version is the same as the last time we read it, but it might itself be\n        // stale. Force the producer to recompute its version (calculating a new value if necessary).\n        producerUpdateValueVersion(producer);\n        // Now when we do this check, `producer.version` is guaranteed to be up to date, so if the\n        // versions still match then it has not changed since the last time we read it.\n        if (seenVersion !== producer.version) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * Disconnect this consumer from the graph.\n */\nfunction consumerDestroy(node) {\n    assertConsumerNode(node);\n    if (consumerIsLive(node)) {\n        // Drop all connections from the graph to this node.\n        for (let i = 0; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Truncate all the arrays to drop all connection from this node to the graph.\n    node.producerNode.length =\n        node.producerLastReadVersion.length =\n            node.producerIndexOfThis.length =\n                0;\n    if (node.liveConsumerNode) {\n        node.liveConsumerNode.length = node.liveConsumerIndexOfThis.length = 0;\n    }\n}\n/**\n * Add `consumer` as a live consumer of this node.\n *\n * Note that this operation is potentially transitive. If this node becomes live, then it becomes\n * a live consumer of all of its current producers.\n */\nfunction producerAddLiveConsumer(node, consumer, indexOfThis) {\n    assertProducerNode(node);\n    if (node.liveConsumerNode.length === 0 && isConsumerNode(node)) {\n        // When going from 0 to 1 live consumers, we become a live consumer to our producers.\n        for (let i = 0; i < node.producerNode.length; i++) {\n            node.producerIndexOfThis[i] = producerAddLiveConsumer(node.producerNode[i], node, i);\n        }\n    }\n    node.liveConsumerIndexOfThis.push(indexOfThis);\n    return node.liveConsumerNode.push(consumer) - 1;\n}\n/**\n * Remove the live consumer at `idx`.\n */\nfunction producerRemoveLiveConsumerAtIndex(node, idx) {\n    assertProducerNode(node);\n    if (typeof ngDevMode !== 'undefined' && ngDevMode && idx >= node.liveConsumerNode.length) {\n        throw new Error(`Assertion error: active consumer index ${idx} is out of bounds of ${node.liveConsumerNode.length} consumers)`);\n    }\n    if (node.liveConsumerNode.length === 1 && isConsumerNode(node)) {\n        // When removing the last live consumer, we will no longer be live. We need to remove\n        // ourselves from our producers' tracking (which may cause consumer-producers to lose\n        // liveness as well).\n        for (let i = 0; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Move the last value of `liveConsumers` into `idx`. Note that if there's only a single\n    // live consumer, this is a no-op.\n    const lastIdx = node.liveConsumerNode.length - 1;\n    node.liveConsumerNode[idx] = node.liveConsumerNode[lastIdx];\n    node.liveConsumerIndexOfThis[idx] = node.liveConsumerIndexOfThis[lastIdx];\n    // Truncate the array.\n    node.liveConsumerNode.length--;\n    node.liveConsumerIndexOfThis.length--;\n    // If the index is still valid, then we need to fix the index pointer from the producer to this\n    // consumer, and update it from `lastIdx` to `idx` (accounting for the move above).\n    if (idx < node.liveConsumerNode.length) {\n        const idxProducer = node.liveConsumerIndexOfThis[idx];\n        const consumer = node.liveConsumerNode[idx];\n        assertConsumerNode(consumer);\n        consumer.producerIndexOfThis[idxProducer] = idx;\n    }\n}\nfunction consumerIsLive(node) {\n    return node.consumerIsAlwaysLive || (node?.liveConsumerNode?.length ?? 0) > 0;\n}\nfunction assertConsumerNode(node) {\n    node.producerNode ??= [];\n    node.producerIndexOfThis ??= [];\n    node.producerLastReadVersion ??= [];\n}\nfunction assertProducerNode(node) {\n    node.liveConsumerNode ??= [];\n    node.liveConsumerIndexOfThis ??= [];\n}\nfunction isConsumerNode(node) {\n    return node.producerNode !== undefined;\n}\nfunction runPostProducerCreatedFn(node) {\n    postProducerCreatedFn?.(node);\n}\nfunction setPostProducerCreatedFn(fn) {\n    const prev = postProducerCreatedFn;\n    postProducerCreatedFn = fn;\n    return prev;\n}\n\n/**\n * Create a computed signal which derives a reactive value from an expression.\n */\nfunction createComputed(computation, equal) {\n    const node = Object.create(COMPUTED_NODE);\n    node.computation = computation;\n    if (equal !== undefined) {\n        node.equal = equal;\n    }\n    const computed = () => {\n        // Check if the value needs updating before returning it.\n        producerUpdateValueVersion(node);\n        // Record that someone looked at this signal.\n        producerAccessed(node);\n        if (node.value === ERRORED) {\n            throw node.error;\n        }\n        return node.value;\n    };\n    computed[SIGNAL] = node;\n    if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n        const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n        computed.toString = () => `[Computed${debugName}: ${node.value}]`;\n    }\n    runPostProducerCreatedFn(node);\n    return computed;\n}\n/**\n * A dedicated symbol used before a computed value has been calculated for the first time.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst UNSET = /* @__PURE__ */ Symbol('UNSET');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * is in progress. Used to detect cycles in computation chains.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst COMPUTING = /* @__PURE__ */ Symbol('COMPUTING');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * failed. The thrown error is cached until the computation gets dirty again.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst ERRORED = /* @__PURE__ */ Symbol('ERRORED');\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst COMPUTED_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        value: UNSET,\n        dirty: true,\n        error: null,\n        equal: defaultEquals,\n        kind: 'computed',\n        producerMustRecompute(node) {\n            // Force a recomputation if there's no current value, or if the current value is in the\n            // process of being calculated (which should throw an error).\n            return node.value === UNSET || node.value === COMPUTING;\n        },\n        producerRecomputeValue(node) {\n            if (node.value === COMPUTING) {\n                // Our computation somehow led to a cyclic read of itself.\n                throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ? 'Detected cycle in computations.' : '');\n            }\n            const oldValue = node.value;\n            node.value = COMPUTING;\n            const prevConsumer = consumerBeforeComputation(node);\n            let newValue;\n            let wasEqual = false;\n            try {\n                newValue = node.computation();\n                // We want to mark this node as errored if calling `equal` throws; however, we don't want\n                // to track any reactive reads inside `equal`.\n                setActiveConsumer(null);\n                wasEqual =\n                    oldValue !== UNSET &&\n                        oldValue !== ERRORED &&\n                        newValue !== ERRORED &&\n                        node.equal(oldValue, newValue);\n            }\n            catch (err) {\n                newValue = ERRORED;\n                node.error = err;\n            }\n            finally {\n                consumerAfterComputation(node, prevConsumer);\n            }\n            if (wasEqual) {\n                // No change to `valueVersion` - old and new values are\n                // semantically equivalent.\n                node.value = oldValue;\n                return;\n            }\n            node.value = newValue;\n            node.version++;\n        },\n    };\n})();\n\nfunction defaultThrowError() {\n    throw new Error();\n}\nlet throwInvalidWriteToSignalErrorFn = defaultThrowError;\nfunction throwInvalidWriteToSignalError(node) {\n    throwInvalidWriteToSignalErrorFn(node);\n}\nfunction setThrowInvalidWriteToSignalError(fn) {\n    throwInvalidWriteToSignalErrorFn = fn;\n}\n\n/**\n * If set, called after `WritableSignal`s are updated.\n *\n * This hook can be used to achieve various effects, such as running effects synchronously as part\n * of setting a signal.\n */\nlet postSignalSetFn = null;\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nfunction createSignal(initialValue, equal) {\n    const node = Object.create(SIGNAL_NODE);\n    node.value = initialValue;\n    if (equal !== undefined) {\n        node.equal = equal;\n    }\n    const getter = (() => signalGetFn(node));\n    getter[SIGNAL] = node;\n    if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n        const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n        getter.toString = () => `[Signal${debugName}: ${node.value}]`;\n    }\n    runPostProducerCreatedFn(node);\n    return getter;\n}\n/**\n * Creates a `Signal` getter, setter, and updater function.\n */\nfunction createSignalTuple(initialValue, equal) {\n    const getter = createSignal(initialValue, equal);\n    const node = getter[SIGNAL];\n    const set = (newValue) => signalSetFn(node, newValue);\n    const update = (updateFn) => signalUpdateFn(node, updateFn);\n    return [getter, set, update];\n}\nfunction setPostSignalSetFn(fn) {\n    const prev = postSignalSetFn;\n    postSignalSetFn = fn;\n    return prev;\n}\nfunction signalGetFn(node) {\n    producerAccessed(node);\n    return node.value;\n}\nfunction signalSetFn(node, newValue) {\n    if (!producerUpdatesAllowed()) {\n        throwInvalidWriteToSignalError(node);\n    }\n    if (!node.equal(node.value, newValue)) {\n        node.value = newValue;\n        signalValueChanged(node);\n    }\n}\nfunction signalUpdateFn(node, updater) {\n    if (!producerUpdatesAllowed()) {\n        throwInvalidWriteToSignalError(node);\n    }\n    signalSetFn(node, updater(node.value));\n}\nfunction runPostSignalSetFn(node) {\n    postSignalSetFn?.(node);\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst SIGNAL_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        equal: defaultEquals,\n        value: undefined,\n        kind: 'signal',\n    };\n})();\nfunction signalValueChanged(node) {\n    node.version++;\n    producerIncrementEpoch();\n    producerNotifyConsumers(node);\n    postSignalSetFn?.(node);\n}\n\nexport { COMPUTING, ERRORED, REACTIVE_NODE, SIGNAL, SIGNAL_NODE, UNSET, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, createComputed, createSignal, createSignalTuple, defaultEquals, getActiveConsumer, isInNotificationPhase, isReactive, producerAccessed, producerIncrementEpoch, producerMarkClean, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, runPostProducerCreatedFn, runPostSignalSetFn, setActiveConsumer, setPostProducerCreatedFn, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalGetFn, signalSetFn, signalUpdateFn };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAASA,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOC,MAAM,CAACC,EAAE,CAACH,CAAC,EAAEC,CAAC,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIG,cAAc,GAAG,IAAI;AACzB,IAAIC,mBAAmB,GAAG,KAAK;AAC/B;AACA;AACA;AACA,IAAIC,KAAK,GAAG,CAAC;AACb;AACA;AACA;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,eAAgBC,MAAM,CAAC,QAAQ,CAAC;AAC/C,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EACjC,MAAMC,IAAI,GAAGR,cAAc;EAC3BA,cAAc,GAAGO,QAAQ;EACzB,OAAOC,IAAI;AACf;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzB,OAAOT,cAAc;AACzB;AACA,SAASU,qBAAqBA,CAAA,EAAG;EAC7B,OAAOT,mBAAmB;AAC9B;AACA,SAASU,UAAUA,CAACC,KAAK,EAAE;EACvB,OAAOA,KAAK,CAACR,MAAM,CAAC,KAAKS,SAAS;AACtC;AACA,MAAMC,aAAa,GAAG;EAClBC,OAAO,EAAE,CAAC;EACVC,cAAc,EAAE,CAAC;EACjBC,KAAK,EAAE,KAAK;EACZC,YAAY,EAAEL,SAAS;EACvBM,uBAAuB,EAAEN,SAAS;EAClCO,mBAAmB,EAAEP,SAAS;EAC9BQ,iBAAiB,EAAE,CAAC;EACpBC,gBAAgB,EAAET,SAAS;EAC3BU,uBAAuB,EAAEV,SAAS;EAClCW,yBAAyB,EAAE,KAAK;EAChCC,oBAAoB,EAAE,KAAK;EAC3BC,IAAI,EAAE,SAAS;EACfC,qBAAqB,EAAEA,CAAA,KAAM,KAAK;EAClCC,sBAAsB,EAAEA,CAAA,KAAM,CAAE,CAAC;EACjCC,mBAAmB,EAAEA,CAAA,KAAM,CAAE,CAAC;EAC9BC,oBAAoB,EAAEA,CAAA,KAAM,CAAE;AAClC,CAAC;AACD;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC5B,IAAI/B,mBAAmB,EAAE;IACrB,MAAM,IAAIgC,KAAK,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvD,wDAAwD,GACxD,EAAE,CAAC;EACb;EACA,IAAIlC,cAAc,KAAK,IAAI,EAAE;IACzB;IACA;EACJ;EACAA,cAAc,CAAC8B,oBAAoB,CAACE,IAAI,CAAC;EACzC;EACA,MAAMG,GAAG,GAAGnC,cAAc,CAACqB,iBAAiB,EAAE;EAC9Ce,kBAAkB,CAACpC,cAAc,CAAC;EAClC,IAAImC,GAAG,GAAGnC,cAAc,CAACkB,YAAY,CAACmB,MAAM,IAAIrC,cAAc,CAACkB,YAAY,CAACiB,GAAG,CAAC,KAAKH,IAAI,EAAE;IACvF;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIM,cAAc,CAACtC,cAAc,CAAC,EAAE;MAChC,MAAMuC,aAAa,GAAGvC,cAAc,CAACkB,YAAY,CAACiB,GAAG,CAAC;MACtDK,iCAAiC,CAACD,aAAa,EAAEvC,cAAc,CAACoB,mBAAmB,CAACe,GAAG,CAAC,CAAC;MACzF;MACA;IACJ;EACJ;EACA,IAAInC,cAAc,CAACkB,YAAY,CAACiB,GAAG,CAAC,KAAKH,IAAI,EAAE;IAC3C;IACAhC,cAAc,CAACkB,YAAY,CAACiB,GAAG,CAAC,GAAGH,IAAI;IACvC;IACA;IACAhC,cAAc,CAACoB,mBAAmB,CAACe,GAAG,CAAC,GAAGG,cAAc,CAACtC,cAAc,CAAC,GAClEyC,uBAAuB,CAACT,IAAI,EAAEhC,cAAc,EAAEmC,GAAG,CAAC,GAClD,CAAC;EACX;EACAnC,cAAc,CAACmB,uBAAuB,CAACgB,GAAG,CAAC,GAAGH,IAAI,CAACjB,OAAO;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,sBAAsBA,CAAA,EAAG;EAC9BxC,KAAK,EAAE;AACX;AACA;AACA;AACA;AACA,SAASyC,0BAA0BA,CAACX,IAAI,EAAE;EACtC,IAAIM,cAAc,CAACN,IAAI,CAAC,IAAI,CAACA,IAAI,CAACf,KAAK,EAAE;IACrC;IACA;IACA;EACJ;EACA,IAAI,CAACe,IAAI,CAACf,KAAK,IAAIe,IAAI,CAAChB,cAAc,KAAKd,KAAK,EAAE;IAC9C;IACA;IACA;IACA;EACJ;EACA,IAAI,CAAC8B,IAAI,CAACL,qBAAqB,CAACK,IAAI,CAAC,IAAI,CAACY,8BAA8B,CAACZ,IAAI,CAAC,EAAE;IAC5E;IACA;IACAa,iBAAiB,CAACb,IAAI,CAAC;IACvB;EACJ;EACAA,IAAI,CAACJ,sBAAsB,CAACI,IAAI,CAAC;EACjC;EACAa,iBAAiB,CAACb,IAAI,CAAC;AAC3B;AACA;AACA;AACA;AACA,SAASc,uBAAuBA,CAACd,IAAI,EAAE;EACnC,IAAIA,IAAI,CAACV,gBAAgB,KAAKT,SAAS,EAAE;IACrC;EACJ;EACA;EACA,MAAML,IAAI,GAAGP,mBAAmB;EAChCA,mBAAmB,GAAG,IAAI;EAC1B,IAAI;IACA,KAAK,MAAMM,QAAQ,IAAIyB,IAAI,CAACV,gBAAgB,EAAE;MAC1C,IAAI,CAACf,QAAQ,CAACU,KAAK,EAAE;QACjB8B,iBAAiB,CAACxC,QAAQ,CAAC;MAC/B;IACJ;EACJ,CAAC,SACO;IACJN,mBAAmB,GAAGO,IAAI;EAC9B;AACJ;AACA;AACA;AACA;AACA;AACA,SAASwC,sBAAsBA,CAAA,EAAG;EAC9B,OAAOhD,cAAc,EAAEwB,yBAAyB,KAAK,KAAK;AAC9D;AACA,SAASuB,iBAAiBA,CAACf,IAAI,EAAE;EAC7BA,IAAI,CAACf,KAAK,GAAG,IAAI;EACjB6B,uBAAuB,CAACd,IAAI,CAAC;EAC7BA,IAAI,CAACH,mBAAmB,GAAGG,IAAI,CAAC;AACpC;AACA,SAASa,iBAAiBA,CAACb,IAAI,EAAE;EAC7BA,IAAI,CAACf,KAAK,GAAG,KAAK;EAClBe,IAAI,CAAChB,cAAc,GAAGd,KAAK;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+C,yBAAyBA,CAACjB,IAAI,EAAE;EACrCA,IAAI,KAAKA,IAAI,CAACX,iBAAiB,GAAG,CAAC,CAAC;EACpC,OAAOf,iBAAiB,CAAC0B,IAAI,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,wBAAwBA,CAAClB,IAAI,EAAEmB,YAAY,EAAE;EAClD7C,iBAAiB,CAAC6C,YAAY,CAAC;EAC/B,IAAI,CAACnB,IAAI,IACLA,IAAI,CAACd,YAAY,KAAKL,SAAS,IAC/BmB,IAAI,CAACZ,mBAAmB,KAAKP,SAAS,IACtCmB,IAAI,CAACb,uBAAuB,KAAKN,SAAS,EAAE;IAC5C;EACJ;EACA,IAAIyB,cAAc,CAACN,IAAI,CAAC,EAAE;IACtB;IACA;IACA,KAAK,IAAIoB,CAAC,GAAGpB,IAAI,CAACX,iBAAiB,EAAE+B,CAAC,GAAGpB,IAAI,CAACd,YAAY,CAACmB,MAAM,EAAEe,CAAC,EAAE,EAAE;MACpEZ,iCAAiC,CAACR,IAAI,CAACd,YAAY,CAACkC,CAAC,CAAC,EAAEpB,IAAI,CAACZ,mBAAmB,CAACgC,CAAC,CAAC,CAAC;IACxF;EACJ;EACA;EACA;EACA;EACA,OAAOpB,IAAI,CAACd,YAAY,CAACmB,MAAM,GAAGL,IAAI,CAACX,iBAAiB,EAAE;IACtDW,IAAI,CAACd,YAAY,CAACmC,GAAG,CAAC,CAAC;IACvBrB,IAAI,CAACb,uBAAuB,CAACkC,GAAG,CAAC,CAAC;IAClCrB,IAAI,CAACZ,mBAAmB,CAACiC,GAAG,CAAC,CAAC;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA,SAAST,8BAA8BA,CAACZ,IAAI,EAAE;EAC1CI,kBAAkB,CAACJ,IAAI,CAAC;EACxB;EACA,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,CAACd,YAAY,CAACmB,MAAM,EAAEe,CAAC,EAAE,EAAE;IAC/C,MAAME,QAAQ,GAAGtB,IAAI,CAACd,YAAY,CAACkC,CAAC,CAAC;IACrC,MAAMG,WAAW,GAAGvB,IAAI,CAACb,uBAAuB,CAACiC,CAAC,CAAC;IACnD;IACA;IACA,IAAIG,WAAW,KAAKD,QAAQ,CAACvC,OAAO,EAAE;MAClC,OAAO,IAAI;IACf;IACA;IACA;IACA4B,0BAA0B,CAACW,QAAQ,CAAC;IACpC;IACA;IACA,IAAIC,WAAW,KAAKD,QAAQ,CAACvC,OAAO,EAAE;MAClC,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA,SAASyC,eAAeA,CAACxB,IAAI,EAAE;EAC3BI,kBAAkB,CAACJ,IAAI,CAAC;EACxB,IAAIM,cAAc,CAACN,IAAI,CAAC,EAAE;IACtB;IACA,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,CAACd,YAAY,CAACmB,MAAM,EAAEe,CAAC,EAAE,EAAE;MAC/CZ,iCAAiC,CAACR,IAAI,CAACd,YAAY,CAACkC,CAAC,CAAC,EAAEpB,IAAI,CAACZ,mBAAmB,CAACgC,CAAC,CAAC,CAAC;IACxF;EACJ;EACA;EACApB,IAAI,CAACd,YAAY,CAACmB,MAAM,GACpBL,IAAI,CAACb,uBAAuB,CAACkB,MAAM,GAC/BL,IAAI,CAACZ,mBAAmB,CAACiB,MAAM,GAC3B,CAAC;EACb,IAAIL,IAAI,CAACV,gBAAgB,EAAE;IACvBU,IAAI,CAACV,gBAAgB,CAACe,MAAM,GAAGL,IAAI,CAACT,uBAAuB,CAACc,MAAM,GAAG,CAAC;EAC1E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,uBAAuBA,CAACT,IAAI,EAAEzB,QAAQ,EAAEkD,WAAW,EAAE;EAC1DC,kBAAkB,CAAC1B,IAAI,CAAC;EACxB,IAAIA,IAAI,CAACV,gBAAgB,CAACe,MAAM,KAAK,CAAC,IAAIsB,cAAc,CAAC3B,IAAI,CAAC,EAAE;IAC5D;IACA,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,CAACd,YAAY,CAACmB,MAAM,EAAEe,CAAC,EAAE,EAAE;MAC/CpB,IAAI,CAACZ,mBAAmB,CAACgC,CAAC,CAAC,GAAGX,uBAAuB,CAACT,IAAI,CAACd,YAAY,CAACkC,CAAC,CAAC,EAAEpB,IAAI,EAAEoB,CAAC,CAAC;IACxF;EACJ;EACApB,IAAI,CAACT,uBAAuB,CAACqC,IAAI,CAACH,WAAW,CAAC;EAC9C,OAAOzB,IAAI,CAACV,gBAAgB,CAACsC,IAAI,CAACrD,QAAQ,CAAC,GAAG,CAAC;AACnD;AACA;AACA;AACA;AACA,SAASiC,iCAAiCA,CAACR,IAAI,EAAEG,GAAG,EAAE;EAClDuB,kBAAkB,CAAC1B,IAAI,CAAC;EACxB,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,IAAIC,GAAG,IAAIH,IAAI,CAACV,gBAAgB,CAACe,MAAM,EAAE;IACtF,MAAM,IAAIJ,KAAK,CAAC,0CAA0CE,GAAG,wBAAwBH,IAAI,CAACV,gBAAgB,CAACe,MAAM,aAAa,CAAC;EACnI;EACA,IAAIL,IAAI,CAACV,gBAAgB,CAACe,MAAM,KAAK,CAAC,IAAIsB,cAAc,CAAC3B,IAAI,CAAC,EAAE;IAC5D;IACA;IACA;IACA,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,CAACd,YAAY,CAACmB,MAAM,EAAEe,CAAC,EAAE,EAAE;MAC/CZ,iCAAiC,CAACR,IAAI,CAACd,YAAY,CAACkC,CAAC,CAAC,EAAEpB,IAAI,CAACZ,mBAAmB,CAACgC,CAAC,CAAC,CAAC;IACxF;EACJ;EACA;EACA;EACA,MAAMS,OAAO,GAAG7B,IAAI,CAACV,gBAAgB,CAACe,MAAM,GAAG,CAAC;EAChDL,IAAI,CAACV,gBAAgB,CAACa,GAAG,CAAC,GAAGH,IAAI,CAACV,gBAAgB,CAACuC,OAAO,CAAC;EAC3D7B,IAAI,CAACT,uBAAuB,CAACY,GAAG,CAAC,GAAGH,IAAI,CAACT,uBAAuB,CAACsC,OAAO,CAAC;EACzE;EACA7B,IAAI,CAACV,gBAAgB,CAACe,MAAM,EAAE;EAC9BL,IAAI,CAACT,uBAAuB,CAACc,MAAM,EAAE;EACrC;EACA;EACA,IAAIF,GAAG,GAAGH,IAAI,CAACV,gBAAgB,CAACe,MAAM,EAAE;IACpC,MAAMyB,WAAW,GAAG9B,IAAI,CAACT,uBAAuB,CAACY,GAAG,CAAC;IACrD,MAAM5B,QAAQ,GAAGyB,IAAI,CAACV,gBAAgB,CAACa,GAAG,CAAC;IAC3CC,kBAAkB,CAAC7B,QAAQ,CAAC;IAC5BA,QAAQ,CAACa,mBAAmB,CAAC0C,WAAW,CAAC,GAAG3B,GAAG;EACnD;AACJ;AACA,SAASG,cAAcA,CAACN,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACP,oBAAoB,IAAI,CAACO,IAAI,EAAEV,gBAAgB,EAAEe,MAAM,IAAI,CAAC,IAAI,CAAC;AACjF;AACA,SAASD,kBAAkBA,CAACJ,IAAI,EAAE;EAC9BA,IAAI,CAACd,YAAY,KAAK,EAAE;EACxBc,IAAI,CAACZ,mBAAmB,KAAK,EAAE;EAC/BY,IAAI,CAACb,uBAAuB,KAAK,EAAE;AACvC;AACA,SAASuC,kBAAkBA,CAAC1B,IAAI,EAAE;EAC9BA,IAAI,CAACV,gBAAgB,KAAK,EAAE;EAC5BU,IAAI,CAACT,uBAAuB,KAAK,EAAE;AACvC;AACA,SAASoC,cAAcA,CAAC3B,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACd,YAAY,KAAKL,SAAS;AAC1C;AACA,SAASkD,wBAAwBA,CAAC/B,IAAI,EAAE;EACpC7B,qBAAqB,GAAG6B,IAAI,CAAC;AACjC;AACA,SAASgC,wBAAwBA,CAACC,EAAE,EAAE;EAClC,MAAMzD,IAAI,GAAGL,qBAAqB;EAClCA,qBAAqB,GAAG8D,EAAE;EAC1B,OAAOzD,IAAI;AACf;;AAEA;AACA;AACA;AACA,SAAS0D,cAAcA,CAACC,WAAW,EAAEC,KAAK,EAAE;EACxC,MAAMpC,IAAI,GAAGlC,MAAM,CAACuE,MAAM,CAACC,aAAa,CAAC;EACzCtC,IAAI,CAACmC,WAAW,GAAGA,WAAW;EAC9B,IAAIC,KAAK,KAAKvD,SAAS,EAAE;IACrBmB,IAAI,CAACoC,KAAK,GAAGA,KAAK;EACtB;EACA,MAAMG,QAAQ,GAAGA,CAAA,KAAM;IACnB;IACA5B,0BAA0B,CAACX,IAAI,CAAC;IAChC;IACAD,gBAAgB,CAACC,IAAI,CAAC;IACtB,IAAIA,IAAI,CAACpB,KAAK,KAAK4D,OAAO,EAAE;MACxB,MAAMxC,IAAI,CAACyC,KAAK;IACpB;IACA,OAAOzC,IAAI,CAACpB,KAAK;EACrB,CAAC;EACD2D,QAAQ,CAACnE,MAAM,CAAC,GAAG4B,IAAI;EACvB,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;IAC/C,MAAMwC,SAAS,GAAG1C,IAAI,CAAC0C,SAAS,GAAG,IAAI,GAAG1C,IAAI,CAAC0C,SAAS,GAAG,GAAG,GAAG,EAAE;IACnEH,QAAQ,CAACI,QAAQ,GAAG,MAAM,YAAYD,SAAS,KAAK1C,IAAI,CAACpB,KAAK,GAAG;EACrE;EACAmD,wBAAwB,CAAC/B,IAAI,CAAC;EAC9B,OAAOuC,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA,MAAMK,KAAK,GAAG,eAAgBvE,MAAM,CAAC,OAAO,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,MAAMwE,SAAS,GAAG,eAAgBxE,MAAM,CAAC,WAAW,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,MAAMmE,OAAO,GAAG,eAAgBnE,MAAM,CAAC,SAAS,CAAC;AACjD;AACA;AACA;AACA,MAAMiE,aAAa,GAAG,eAAgB,CAAC,MAAM;EACzC,OAAO;IACH,GAAGxD,aAAa;IAChBF,KAAK,EAAEgE,KAAK;IACZ3D,KAAK,EAAE,IAAI;IACXwD,KAAK,EAAE,IAAI;IACXL,KAAK,EAAEzE,aAAa;IACpB+B,IAAI,EAAE,UAAU;IAChBC,qBAAqBA,CAACK,IAAI,EAAE;MACxB;MACA;MACA,OAAOA,IAAI,CAACpB,KAAK,KAAKgE,KAAK,IAAI5C,IAAI,CAACpB,KAAK,KAAKiE,SAAS;IAC3D,CAAC;IACDjD,sBAAsBA,CAACI,IAAI,EAAE;MACzB,IAAIA,IAAI,CAACpB,KAAK,KAAKiE,SAAS,EAAE;QAC1B;QACA,MAAM,IAAI5C,KAAK,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,iCAAiC,GAAG,EAAE,CAAC;MAC3G;MACA,MAAM4C,QAAQ,GAAG9C,IAAI,CAACpB,KAAK;MAC3BoB,IAAI,CAACpB,KAAK,GAAGiE,SAAS;MACtB,MAAM1B,YAAY,GAAGF,yBAAyB,CAACjB,IAAI,CAAC;MACpD,IAAI+C,QAAQ;MACZ,IAAIC,QAAQ,GAAG,KAAK;MACpB,IAAI;QACAD,QAAQ,GAAG/C,IAAI,CAACmC,WAAW,CAAC,CAAC;QAC7B;QACA;QACA7D,iBAAiB,CAAC,IAAI,CAAC;QACvB0E,QAAQ,GACJF,QAAQ,KAAKF,KAAK,IACdE,QAAQ,KAAKN,OAAO,IACpBO,QAAQ,KAAKP,OAAO,IACpBxC,IAAI,CAACoC,KAAK,CAACU,QAAQ,EAAEC,QAAQ,CAAC;MAC1C,CAAC,CACD,OAAOE,GAAG,EAAE;QACRF,QAAQ,GAAGP,OAAO;QAClBxC,IAAI,CAACyC,KAAK,GAAGQ,GAAG;MACpB,CAAC,SACO;QACJ/B,wBAAwB,CAAClB,IAAI,EAAEmB,YAAY,CAAC;MAChD;MACA,IAAI6B,QAAQ,EAAE;QACV;QACA;QACAhD,IAAI,CAACpB,KAAK,GAAGkE,QAAQ;QACrB;MACJ;MACA9C,IAAI,CAACpB,KAAK,GAAGmE,QAAQ;MACrB/C,IAAI,CAACjB,OAAO,EAAE;IAClB;EACJ,CAAC;AACL,CAAC,EAAE,CAAC;AAEJ,SAASmE,iBAAiBA,CAAA,EAAG;EACzB,MAAM,IAAIjD,KAAK,CAAC,CAAC;AACrB;AACA,IAAIkD,gCAAgC,GAAGD,iBAAiB;AACxD,SAASE,8BAA8BA,CAACpD,IAAI,EAAE;EAC1CmD,gCAAgC,CAACnD,IAAI,CAAC;AAC1C;AACA,SAASqD,iCAAiCA,CAACpB,EAAE,EAAE;EAC3CkB,gCAAgC,GAAGlB,EAAE;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIqB,eAAe,GAAG,IAAI;AAC1B;AACA;AACA;AACA,SAASC,YAAYA,CAACC,YAAY,EAAEpB,KAAK,EAAE;EACvC,MAAMpC,IAAI,GAAGlC,MAAM,CAACuE,MAAM,CAACoB,WAAW,CAAC;EACvCzD,IAAI,CAACpB,KAAK,GAAG4E,YAAY;EACzB,IAAIpB,KAAK,KAAKvD,SAAS,EAAE;IACrBmB,IAAI,CAACoC,KAAK,GAAGA,KAAK;EACtB;EACA,MAAMsB,MAAM,GAAIA,CAAA,KAAMC,WAAW,CAAC3D,IAAI,CAAE;EACxC0D,MAAM,CAACtF,MAAM,CAAC,GAAG4B,IAAI;EACrB,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;IAC/C,MAAMwC,SAAS,GAAG1C,IAAI,CAAC0C,SAAS,GAAG,IAAI,GAAG1C,IAAI,CAAC0C,SAAS,GAAG,GAAG,GAAG,EAAE;IACnEgB,MAAM,CAACf,QAAQ,GAAG,MAAM,UAAUD,SAAS,KAAK1C,IAAI,CAACpB,KAAK,GAAG;EACjE;EACAmD,wBAAwB,CAAC/B,IAAI,CAAC;EAC9B,OAAO0D,MAAM;AACjB;AACA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACJ,YAAY,EAAEpB,KAAK,EAAE;EAC5C,MAAMsB,MAAM,GAAGH,YAAY,CAACC,YAAY,EAAEpB,KAAK,CAAC;EAChD,MAAMpC,IAAI,GAAG0D,MAAM,CAACtF,MAAM,CAAC;EAC3B,MAAMyF,GAAG,GAAId,QAAQ,IAAKe,WAAW,CAAC9D,IAAI,EAAE+C,QAAQ,CAAC;EACrD,MAAMgB,MAAM,GAAIC,QAAQ,IAAKC,cAAc,CAACjE,IAAI,EAAEgE,QAAQ,CAAC;EAC3D,OAAO,CAACN,MAAM,EAAEG,GAAG,EAAEE,MAAM,CAAC;AAChC;AACA,SAASG,kBAAkBA,CAACjC,EAAE,EAAE;EAC5B,MAAMzD,IAAI,GAAG8E,eAAe;EAC5BA,eAAe,GAAGrB,EAAE;EACpB,OAAOzD,IAAI;AACf;AACA,SAASmF,WAAWA,CAAC3D,IAAI,EAAE;EACvBD,gBAAgB,CAACC,IAAI,CAAC;EACtB,OAAOA,IAAI,CAACpB,KAAK;AACrB;AACA,SAASkF,WAAWA,CAAC9D,IAAI,EAAE+C,QAAQ,EAAE;EACjC,IAAI,CAAC/B,sBAAsB,CAAC,CAAC,EAAE;IAC3BoC,8BAA8B,CAACpD,IAAI,CAAC;EACxC;EACA,IAAI,CAACA,IAAI,CAACoC,KAAK,CAACpC,IAAI,CAACpB,KAAK,EAAEmE,QAAQ,CAAC,EAAE;IACnC/C,IAAI,CAACpB,KAAK,GAAGmE,QAAQ;IACrBoB,kBAAkB,CAACnE,IAAI,CAAC;EAC5B;AACJ;AACA,SAASiE,cAAcA,CAACjE,IAAI,EAAEoE,OAAO,EAAE;EACnC,IAAI,CAACpD,sBAAsB,CAAC,CAAC,EAAE;IAC3BoC,8BAA8B,CAACpD,IAAI,CAAC;EACxC;EACA8D,WAAW,CAAC9D,IAAI,EAAEoE,OAAO,CAACpE,IAAI,CAACpB,KAAK,CAAC,CAAC;AAC1C;AACA,SAASyF,kBAAkBA,CAACrE,IAAI,EAAE;EAC9BsD,eAAe,GAAGtD,IAAI,CAAC;AAC3B;AACA;AACA;AACA;AACA,MAAMyD,WAAW,GAAG,eAAgB,CAAC,MAAM;EACvC,OAAO;IACH,GAAG3E,aAAa;IAChBsD,KAAK,EAAEzE,aAAa;IACpBiB,KAAK,EAAEC,SAAS;IAChBa,IAAI,EAAE;EACV,CAAC;AACL,CAAC,EAAE,CAAC;AACJ,SAASyE,kBAAkBA,CAACnE,IAAI,EAAE;EAC9BA,IAAI,CAACjB,OAAO,EAAE;EACd2B,sBAAsB,CAAC,CAAC;EACxBI,uBAAuB,CAACd,IAAI,CAAC;EAC7BsD,eAAe,GAAGtD,IAAI,CAAC;AAC3B;AAEA,SAAS6C,SAAS,EAAEL,OAAO,EAAE1D,aAAa,EAAEV,MAAM,EAAEqF,WAAW,EAAEb,KAAK,EAAE1B,wBAAwB,EAAED,yBAAyB,EAAEO,eAAe,EAAET,iBAAiB,EAAEH,8BAA8B,EAAEsB,cAAc,EAAEqB,YAAY,EAAEK,iBAAiB,EAAEjG,aAAa,EAAEc,iBAAiB,EAAEC,qBAAqB,EAAEC,UAAU,EAAEoB,gBAAgB,EAAEW,sBAAsB,EAAEG,iBAAiB,EAAEC,uBAAuB,EAAEH,0BAA0B,EAAEK,sBAAsB,EAAEe,wBAAwB,EAAEsC,kBAAkB,EAAE/F,iBAAiB,EAAE0D,wBAAwB,EAAEkC,kBAAkB,EAAEb,iCAAiC,EAAEM,WAAW,EAAEG,WAAW,EAAEG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}