"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmsService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
let SmsService = class SmsService {
    constructor() {
        // In a real application, you would initialize SMS service here
        // For demo purposes, we'll simulate SMS sending
    }
    async sendOTP(phone, otp, type) {
        // Simulate SMS sending
        // In production, integrate with services like:
        // - Twilio (has free tier)
        // - AWS SNS
        // - Firebase Cloud Messaging
        console.log(`SMS to ${phone}: Your ${type} code is ${otp}. Valid for 10 minutes.`);
        // For demo purposes, we'll just log the SMS
        // In production, implement actual SMS sending:
        /*
        const twilio = require('twilio');
        const client = twilio(process.env.TWILIO_SID, process.env.TWILIO_TOKEN);
        
        await client.messages.create({
          body: `Your ${type} code is ${otp}. Valid for 10 minutes.`,
          from: process.env.TWILIO_PHONE,
          to: phone
        });
        */
    }
    async sendVerificationSMS(phone, otp) {
        await this.sendOTP(phone, otp, 'verification');
    }
    async sendLoginSMS(phone, otp) {
        await this.sendOTP(phone, otp, 'login');
    }
    async send2FASMS(phone, otp) {
        await this.sendOTP(phone, otp, '2FA');
    }
};
exports.SmsService = SmsService;
exports.SmsService = SmsService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.TRANSIENT }),
    tslib_1.__metadata("design:paramtypes", [])
], SmsService);
//# sourceMappingURL=sms.service.js.map