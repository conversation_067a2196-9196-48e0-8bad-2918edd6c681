import {Entity, model, property, belongsTo} from '@loopback/repository';
import {User} from './user.model';

@model({
  settings: {
    postgresql: {
      table: 'payment'
    }
  }
})
export class Payment extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
    postgresql: {
      columnName: 'id'
    }
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    postgresql: {
      columnName: 'razorpay_order_id'
    }
  })
  razorpayOrderId: string;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'razorpay_payment_id'
    }
  })
  razorpayPaymentId?: string;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'razorpay_signature'
    }
  })
  razorpaySignature?: string;

  @property({
    type: 'number',
    required: true,
    jsonSchema: {
      minimum: 1,
      maximum: 1000000,
    },
  })
  amount: number;

  @property({
    type: 'string',
    required: true,
    default: 'INR',
    jsonSchema: {
      enum: ['INR', 'USD'],
    },
  })
  currency: string;

  @property({
    type: 'string',
    required: true,
    default: 'pending',
    jsonSchema: {
      enum: ['pending', 'paid', 'failed', 'cancelled', 'refunded'],
    },
  })
  status: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'object',
  })
  metadata?: object;

  @property({
    type: 'date',
    default: () => new Date(),
    postgresql: {
      columnName: 'created_at'
    }
  })
  createdAt: Date;

  @property({
    type: 'date',
    default: () => new Date(),
    postgresql: {
      columnName: 'updated_at'
    }
  })
  updatedAt: Date;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'paid_at'
    }
  })
  paidAt?: Date;

  @belongsTo(() => User, {}, {
    postgresql: {
      columnName: 'user_id'
    }
  })
  userId: string;

  constructor(data?: Partial<Payment>) {
    super(data);
  }
}

export interface PaymentRelations {
  user?: User;
}

export type PaymentWithRelations = Payment & PaymentRelations;
