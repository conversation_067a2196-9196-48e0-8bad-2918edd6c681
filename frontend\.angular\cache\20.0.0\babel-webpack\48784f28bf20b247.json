{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nfunction parseCookieValue(cookieStr, name) {\n  name = encodeURIComponent(name);\n  for (const cookie of cookieStr.split(';')) {\n    const eqIndex = cookie.indexOf('=');\n    const [cookieName, cookieValue] = eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n    if (cookieName.trim() === name) {\n      return decodeURIComponent(cookieValue);\n    }\n  }\n  return null;\n}\nconst PLATFORM_BROWSER_ID = 'browser';\nconst PLATFORM_SERVER_ID = 'server';\n/**\n * Returns whether a platform id represents a browser platform.\n * @publicApi\n */\nfunction isPlatformBrowser(platformId) {\n  return platformId === PLATFORM_BROWSER_ID;\n}\n/**\n * Returns whether a platform id represents a server platform.\n * @publicApi\n */\nfunction isPlatformServer(platformId) {\n  return platformId === PLATFORM_SERVER_ID;\n}\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n */\nclass XhrFactory {}\nexport { PLATFORM_BROWSER_ID, PLATFORM_SERVER_ID, XhrFactory, isPlatformBrowser, isPlatformServer, parseCookieValue };", "map": {"version": 3, "names": ["parseCookieValue", "cookieStr", "name", "encodeURIComponent", "cookie", "split", "eqIndex", "indexOf", "cookieName", "cookieValue", "slice", "trim", "decodeURIComponent", "PLATFORM_BROWSER_ID", "PLATFORM_SERVER_ID", "isPlatformBrowser", "platformId", "isPlatformServer", "XhrFactory"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/common/fesm2022/xhr-BfNfxNDv.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nfunction parseCookieValue(cookieStr, name) {\n    name = encodeURIComponent(name);\n    for (const cookie of cookieStr.split(';')) {\n        const eqIndex = cookie.indexOf('=');\n        const [cookieName, cookieValue] = eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n        if (cookieName.trim() === name) {\n            return decodeURIComponent(cookieValue);\n        }\n    }\n    return null;\n}\n\nconst PLATFORM_BROWSER_ID = 'browser';\nconst PLATFORM_SERVER_ID = 'server';\n/**\n * Returns whether a platform id represents a browser platform.\n * @publicApi\n */\nfunction isPlatformBrowser(platformId) {\n    return platformId === PLATFORM_BROWSER_ID;\n}\n/**\n * Returns whether a platform id represents a server platform.\n * @publicApi\n */\nfunction isPlatformServer(platformId) {\n    return platformId === PLATFORM_SERVER_ID;\n}\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n */\nclass XhrFactory {\n}\n\nexport { PLATFORM_BROWSER_ID, PLATFORM_SERVER_ID, XhrFactory, isPlatformBrowser, isPlatformServer, parseCookieValue };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,gBAAgBA,CAACC,SAAS,EAAEC,IAAI,EAAE;EACvCA,IAAI,GAAGC,kBAAkB,CAACD,IAAI,CAAC;EAC/B,KAAK,MAAME,MAAM,IAAIH,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,EAAE;IACvC,MAAMC,OAAO,GAAGF,MAAM,CAACG,OAAO,CAAC,GAAG,CAAC;IACnC,MAAM,CAACC,UAAU,EAAEC,WAAW,CAAC,GAAGH,OAAO,IAAI,CAAC,CAAC,GAAG,CAACF,MAAM,EAAE,EAAE,CAAC,GAAG,CAACA,MAAM,CAACM,KAAK,CAAC,CAAC,EAAEJ,OAAO,CAAC,EAAEF,MAAM,CAACM,KAAK,CAACJ,OAAO,GAAG,CAAC,CAAC,CAAC;IACtH,IAAIE,UAAU,CAACG,IAAI,CAAC,CAAC,KAAKT,IAAI,EAAE;MAC5B,OAAOU,kBAAkB,CAACH,WAAW,CAAC;IAC1C;EACJ;EACA,OAAO,IAAI;AACf;AAEA,MAAMI,mBAAmB,GAAG,SAAS;AACrC,MAAMC,kBAAkB,GAAG,QAAQ;AACnC;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EACnC,OAAOA,UAAU,KAAKH,mBAAmB;AAC7C;AACA;AACA;AACA;AACA;AACA,SAASI,gBAAgBA,CAACD,UAAU,EAAE;EAClC,OAAOA,UAAU,KAAKF,kBAAkB;AAC5C;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,UAAU,CAAC;AAGjB,SAASL,mBAAmB,EAAEC,kBAAkB,EAAEI,UAAU,EAAEH,iBAAiB,EAAEE,gBAAgB,EAAEjB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}