{"ast": null, "code": "import { isObservable, of } from 'rxjs';\nimport { D as DataSource } from './data-source-D34wiQZj.mjs';\nimport { InjectionToken } from '@angular/core';\n\n/** DataSource wrapper for a native array. */\nclass ArrayDataSource extends DataSource {\n  _data;\n  constructor(_data) {\n    super();\n    this._data = _data;\n  }\n  connect() {\n    return isObservable(this._data) ? this._data : of(this._data);\n  }\n  disconnect() {}\n}\n\n/** Indicates how a view was changed by a {@link _ViewRepeater}. */\nvar _ViewRepeaterOperation;\n(function (_ViewRepeaterOperation) {\n  /** The content of an existing view was replaced with another item. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"REPLACED\"] = 0] = \"REPLACED\";\n  /** A new view was created with `createEmbeddedView`. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"INSERTED\"] = 1] = \"INSERTED\";\n  /** The position of a view changed, but the content remains the same. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"MOVED\"] = 2] = \"MOVED\";\n  /** A view was detached from the view container. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"REMOVED\"] = 3] = \"REMOVED\";\n})(_ViewRepeaterOperation || (_ViewRepeaterOperation = {}));\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _RecycleViewRepeaterStrategy {\n  /**\n   * The size of the cache used to store unused views.\n   * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n   */\n  viewCacheSize = 20;\n  /**\n   * View cache that stores embedded view instances that have been previously stamped out,\n   * but don't are not currently rendered. The view repeater will reuse these views rather than\n   * creating brand new ones.\n   *\n   * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n   */\n  _viewCache = [];\n  /** Apply changes to the DOM. */\n  applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n    // Rearrange the views to put them in the right location.\n    changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n      let view;\n      let operation;\n      if (record.previousIndex == null) {\n        // Item added.\n        const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n        view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = view ? _ViewRepeaterOperation.INSERTED : _ViewRepeaterOperation.REPLACED;\n      } else if (currentIndex == null) {\n        // Item removed.\n        this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n        operation = _ViewRepeaterOperation.REMOVED;\n      } else {\n        // Item moved.\n        view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = _ViewRepeaterOperation.MOVED;\n      }\n      if (itemViewChanged) {\n        itemViewChanged({\n          context: view?.context,\n          operation,\n          record\n        });\n      }\n    });\n  }\n  detach() {\n    for (const view of this._viewCache) {\n      view.destroy();\n    }\n    this._viewCache = [];\n  }\n  /**\n   * Inserts a view for a new item, either from the cache or by creating a new\n   * one. Returns `undefined` if the item was inserted into a cached view.\n   */\n  _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n    const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n    if (cachedView) {\n      cachedView.context.$implicit = value;\n      return undefined;\n    }\n    const viewArgs = viewArgsFactory();\n    return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n  }\n  /** Detaches the view at the given index and inserts into the view cache. */\n  _detachAndCacheView(index, viewContainerRef) {\n    const detachedView = viewContainerRef.detach(index);\n    this._maybeCacheView(detachedView, viewContainerRef);\n  }\n  /** Moves view at the previous index to the current index. */\n  _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n    const view = viewContainerRef.get(adjustedPreviousIndex);\n    viewContainerRef.move(view, currentIndex);\n    view.context.$implicit = value;\n    return view;\n  }\n  /**\n   * Cache the given detached view. If the cache is full, the view will be\n   * destroyed.\n   */\n  _maybeCacheView(view, viewContainerRef) {\n    if (this._viewCache.length < this.viewCacheSize) {\n      this._viewCache.push(view);\n    } else {\n      const index = viewContainerRef.indexOf(view);\n      // The host component could remove views from the container outside of\n      // the view repeater. It's unlikely this will occur, but just in case,\n      // destroy the view on its own, otherwise destroy it through the\n      // container to ensure that all the references are removed.\n      if (index === -1) {\n        view.destroy();\n      } else {\n        viewContainerRef.remove(index);\n      }\n    }\n  }\n  /** Inserts a recycled view from the cache at the given index. */\n  _insertViewFromCache(index, viewContainerRef) {\n    const cachedView = this._viewCache.pop();\n    if (cachedView) {\n      viewContainerRef.insert(cachedView, index);\n    }\n    return cachedView || null;\n  }\n}\nexport { ArrayDataSource as A, _RecycleViewRepeaterStrategy as _, _ViewRepeaterOperation as a, _VIEW_REPEATER_STRATEGY as b };", "map": {"version": 3, "names": ["isObservable", "of", "D", "DataSource", "InjectionToken", "ArrayDataSource", "_data", "constructor", "connect", "disconnect", "_ViewRepeaterOperation", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "viewCacheSize", "_viewCache", "applyChanges", "changes", "viewContainerRef", "itemContextFactory", "itemValueResolver", "itemViewChanged", "forEachOperation", "record", "adjustedPreviousIndex", "currentIndex", "view", "operation", "previousIndex", "viewArgsFactory", "_insertView", "INSERTED", "REPLACED", "_detachAndCacheView", "REMOVED", "_moveView", "MOVED", "context", "detach", "destroy", "value", "cachedView", "_insertViewFromCache", "$implicit", "undefined", "viewArgs", "createEmbeddedView", "templateRef", "index", "detached<PERSON>iew", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get", "move", "length", "push", "indexOf", "remove", "pop", "insert", "A", "_", "a", "b"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/cdk/fesm2022/recycle-view-repeater-strategy-DoWdPqVw.mjs"], "sourcesContent": ["import { isObservable, of } from 'rxjs';\nimport { D as DataSource } from './data-source-D34wiQZj.mjs';\nimport { InjectionToken } from '@angular/core';\n\n/** DataSource wrapper for a native array. */\nclass ArrayDataSource extends DataSource {\n    _data;\n    constructor(_data) {\n        super();\n        this._data = _data;\n    }\n    connect() {\n        return isObservable(this._data) ? this._data : of(this._data);\n    }\n    disconnect() { }\n}\n\n/** Indicates how a view was changed by a {@link _ViewRepeater}. */\nvar _ViewRepeaterOperation;\n(function (_ViewRepeaterOperation) {\n    /** The content of an existing view was replaced with another item. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"REPLACED\"] = 0] = \"REPLACED\";\n    /** A new view was created with `createEmbeddedView`. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"INSERTED\"] = 1] = \"INSERTED\";\n    /** The position of a view changed, but the content remains the same. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"MOVED\"] = 2] = \"MOVED\";\n    /** A view was detached from the view container. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"REMOVED\"] = 3] = \"REMOVED\";\n})(_ViewRepeaterOperation || (_ViewRepeaterOperation = {}));\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _RecycleViewRepeaterStrategy {\n    /**\n     * The size of the cache used to store unused views.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n     */\n    viewCacheSize = 20;\n    /**\n     * View cache that stores embedded view instances that have been previously stamped out,\n     * but don't are not currently rendered. The view repeater will reuse these views rather than\n     * creating brand new ones.\n     *\n     * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n     */\n    _viewCache = [];\n    /** Apply changes to the DOM. */\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        // Rearrange the views to put them in the right location.\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                // Item added.\n                const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = view ? _ViewRepeaterOperation.INSERTED : _ViewRepeaterOperation.REPLACED;\n            }\n            else if (currentIndex == null) {\n                // Item removed.\n                this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n                operation = _ViewRepeaterOperation.REMOVED;\n            }\n            else {\n                // Item moved.\n                view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = _ViewRepeaterOperation.MOVED;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view?.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() {\n        for (const view of this._viewCache) {\n            view.destroy();\n        }\n        this._viewCache = [];\n    }\n    /**\n     * Inserts a view for a new item, either from the cache or by creating a new\n     * one. Returns `undefined` if the item was inserted into a cached view.\n     */\n    _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n        const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n        if (cachedView) {\n            cachedView.context.$implicit = value;\n            return undefined;\n        }\n        const viewArgs = viewArgsFactory();\n        return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n    }\n    /** Detaches the view at the given index and inserts into the view cache. */\n    _detachAndCacheView(index, viewContainerRef) {\n        const detachedView = viewContainerRef.detach(index);\n        this._maybeCacheView(detachedView, viewContainerRef);\n    }\n    /** Moves view at the previous index to the current index. */\n    _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n        const view = viewContainerRef.get(adjustedPreviousIndex);\n        viewContainerRef.move(view, currentIndex);\n        view.context.$implicit = value;\n        return view;\n    }\n    /**\n     * Cache the given detached view. If the cache is full, the view will be\n     * destroyed.\n     */\n    _maybeCacheView(view, viewContainerRef) {\n        if (this._viewCache.length < this.viewCacheSize) {\n            this._viewCache.push(view);\n        }\n        else {\n            const index = viewContainerRef.indexOf(view);\n            // The host component could remove views from the container outside of\n            // the view repeater. It's unlikely this will occur, but just in case,\n            // destroy the view on its own, otherwise destroy it through the\n            // container to ensure that all the references are removed.\n            if (index === -1) {\n                view.destroy();\n            }\n            else {\n                viewContainerRef.remove(index);\n            }\n        }\n    }\n    /** Inserts a recycled view from the cache at the given index. */\n    _insertViewFromCache(index, viewContainerRef) {\n        const cachedView = this._viewCache.pop();\n        if (cachedView) {\n            viewContainerRef.insert(cachedView, index);\n        }\n        return cachedView || null;\n    }\n}\n\nexport { ArrayDataSource as A, _RecycleViewRepeaterStrategy as _, _ViewRepeaterOperation as a, _VIEW_REPEATER_STRATEGY as b };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,EAAE,QAAQ,MAAM;AACvC,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC5D,SAASC,cAAc,QAAQ,eAAe;;AAE9C;AACA,MAAMC,eAAe,SAASF,UAAU,CAAC;EACrCG,KAAK;EACLC,WAAWA,CAACD,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAE,OAAOA,CAAA,EAAG;IACN,OAAOR,YAAY,CAAC,IAAI,CAACM,KAAK,CAAC,GAAG,IAAI,CAACA,KAAK,GAAGL,EAAE,CAAC,IAAI,CAACK,KAAK,CAAC;EACjE;EACAG,UAAUA,CAAA,EAAG,CAAE;AACnB;;AAEA;AACA,IAAIC,sBAAsB;AAC1B,CAAC,UAAUA,sBAAsB,EAAE;EAC/B;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC3E;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC3E;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACrE;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;AAC7E,CAAC,EAAEA,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG,IAAIP,cAAc,CAAC,eAAe,CAAC;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,4BAA4B,CAAC;EAC/B;AACJ;AACA;AACA;EACIC,aAAa,GAAG,EAAE;EAClB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,UAAU,GAAG,EAAE;EACf;EACAC,YAAYA,CAACC,OAAO,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,eAAe,EAAE;IAC5F;IACAJ,OAAO,CAACK,gBAAgB,CAAC,CAACC,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,KAAK;MACtE,IAAIC,IAAI;MACR,IAAIC,SAAS;MACb,IAAIJ,MAAM,CAACK,aAAa,IAAI,IAAI,EAAE;QAC9B;QACA,MAAMC,eAAe,GAAGA,CAAA,KAAMV,kBAAkB,CAACI,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,CAAC;QAC7FC,IAAI,GAAG,IAAI,CAACI,WAAW,CAACD,eAAe,EAAEJ,YAAY,EAAEP,gBAAgB,EAAEE,iBAAiB,CAACG,MAAM,CAAC,CAAC;QACnGI,SAAS,GAAGD,IAAI,GAAGf,sBAAsB,CAACoB,QAAQ,GAAGpB,sBAAsB,CAACqB,QAAQ;MACxF,CAAC,MACI,IAAIP,YAAY,IAAI,IAAI,EAAE;QAC3B;QACA,IAAI,CAACQ,mBAAmB,CAACT,qBAAqB,EAAEN,gBAAgB,CAAC;QACjES,SAAS,GAAGhB,sBAAsB,CAACuB,OAAO;MAC9C,CAAC,MACI;QACD;QACAR,IAAI,GAAG,IAAI,CAACS,SAAS,CAACX,qBAAqB,EAAEC,YAAY,EAAEP,gBAAgB,EAAEE,iBAAiB,CAACG,MAAM,CAAC,CAAC;QACvGI,SAAS,GAAGhB,sBAAsB,CAACyB,KAAK;MAC5C;MACA,IAAIf,eAAe,EAAE;QACjBA,eAAe,CAAC;UACZgB,OAAO,EAAEX,IAAI,EAAEW,OAAO;UACtBV,SAAS;UACTJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACAe,MAAMA,CAAA,EAAG;IACL,KAAK,MAAMZ,IAAI,IAAI,IAAI,CAACX,UAAU,EAAE;MAChCW,IAAI,CAACa,OAAO,CAAC,CAAC;IAClB;IACA,IAAI,CAACxB,UAAU,GAAG,EAAE;EACxB;EACA;AACJ;AACA;AACA;EACIe,WAAWA,CAACD,eAAe,EAAEJ,YAAY,EAAEP,gBAAgB,EAAEsB,KAAK,EAAE;IAChE,MAAMC,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAACjB,YAAY,EAAEP,gBAAgB,CAAC;IAC5E,IAAIuB,UAAU,EAAE;MACZA,UAAU,CAACJ,OAAO,CAACM,SAAS,GAAGH,KAAK;MACpC,OAAOI,SAAS;IACpB;IACA,MAAMC,QAAQ,GAAGhB,eAAe,CAAC,CAAC;IAClC,OAAOX,gBAAgB,CAAC4B,kBAAkB,CAACD,QAAQ,CAACE,WAAW,EAAEF,QAAQ,CAACR,OAAO,EAAEQ,QAAQ,CAACG,KAAK,CAAC;EACtG;EACA;EACAf,mBAAmBA,CAACe,KAAK,EAAE9B,gBAAgB,EAAE;IACzC,MAAM+B,YAAY,GAAG/B,gBAAgB,CAACoB,MAAM,CAACU,KAAK,CAAC;IACnD,IAAI,CAACE,eAAe,CAACD,YAAY,EAAE/B,gBAAgB,CAAC;EACxD;EACA;EACAiB,SAASA,CAACX,qBAAqB,EAAEC,YAAY,EAAEP,gBAAgB,EAAEsB,KAAK,EAAE;IACpE,MAAMd,IAAI,GAAGR,gBAAgB,CAACiC,GAAG,CAAC3B,qBAAqB,CAAC;IACxDN,gBAAgB,CAACkC,IAAI,CAAC1B,IAAI,EAAED,YAAY,CAAC;IACzCC,IAAI,CAACW,OAAO,CAACM,SAAS,GAAGH,KAAK;IAC9B,OAAOd,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIwB,eAAeA,CAACxB,IAAI,EAAER,gBAAgB,EAAE;IACpC,IAAI,IAAI,CAACH,UAAU,CAACsC,MAAM,GAAG,IAAI,CAACvC,aAAa,EAAE;MAC7C,IAAI,CAACC,UAAU,CAACuC,IAAI,CAAC5B,IAAI,CAAC;IAC9B,CAAC,MACI;MACD,MAAMsB,KAAK,GAAG9B,gBAAgB,CAACqC,OAAO,CAAC7B,IAAI,CAAC;MAC5C;MACA;MACA;MACA;MACA,IAAIsB,KAAK,KAAK,CAAC,CAAC,EAAE;QACdtB,IAAI,CAACa,OAAO,CAAC,CAAC;MAClB,CAAC,MACI;QACDrB,gBAAgB,CAACsC,MAAM,CAACR,KAAK,CAAC;MAClC;IACJ;EACJ;EACA;EACAN,oBAAoBA,CAACM,KAAK,EAAE9B,gBAAgB,EAAE;IAC1C,MAAMuB,UAAU,GAAG,IAAI,CAAC1B,UAAU,CAAC0C,GAAG,CAAC,CAAC;IACxC,IAAIhB,UAAU,EAAE;MACZvB,gBAAgB,CAACwC,MAAM,CAACjB,UAAU,EAAEO,KAAK,CAAC;IAC9C;IACA,OAAOP,UAAU,IAAI,IAAI;EAC7B;AACJ;AAEA,SAASnC,eAAe,IAAIqD,CAAC,EAAE9C,4BAA4B,IAAI+C,CAAC,EAAEjD,sBAAsB,IAAIkD,CAAC,EAAEjD,uBAAuB,IAAIkD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}