import { Entity } from '@loopback/repository';
import { User } from './user.model';
export declare class UserCredentials extends Entity {
    id: string;
    password: string;
    otpSecret?: string;
    backupCodes?: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    constructor(data?: Partial<UserCredentials>);
}
export interface UserCredentialsRelations {
    user?: User;
}
export type UserCredentialsWithRelations = UserCredentials & UserCredentialsRelations;
