import { Entity } from '@loopback/repository';
import { User } from './user.model';
export declare class Payment extends Entity {
    id: string;
    razorpayOrderId: string;
    razorpayPaymentId?: string;
    razorpaySignature?: string;
    amount: number;
    currency: string;
    status: string;
    description?: string;
    metadata?: object;
    createdAt: Date;
    updatedAt: Date;
    paidAt?: Date;
    userId: string;
    constructor(data?: Partial<Payment>);
}
export interface PaymentRelations {
    user?: User;
}
export type PaymentWithRelations = Payment & PaymentRelations;
