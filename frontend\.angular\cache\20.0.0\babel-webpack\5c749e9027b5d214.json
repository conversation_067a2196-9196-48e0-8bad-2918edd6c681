{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, NgModule } from '@angular/core';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nconst _c0 = [\"determinateSpinner\"];\nfunction MatProgressSpinner_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 11);\n    i0.ɵɵelement(1, \"circle\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"viewBox\", ctx_r0._viewBox());\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"stroke-dasharray\", ctx_r0._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx_r0._strokeCircumference() / 2, \"px\")(\"stroke-width\", ctx_r0._circleStrokeWidth(), \"%\");\n    i0.ɵɵattribute(\"r\", ctx_r0._circleRadius());\n  }\n}\nconst MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken('mat-progress-spinner-default-options', {\n  providedIn: 'root',\n  factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    diameter: BASE_SIZE\n  };\n}\n/**\n * Base reference size of the spinner.\n */\nconst BASE_SIZE = 100;\n/**\n * Base reference stroke width of the spinner.\n */\nconst BASE_STROKE_WIDTH = 10;\nclass MatProgressSpinner {\n  _elementRef = inject(ElementRef);\n  /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */\n  _noopAnimations;\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the progress spinner. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-spinner/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value) {\n    this._color = value;\n  }\n  _color;\n  _defaultColor = 'primary';\n  /** The element of the determinate spinner. */\n  _determinateCircle;\n  constructor() {\n    const defaults = inject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS);\n    this._noopAnimations = _animationsDisabled() && !!defaults && !defaults._forceAnimations;\n    this.mode = this._elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner' ? 'indeterminate' : 'determinate';\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n      if (defaults.diameter) {\n        this.diameter = defaults.diameter;\n      }\n      if (defaults.strokeWidth) {\n        this.strokeWidth = defaults.strokeWidth;\n      }\n    }\n  }\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  mode;\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  get value() {\n    return this.mode === 'determinate' ? this._value : 0;\n  }\n  set value(v) {\n    this._value = Math.max(0, Math.min(100, v || 0));\n  }\n  _value = 0;\n  /** The diameter of the progress spinner (will set width and height of svg). */\n  get diameter() {\n    return this._diameter;\n  }\n  set diameter(size) {\n    this._diameter = size || 0;\n  }\n  _diameter = BASE_SIZE;\n  /** Stroke width of the progress spinner. */\n  get strokeWidth() {\n    return this._strokeWidth ?? this.diameter / 10;\n  }\n  set strokeWidth(value) {\n    this._strokeWidth = value || 0;\n  }\n  _strokeWidth;\n  /** The radius of the spinner, adjusted for stroke width. */\n  _circleRadius() {\n    return (this.diameter - BASE_STROKE_WIDTH) / 2;\n  }\n  /** The view box of the spinner's svg element. */\n  _viewBox() {\n    const viewBox = this._circleRadius() * 2 + this.strokeWidth;\n    return `0 0 ${viewBox} ${viewBox}`;\n  }\n  /** The stroke circumference of the svg circle. */\n  _strokeCircumference() {\n    return 2 * Math.PI * this._circleRadius();\n  }\n  /** The dash offset of the svg circle. */\n  _strokeDashOffset() {\n    if (this.mode === 'determinate') {\n      return this._strokeCircumference() * (100 - this._value) / 100;\n    }\n    return null;\n  }\n  /** Stroke width of the circle in percent. */\n  _circleStrokeWidth() {\n    return this.strokeWidth / this.diameter * 100;\n  }\n  static ɵfac = function MatProgressSpinner_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatProgressSpinner)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatProgressSpinner,\n    selectors: [[\"mat-progress-spinner\"], [\"mat-spinner\"]],\n    viewQuery: function MatProgressSpinner_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._determinateCircle = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"progressbar\", \"tabindex\", \"-1\", 1, \"mat-mdc-progress-spinner\", \"mdc-circular-progress\"],\n    hostVars: 18,\n    hostBindings: function MatProgressSpinner_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-valuemin\", 0)(\"aria-valuemax\", 100)(\"aria-valuenow\", ctx.mode === \"determinate\" ? ctx.value : null)(\"mode\", ctx.mode);\n        i0.ɵɵclassMap(\"mat-\" + ctx.color);\n        i0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\")(\"--mat-progress-spinner-size\", ctx.diameter + \"px\")(\"--mat-progress-spinner-active-indicator-width\", ctx.diameter + \"px\");\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._noopAnimations)(\"mdc-circular-progress--indeterminate\", ctx.mode === \"indeterminate\");\n      }\n    },\n    inputs: {\n      color: \"color\",\n      mode: \"mode\",\n      value: [2, \"value\", \"value\", numberAttribute],\n      diameter: [2, \"diameter\", \"diameter\", numberAttribute],\n      strokeWidth: [2, \"strokeWidth\", \"strokeWidth\", numberAttribute]\n    },\n    exportAs: [\"matProgressSpinner\"],\n    decls: 14,\n    vars: 11,\n    consts: [[\"circle\", \"\"], [\"determinateSpinner\", \"\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__determinate-container\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__determinate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\", 1, \"mdc-circular-progress__determinate-circle\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__indeterminate-container\"], [1, \"mdc-circular-progress__spinner-layer\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-left\"], [3, \"ngTemplateOutlet\"], [1, \"mdc-circular-progress__gap-patch\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-right\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__indeterminate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\"]],\n    template: function MatProgressSpinner_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatProgressSpinner_ng_template_0_Template, 2, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(2, \"div\", 2, 1);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(4, \"svg\", 3);\n        i0.ɵɵelement(5, \"circle\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n        i0.ɵɵelementContainer(9, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 9);\n        i0.ɵɵelementContainer(11, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 10);\n        i0.ɵɵelementContainer(13, 8);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        const circle_r2 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"viewBox\", ctx._viewBox());\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"stroke-dasharray\", ctx._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx._strokeDashOffset(), \"px\")(\"stroke-width\", ctx._circleStrokeWidth(), \"%\");\n        i0.ɵɵattribute(\"r\", ctx._circleRadius());\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n      }\n    },\n    dependencies: [NgTemplateOutlet],\n    styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mat-progress-spinner-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mat-progress-spinner-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'mat-progress-spinner, mat-spinner',\n      exportAs: 'matProgressSpinner',\n      host: {\n        'role': 'progressbar',\n        'class': 'mat-mdc-progress-spinner mdc-circular-progress',\n        // set tab index to -1 so screen readers will read the aria-label\n        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n        'tabindex': '-1',\n        '[class]': '\"mat-\" + color',\n        '[class._mat-animation-noopable]': `_noopAnimations`,\n        '[class.mdc-circular-progress--indeterminate]': 'mode === \"indeterminate\"',\n        '[style.width.px]': 'diameter',\n        '[style.height.px]': 'diameter',\n        '[style.--mat-progress-spinner-size]': 'diameter + \"px\"',\n        '[style.--mat-progress-spinner-active-indicator-width]': 'diameter + \"px\"',\n        '[attr.aria-valuemin]': '0',\n        '[attr.aria-valuemax]': '100',\n        '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n        '[attr.mode]': 'mode'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [NgTemplateOutlet],\n      template: \"<ng-template #circle>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__indeterminate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeCircumference() / 2\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</ng-template>\\n\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-circular-progress__determinate-container\\\" aria-hidden=\\\"true\\\" #determinateSpinner>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__determinate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeDashOffset()\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            class=\\\"mdc-circular-progress__determinate-circle\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</div>\\n<!--TODO: figure out why there are 3 separate svgs-->\\n<div class=\\\"mdc-circular-progress__indeterminate-container\\\" aria-hidden=\\\"true\\\">\\n  <div class=\\\"mdc-circular-progress__spinner-layer\\\">\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__gap-patch\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mat-progress-spinner-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mat-progress-spinner-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    _determinateCircle: [{\n      type: ViewChild,\n      args: ['determinateSpinner']\n    }],\n    mode: [{\n      type: Input\n    }],\n    value: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    diameter: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    strokeWidth: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }]\n  });\n})();\n/**\n * @deprecated Import Progress Spinner instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 16.0.0\n */\n// tslint:disable-next-line:variable-name\nconst MatSpinner = MatProgressSpinner;\nclass MatProgressSpinnerModule {\n  static ɵfac = function MatProgressSpinnerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatProgressSpinnerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatProgressSpinnerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatProgressSpinner, MatSpinner],\n      exports: [MatProgressSpinner, MatSpinner, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, MatProgressSpinner, MatProgressSpinnerModule, MatSpinner };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "ElementRef", "numberAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ViewChild", "NgModule", "NgTemplateOutlet", "_", "_animationsDisabled", "M", "MatCommonModule", "_c0", "MatProgressSpinner_ng_template_0_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵattribute", "_viewBox", "ɵɵadvance", "ɵɵstyleProp", "_strokeCircumference", "_circleStrokeWidth", "_circleRadius", "MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY", "diameter", "BASE_SIZE", "BASE_STROKE_WIDTH", "MatProgressSpinner", "_elementRef", "_noopAnimations", "color", "_color", "_defaultColor", "value", "_determinateCircle", "constructor", "defaults", "_forceAnimations", "mode", "nativeElement", "nodeName", "toLowerCase", "strokeWidth", "_value", "v", "Math", "max", "min", "_diameter", "size", "_strokeWidth", "viewBox", "PI", "_strokeDashOffset", "ɵfac", "MatProgressSpinner_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "MatProgressSpinner_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "MatProgressSpinner_HostBindings", "ɵɵclassMap", "ɵɵclassProp", "inputs", "exportAs", "decls", "vars", "consts", "template", "MatProgressSpinner_Template", "ɵɵtemplate", "ɵɵtemplateRefExtractor", "ɵɵnamespaceHTML", "ɵɵelementContainer", "circle_r2", "ɵɵreference", "ɵɵproperty", "dependencies", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "None", "imports", "transform", "<PERSON><PERSON><PERSON><PERSON>", "MatProgressSpinnerModule", "MatProgressSpinnerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/material/fesm2022/progress-spinner.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, NgModule } from '@angular/core';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nconst MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken('mat-progress-spinner-default-options', {\n    providedIn: 'root',\n    factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {\n    return { diameter: BASE_SIZE };\n}\n/**\n * Base reference size of the spinner.\n */\nconst BASE_SIZE = 100;\n/**\n * Base reference stroke width of the spinner.\n */\nconst BASE_STROKE_WIDTH = 10;\nclass MatProgressSpinner {\n    _elementRef = inject(ElementRef);\n    /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */\n    _noopAnimations;\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /**\n     * Theme color of the progress spinner. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-spinner/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n        return this._color || this._defaultColor;\n    }\n    set color(value) {\n        this._color = value;\n    }\n    _color;\n    _defaultColor = 'primary';\n    /** The element of the determinate spinner. */\n    _determinateCircle;\n    constructor() {\n        const defaults = inject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS);\n        this._noopAnimations = _animationsDisabled() && !!defaults && !defaults._forceAnimations;\n        this.mode =\n            this._elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner'\n                ? 'indeterminate'\n                : 'determinate';\n        if (defaults) {\n            if (defaults.color) {\n                this.color = this._defaultColor = defaults.color;\n            }\n            if (defaults.diameter) {\n                this.diameter = defaults.diameter;\n            }\n            if (defaults.strokeWidth) {\n                this.strokeWidth = defaults.strokeWidth;\n            }\n        }\n    }\n    /**\n     * Mode of the progress bar.\n     *\n     * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n     * 'determinate'.\n     * Mirrored to mode attribute.\n     */\n    mode;\n    /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n    get value() {\n        return this.mode === 'determinate' ? this._value : 0;\n    }\n    set value(v) {\n        this._value = Math.max(0, Math.min(100, v || 0));\n    }\n    _value = 0;\n    /** The diameter of the progress spinner (will set width and height of svg). */\n    get diameter() {\n        return this._diameter;\n    }\n    set diameter(size) {\n        this._diameter = size || 0;\n    }\n    _diameter = BASE_SIZE;\n    /** Stroke width of the progress spinner. */\n    get strokeWidth() {\n        return this._strokeWidth ?? this.diameter / 10;\n    }\n    set strokeWidth(value) {\n        this._strokeWidth = value || 0;\n    }\n    _strokeWidth;\n    /** The radius of the spinner, adjusted for stroke width. */\n    _circleRadius() {\n        return (this.diameter - BASE_STROKE_WIDTH) / 2;\n    }\n    /** The view box of the spinner's svg element. */\n    _viewBox() {\n        const viewBox = this._circleRadius() * 2 + this.strokeWidth;\n        return `0 0 ${viewBox} ${viewBox}`;\n    }\n    /** The stroke circumference of the svg circle. */\n    _strokeCircumference() {\n        return 2 * Math.PI * this._circleRadius();\n    }\n    /** The dash offset of the svg circle. */\n    _strokeDashOffset() {\n        if (this.mode === 'determinate') {\n            return (this._strokeCircumference() * (100 - this._value)) / 100;\n        }\n        return null;\n    }\n    /** Stroke width of the circle in percent. */\n    _circleStrokeWidth() {\n        return (this.strokeWidth / this.diameter) * 100;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressSpinner, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatProgressSpinner, isStandalone: true, selector: \"mat-progress-spinner, mat-spinner\", inputs: { color: \"color\", mode: \"mode\", value: [\"value\", \"value\", numberAttribute], diameter: [\"diameter\", \"diameter\", numberAttribute], strokeWidth: [\"strokeWidth\", \"strokeWidth\", numberAttribute] }, host: { attributes: { \"role\": \"progressbar\", \"tabindex\": \"-1\" }, properties: { \"class\": \"\\\"mat-\\\" + color\", \"class._mat-animation-noopable\": \"_noopAnimations\", \"class.mdc-circular-progress--indeterminate\": \"mode === \\\"indeterminate\\\"\", \"style.width.px\": \"diameter\", \"style.height.px\": \"diameter\", \"style.--mat-progress-spinner-size\": \"diameter + \\\"px\\\"\", \"style.--mat-progress-spinner-active-indicator-width\": \"diameter + \\\"px\\\"\", \"attr.aria-valuemin\": \"0\", \"attr.aria-valuemax\": \"100\", \"attr.aria-valuenow\": \"mode === \\\"determinate\\\" ? value : null\", \"attr.mode\": \"mode\" }, classAttribute: \"mat-mdc-progress-spinner mdc-circular-progress\" }, viewQueries: [{ propertyName: \"_determinateCircle\", first: true, predicate: [\"determinateSpinner\"], descendants: true }], exportAs: [\"matProgressSpinner\"], ngImport: i0, template: \"<ng-template #circle>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__indeterminate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeCircumference() / 2\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</ng-template>\\n\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-circular-progress__determinate-container\\\" aria-hidden=\\\"true\\\" #determinateSpinner>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__determinate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeDashOffset()\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            class=\\\"mdc-circular-progress__determinate-circle\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</div>\\n<!--TODO: figure out why there are 3 separate svgs-->\\n<div class=\\\"mdc-circular-progress__indeterminate-container\\\" aria-hidden=\\\"true\\\">\\n  <div class=\\\"mdc-circular-progress__spinner-layer\\\">\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__gap-patch\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mat-progress-spinner-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mat-progress-spinner-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"], dependencies: [{ kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressSpinner, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-progress-spinner, mat-spinner', exportAs: 'matProgressSpinner', host: {\n                        'role': 'progressbar',\n                        'class': 'mat-mdc-progress-spinner mdc-circular-progress',\n                        // set tab index to -1 so screen readers will read the aria-label\n                        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n                        'tabindex': '-1',\n                        '[class]': '\"mat-\" + color',\n                        '[class._mat-animation-noopable]': `_noopAnimations`,\n                        '[class.mdc-circular-progress--indeterminate]': 'mode === \"indeterminate\"',\n                        '[style.width.px]': 'diameter',\n                        '[style.height.px]': 'diameter',\n                        '[style.--mat-progress-spinner-size]': 'diameter + \"px\"',\n                        '[style.--mat-progress-spinner-active-indicator-width]': 'diameter + \"px\"',\n                        '[attr.aria-valuemin]': '0',\n                        '[attr.aria-valuemax]': '100',\n                        '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n                        '[attr.mode]': 'mode',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, imports: [NgTemplateOutlet], template: \"<ng-template #circle>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__indeterminate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeCircumference() / 2\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</ng-template>\\n\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-circular-progress__determinate-container\\\" aria-hidden=\\\"true\\\" #determinateSpinner>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__determinate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeDashOffset()\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            class=\\\"mdc-circular-progress__determinate-circle\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</div>\\n<!--TODO: figure out why there are 3 separate svgs-->\\n<div class=\\\"mdc-circular-progress__indeterminate-container\\\" aria-hidden=\\\"true\\\">\\n  <div class=\\\"mdc-circular-progress__spinner-layer\\\">\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__gap-patch\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mat-progress-spinner-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mat-progress-spinner-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input\n            }], _determinateCircle: [{\n                type: ViewChild,\n                args: ['determinateSpinner']\n            }], mode: [{\n                type: Input\n            }], value: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], diameter: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], strokeWidth: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }] } });\n/**\n * @deprecated Import Progress Spinner instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 16.0.0\n */\n// tslint:disable-next-line:variable-name\nconst MatSpinner = MatProgressSpinner;\n\nclass MatProgressSpinnerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressSpinnerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressSpinnerModule, imports: [MatProgressSpinner, MatSpinner], exports: [MatProgressSpinner, MatSpinner, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressSpinnerModule, imports: [MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressSpinnerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatProgressSpinner, MatSpinner],\n                    exports: [MatProgressSpinner, MatSpinner, MatCommonModule],\n                }]\n        }] });\n\nexport { MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, MatProgressSpinner, MatProgressSpinnerModule, MatSpinner };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,eAAe,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACtK,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,qBAAqB;AAC5B,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;;AAE1B;AAAA,MAAAC,GAAA;AAAA,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAsH6FlB,EAAE,CAAAoB,cAAA;IAAFpB,EAAE,CAAAqB,cAAA,aAC8wC,CAAC;IADjxCrB,EAAE,CAAAsB,SAAA,gBACuiD,CAAC;IAD1iDtB,EAAE,CAAAuB,YAAA,CACijD,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,MAAA,GADpjDxB,EAAE,CAAAyB,aAAA;IAAFzB,EAAE,CAAA0B,WAAA,YAAAF,MAAA,CAAAG,QAAA;IAAF3B,EAAE,CAAA4B,SAAA,CAC43C,CAAC;IAD/3C5B,EAAE,CAAA6B,WAAA,qBAAAL,MAAA,CAAAM,oBAAA,QAC43C,CAAC,sBAAAN,MAAA,CAAAM,oBAAA,YAAwE,CAAC,iBAAAN,MAAA,CAAAO,kBAAA,OAA4D,CAAC;IADrgD/B,EAAE,CAAA0B,WAAA,MAAAF,MAAA,CAAAQ,aAAA;EAAA;AAAA;AArH/F,MAAMC,oCAAoC,GAAG,IAAIhC,cAAc,CAAC,sCAAsC,EAAE;EACpGiC,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,4CAA4CA,CAAA,EAAG;EACpD,OAAO;IAAEC,QAAQ,EAAEC;EAAU,CAAC;AAClC;AACA;AACA;AACA;AACA,MAAMA,SAAS,GAAG,GAAG;AACrB;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,EAAE;AAC5B,MAAMC,kBAAkB,CAAC;EACrBC,WAAW,GAAGvC,MAAM,CAACC,UAAU,CAAC;EAChC;EACAuC,eAAe;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM,IAAI,IAAI,CAACC,aAAa;EAC5C;EACA,IAAIF,KAAKA,CAACG,KAAK,EAAE;IACb,IAAI,CAACF,MAAM,GAAGE,KAAK;EACvB;EACAF,MAAM;EACNC,aAAa,GAAG,SAAS;EACzB;EACAE,kBAAkB;EAClBC,WAAWA,CAAA,EAAG;IACV,MAAMC,QAAQ,GAAG/C,MAAM,CAAC+B,oCAAoC,CAAC;IAC7D,IAAI,CAACS,eAAe,GAAG7B,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAACoC,QAAQ,IAAI,CAACA,QAAQ,CAACC,gBAAgB;IACxF,IAAI,CAACC,IAAI,GACL,IAAI,CAACV,WAAW,CAACW,aAAa,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,aAAa,GACjE,eAAe,GACf,aAAa;IACvB,IAAIL,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACN,KAAK,EAAE;QAChB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACE,aAAa,GAAGI,QAAQ,CAACN,KAAK;MACpD;MACA,IAAIM,QAAQ,CAACZ,QAAQ,EAAE;QACnB,IAAI,CAACA,QAAQ,GAAGY,QAAQ,CAACZ,QAAQ;MACrC;MACA,IAAIY,QAAQ,CAACM,WAAW,EAAE;QACtB,IAAI,CAACA,WAAW,GAAGN,QAAQ,CAACM,WAAW;MAC3C;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIJ,IAAI;EACJ;EACA,IAAIL,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACK,IAAI,KAAK,aAAa,GAAG,IAAI,CAACK,MAAM,GAAG,CAAC;EACxD;EACA,IAAIV,KAAKA,CAACW,CAAC,EAAE;IACT,IAAI,CAACD,MAAM,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEH,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD;EACAD,MAAM,GAAG,CAAC;EACV;EACA,IAAInB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACwB,SAAS;EACzB;EACA,IAAIxB,QAAQA,CAACyB,IAAI,EAAE;IACf,IAAI,CAACD,SAAS,GAAGC,IAAI,IAAI,CAAC;EAC9B;EACAD,SAAS,GAAGvB,SAAS;EACrB;EACA,IAAIiB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACQ,YAAY,IAAI,IAAI,CAAC1B,QAAQ,GAAG,EAAE;EAClD;EACA,IAAIkB,WAAWA,CAACT,KAAK,EAAE;IACnB,IAAI,CAACiB,YAAY,GAAGjB,KAAK,IAAI,CAAC;EAClC;EACAiB,YAAY;EACZ;EACA/B,aAAaA,CAAA,EAAG;IACZ,OAAO,CAAC,IAAI,CAACK,QAAQ,GAAGE,iBAAiB,IAAI,CAAC;EAClD;EACA;EACAZ,QAAQA,CAAA,EAAG;IACP,MAAMqC,OAAO,GAAG,IAAI,CAAChC,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACuB,WAAW;IAC3D,OAAO,OAAOS,OAAO,IAAIA,OAAO,EAAE;EACtC;EACA;EACAlC,oBAAoBA,CAAA,EAAG;IACnB,OAAO,CAAC,GAAG4B,IAAI,CAACO,EAAE,GAAG,IAAI,CAACjC,aAAa,CAAC,CAAC;EAC7C;EACA;EACAkC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACf,IAAI,KAAK,aAAa,EAAE;MAC7B,OAAQ,IAAI,CAACrB,oBAAoB,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC0B,MAAM,CAAC,GAAI,GAAG;IACpE;IACA,OAAO,IAAI;EACf;EACA;EACAzB,kBAAkBA,CAAA,EAAG;IACjB,OAAQ,IAAI,CAACwB,WAAW,GAAG,IAAI,CAAClB,QAAQ,GAAI,GAAG;EACnD;EACA,OAAO8B,IAAI,YAAAC,2BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwF7B,kBAAkB;EAAA;EACrH,OAAO8B,IAAI,kBAD8EtE,EAAE,CAAAuE,iBAAA;IAAAC,IAAA,EACJhC,kBAAkB;IAAAiC,SAAA;IAAAC,SAAA,WAAAC,yBAAAzD,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADhBlB,EAAE,CAAA4E,WAAA,CAAA5D,GAAA;MAAA;MAAA,IAAAE,EAAA;QAAA,IAAA2D,EAAA;QAAF7E,EAAE,CAAA8E,cAAA,CAAAD,EAAA,GAAF7E,EAAE,CAAA+E,WAAA,QAAA5D,GAAA,CAAA4B,kBAAA,GAAA8B,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA,WAC0T,aAAa,cAAc,IAAI;IAAAC,QAAA;IAAAC,YAAA,WAAAC,gCAAAlE,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAD3VlB,EAAE,CAAA0B,WAAA,kBACJ,CAAC,mBAAD,GAAG,mBAAAP,GAAA,CAAAgC,IAAA,KAAM,aAAa,GAAAhC,GAAA,CAAA2B,KAAA,GAAW,IAAI,UAAA3B,GAAA,CAAAgC,IAAA;QADnCnD,EAAE,CAAAqF,UAAA,CACJ,MAAM,GAAAlE,GAAA,CAAAwB,KAAW,CAAC;QADhB3C,EAAE,CAAA6B,WAAA,UAAAV,GAAA,CAAAkB,QAAA,MACa,CAAC,WAAAlB,GAAA,CAAAkB,QAAA,MAAD,CAAC,gCAAAlB,GAAA,CAAAkB,QAAA,GAAP,IAAM,CAAC,kDAAAlB,GAAA,CAAAkB,QAAA,GAAP,IAAM,CAAC;QADhBrC,EAAE,CAAAsF,WAAA,4BAAAnE,GAAA,CAAAuB,eACa,CAAC,yCAAAvB,GAAA,CAAAgC,IAAA,KAAT,eAAQ,CAAC;MAAA;IAAA;IAAAoC,MAAA;MAAA5C,KAAA;MAAAQ,IAAA;MAAAL,KAAA,wBAAuI1C,eAAe;MAAAiC,QAAA,8BAAsCjC,eAAe;MAAAmD,WAAA,oCAA+CnD,eAAe;IAAA;IAAAoF,QAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAA3E,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADzRlB,EAAE,CAAA8F,UAAA,IAAA7E,yCAAA,gCAAFjB,EAAE,CAAA+F,sBACwmC,CAAC;QAD3mC/F,EAAE,CAAAqB,cAAA,eAC21D,CAAC;QAD91DrB,EAAE,CAAAoB,cAAA;QAAFpB,EAAE,CAAAqB,cAAA,YAC+/D,CAAC;QADlgErB,EAAE,CAAAsB,SAAA,eACk1E,CAAC;QADr1EtB,EAAE,CAAAuB,YAAA,CAC41E,CAAC,CAAO,CAAC;QADv2EvB,EAAE,CAAAgG,eAAA;QAAFhG,EAAE,CAAAqB,cAAA,YACg/E,CAAC,YAAuD,CAAC,YAA6F,CAAC;QADzoFrB,EAAE,CAAAiG,kBAAA,KACysF,CAAC;QAD5sFjG,EAAE,CAAAuB,YAAA,CACqtF,CAAC;QADxtFvB,EAAE,CAAAqB,cAAA,aAC2wF,CAAC;QAD9wFrB,EAAE,CAAAiG,kBAAA,MAC80F,CAAC;QADj1FjG,EAAE,CAAAuB,YAAA,CAC01F,CAAC;QAD71FvB,EAAE,CAAAqB,cAAA,cACy7F,CAAC;QAD57FrB,EAAE,CAAAiG,kBAAA,MAC4/F,CAAC;QAD//FjG,EAAE,CAAAuB,YAAA,CACwgG,CAAC,CAAS,CAAC,CAAO,CAAC;MAAA;MAAA,IAAAL,EAAA;QAAA,MAAAgF,SAAA,GAD7hGlG,EAAE,CAAAmG,WAAA;QAAFnG,EAAE,CAAA4B,SAAA,EACi4D,CAAC;QADp4D5B,EAAE,CAAA0B,WAAA,YAAAP,GAAA,CAAAQ,QAAA;QAAF3B,EAAE,CAAA4B,SAAA,CAC6mE,CAAC;QADhnE5B,EAAE,CAAA6B,WAAA,qBAAAV,GAAA,CAAAW,oBAAA,QAC6mE,CAAC,sBAAAX,GAAA,CAAA+C,iBAAA,QAAiE,CAAC,iBAAA/C,GAAA,CAAAY,kBAAA,OAA4D,CAAC;QAD/uE/B,EAAE,CAAA0B,WAAA,MAAAP,GAAA,CAAAa,aAAA;QAAFhC,EAAE,CAAA4B,SAAA,EACyrF,CAAC;QAD5rF5B,EAAE,CAAAoG,UAAA,qBAAAF,SACyrF,CAAC;QAD5rFlG,EAAE,CAAA4B,SAAA,EAC8zF,CAAC;QADj0F5B,EAAE,CAAAoG,UAAA,qBAAAF,SAC8zF,CAAC;QADj0FlG,EAAE,CAAA4B,SAAA,EAC4+F,CAAC;QAD/+F5B,EAAE,CAAAoG,UAAA,qBAAAF,SAC4+F,CAAC;MAAA;IAAA;IAAAG,YAAA,GAA0xI1F,gBAAgB;IAAA2F,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACt3O;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FzG,EAAE,CAAA0G,iBAAA,CAGJlE,kBAAkB,EAAc,CAAC;IAChHgC,IAAI,EAAEnE,SAAS;IACfsG,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mCAAmC;MAAEpB,QAAQ,EAAE,oBAAoB;MAAEqB,IAAI,EAAE;QAClF,MAAM,EAAE,aAAa;QACrB,OAAO,EAAE,gDAAgD;QACzD;QACA;QACA,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,gBAAgB;QAC3B,iCAAiC,EAAE,iBAAiB;QACpD,8CAA8C,EAAE,0BAA0B;QAC1E,kBAAkB,EAAE,UAAU;QAC9B,mBAAmB,EAAE,UAAU;QAC/B,qCAAqC,EAAE,iBAAiB;QACxD,uDAAuD,EAAE,iBAAiB;QAC1E,sBAAsB,EAAE,GAAG;QAC3B,sBAAsB,EAAE,KAAK;QAC7B,sBAAsB,EAAE,uCAAuC;QAC/D,aAAa,EAAE;MACnB,CAAC;MAAEL,eAAe,EAAElG,uBAAuB,CAACwG,MAAM;MAAEP,aAAa,EAAEhG,iBAAiB,CAACwG,IAAI;MAAEC,OAAO,EAAE,CAACrG,gBAAgB,CAAC;MAAEiF,QAAQ,EAAE,28DAA28D;MAAEU,MAAM,EAAE,CAAC,irIAAirI;IAAE,CAAC;EACxxM,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE3D,KAAK,EAAE,CAAC;MAChD6B,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEuC,kBAAkB,EAAE,CAAC;MACrByB,IAAI,EAAE/D,SAAS;MACfkG,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAExD,IAAI,EAAE,CAAC;MACPqB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEsC,KAAK,EAAE,CAAC;MACR0B,IAAI,EAAEhE,KAAK;MACXmG,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE7G;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEiC,QAAQ,EAAE,CAAC;MACXmC,IAAI,EAAEhE,KAAK;MACXmG,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE7G;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEmD,WAAW,EAAE,CAAC;MACdiB,IAAI,EAAEhE,KAAK;MACXmG,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE7G;MAAgB,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8G,UAAU,GAAG1E,kBAAkB;AAErC,MAAM2E,wBAAwB,CAAC;EAC3B,OAAOhD,IAAI,YAAAiD,iCAAA/C,iBAAA;IAAA,YAAAA,iBAAA,IAAwF8C,wBAAwB;EAAA;EAC3H,OAAOE,IAAI,kBAlD8ErH,EAAE,CAAAsH,gBAAA;IAAA9C,IAAA,EAkDS2C;EAAwB;EAC5H,OAAOI,IAAI,kBAnD8EvH,EAAE,CAAAwH,gBAAA;IAAAR,OAAA,GAmD6CjG,eAAe;EAAA;AAC3J;AACA;EAAA,QAAA0F,SAAA,oBAAAA,SAAA,KArD6FzG,EAAE,CAAA0G,iBAAA,CAqDJS,wBAAwB,EAAc,CAAC;IACtH3C,IAAI,EAAE9D,QAAQ;IACdiG,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CAACxE,kBAAkB,EAAE0E,UAAU,CAAC;MACzCO,OAAO,EAAE,CAACjF,kBAAkB,EAAE0E,UAAU,EAAEnG,eAAe;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASkB,oCAAoC,EAAEG,4CAA4C,EAAEI,kBAAkB,EAAE2E,wBAAwB,EAAED,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}