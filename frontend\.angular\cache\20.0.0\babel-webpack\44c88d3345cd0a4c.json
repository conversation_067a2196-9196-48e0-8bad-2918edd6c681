{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, inject, NgZone, RendererFactory2, Injectable, ElementRef, Renderer2, DOCUMENT, ChangeDetectorRef, signal, Injector, afterNextRender, booleanAttribute, Optional, Inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, ViewContainerRef, TemplateRef, IterableDiffers, NgModule } from '@angular/core';\nimport { Subject, of, Observable, Subscription, animationFrameScheduler, asapScheduler, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, startWith, takeUntil, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport { g as getRtlScrollAxisType, R as RtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { BidiModule } from './bidi.mjs';\nconst _c0 = [\"contentWrapper\"];\nconst _c1 = [\"*\"];\nexport { Dir as ɵɵDir } from './bidi.mjs';\nimport { b as _VIEW_REPEATER_STRATEGY, A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport { i as isDataSource } from './data-source-D34wiQZj.mjs';\nimport '@angular/common';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n  _scrolledIndexChange = new Subject();\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n  /** The attached viewport. */\n  _viewport = null;\n  /** The size of the items in the virtually scrolling list. */\n  _itemSize;\n  /** The minimum amount of buffer rendered beyond the viewport (in pixels). */\n  _minBufferPx;\n  /** The number of buffer items to render beyond the edge of the viewport (in pixels). */\n  _maxBufferPx;\n  /**\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  constructor(itemSize, minBufferPx, maxBufferPx) {\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n  }\n  /**\n   * Attaches this scroll strategy to a viewport.\n   * @param viewport The viewport to attach this strategy to.\n   */\n  attach(viewport) {\n    this._viewport = viewport;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** Detaches this scroll strategy from the currently attached viewport. */\n  detach() {\n    this._scrolledIndexChange.complete();\n    this._viewport = null;\n  }\n  /**\n   * Update the item size and buffer size.\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n    if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n    }\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentScrolled() {\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onDataLengthChanged() {\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentRendered() {\n    /* no-op */\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onRenderedOffsetChanged() {\n    /* no-op */\n  }\n  /**\n   * Scroll to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling.\n   */\n  scrollToIndex(index, behavior) {\n    if (this._viewport) {\n      this._viewport.scrollToOffset(index * this._itemSize, behavior);\n    }\n  }\n  /** Update the viewport's total content size. */\n  _updateTotalContentSize() {\n    if (!this._viewport) {\n      return;\n    }\n    this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n  }\n  /** Update the viewport's rendered range. */\n  _updateRenderedRange() {\n    if (!this._viewport) {\n      return;\n    }\n    const renderedRange = this._viewport.getRenderedRange();\n    const newRange = {\n      start: renderedRange.start,\n      end: renderedRange.end\n    };\n    const viewportSize = this._viewport.getViewportSize();\n    const dataLength = this._viewport.getDataLength();\n    let scrollOffset = this._viewport.measureScrollOffset();\n    // Prevent NaN as result when dividing by zero.\n    let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n    // If user scrolls to the bottom of the list and data changes to a smaller list\n    if (newRange.end > dataLength) {\n      // We have to recalculate the first visible index based on new data length and viewport size.\n      const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n      const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n      // If first visible index changed we must update scroll offset to handle start/end buffers\n      // Current range must also be adjusted to cover the new position (bottom of new list).\n      if (firstVisibleIndex != newVisibleIndex) {\n        firstVisibleIndex = newVisibleIndex;\n        scrollOffset = newVisibleIndex * this._itemSize;\n        newRange.start = Math.floor(firstVisibleIndex);\n      }\n      newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n    }\n    const startBuffer = scrollOffset - newRange.start * this._itemSize;\n    if (startBuffer < this._minBufferPx && newRange.start != 0) {\n      const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n      newRange.start = Math.max(0, newRange.start - expandStart);\n      newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n    } else {\n      const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n      if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n        const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n        if (expandEnd > 0) {\n          newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n          newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n        }\n      }\n    }\n    this._viewport.setRenderedRange(newRange);\n    this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n    this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n  }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n  return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n  /** The size of the items in the list (in pixels). */\n  get itemSize() {\n    return this._itemSize;\n  }\n  set itemSize(value) {\n    this._itemSize = coerceNumberProperty(value);\n  }\n  _itemSize = 20;\n  /**\n   * The minimum amount of buffer rendered beyond the viewport (in pixels).\n   * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n   */\n  get minBufferPx() {\n    return this._minBufferPx;\n  }\n  set minBufferPx(value) {\n    this._minBufferPx = coerceNumberProperty(value);\n  }\n  _minBufferPx = 100;\n  /**\n   * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n   */\n  get maxBufferPx() {\n    return this._maxBufferPx;\n  }\n  set maxBufferPx(value) {\n    this._maxBufferPx = coerceNumberProperty(value);\n  }\n  _maxBufferPx = 200;\n  /** The scroll strategy used by this directive. */\n  _scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  ngOnChanges() {\n    this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  }\n  static ɵfac = function CdkFixedSizeVirtualScroll_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkFixedSizeVirtualScroll)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkFixedSizeVirtualScroll,\n    selectors: [[\"cdk-virtual-scroll-viewport\", \"itemSize\", \"\"]],\n    inputs: {\n      itemSize: \"itemSize\",\n      minBufferPx: \"minBufferPx\",\n      maxBufferPx: \"maxBufferPx\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: VIRTUAL_SCROLL_STRATEGY,\n      useFactory: _fixedSizeVirtualScrollStrategyFactory,\n      deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFixedSizeVirtualScroll, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport[itemSize]',\n      providers: [{\n        provide: VIRTUAL_SCROLL_STRATEGY,\n        useFactory: _fixedSizeVirtualScrollStrategyFactory,\n        deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n      }]\n    }]\n  }], null, {\n    itemSize: [{\n      type: Input\n    }],\n    minBufferPx: [{\n      type: Input\n    }],\n    maxBufferPx: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n  _ngZone = inject(NgZone);\n  _platform = inject(Platform);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _cleanupGlobalListener;\n  constructor() {}\n  /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n  _scrolled = new Subject();\n  /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n  _scrolledCount = 0;\n  /**\n   * Map of all the scrollable references that are registered with the service and their\n   * scroll event subscriptions.\n   */\n  scrollContainers = new Map();\n  /**\n   * Registers a scrollable instance with the service and listens for its scrolled events. When the\n   * scrollable is scrolled, the service emits the event to its scrolled observable.\n   * @param scrollable Scrollable instance to be registered.\n   */\n  register(scrollable) {\n    if (!this.scrollContainers.has(scrollable)) {\n      this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n    }\n  }\n  /**\n   * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n   * @param scrollable Scrollable instance to be deregistered.\n   */\n  deregister(scrollable) {\n    const scrollableReference = this.scrollContainers.get(scrollable);\n    if (scrollableReference) {\n      scrollableReference.unsubscribe();\n      this.scrollContainers.delete(scrollable);\n    }\n  }\n  /**\n   * Returns an observable that emits an event whenever any of the registered Scrollable\n   * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n   * to override the default \"throttle\" time.\n   *\n   * **Note:** in order to avoid hitting change detection for every scroll event,\n   * all of the events emitted from this stream will be run outside the Angular zone.\n   * If you need to update any data bindings as a result of a scroll event, you have\n   * to run the callback using `NgZone.run`.\n   */\n  scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n    if (!this._platform.isBrowser) {\n      return of();\n    }\n    return new Observable(observer => {\n      if (!this._cleanupGlobalListener) {\n        this._cleanupGlobalListener = this._ngZone.runOutsideAngular(() => this._renderer.listen('document', 'scroll', () => this._scrolled.next()));\n      }\n      // In the case of a 0ms delay, use an observable without auditTime\n      // since it does add a perceptible delay in processing overhead.\n      const subscription = auditTimeInMs > 0 ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer) : this._scrolled.subscribe(observer);\n      this._scrolledCount++;\n      return () => {\n        subscription.unsubscribe();\n        this._scrolledCount--;\n        if (!this._scrolledCount) {\n          this._cleanupGlobalListener?.();\n          this._cleanupGlobalListener = undefined;\n        }\n      };\n    });\n  }\n  ngOnDestroy() {\n    this._cleanupGlobalListener?.();\n    this._cleanupGlobalListener = undefined;\n    this.scrollContainers.forEach((_, container) => this.deregister(container));\n    this._scrolled.complete();\n  }\n  /**\n   * Returns an observable that emits whenever any of the\n   * scrollable ancestors of an element are scrolled.\n   * @param elementOrElementRef Element whose ancestors to listen for.\n   * @param auditTimeInMs Time to throttle the scroll events.\n   */\n  ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n    const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n    return this.scrolled(auditTimeInMs).pipe(filter(target => !target || ancestors.indexOf(target) > -1));\n  }\n  /** Returns all registered Scrollables that contain the provided element. */\n  getAncestorScrollContainers(elementOrElementRef) {\n    const scrollingContainers = [];\n    this.scrollContainers.forEach((_subscription, scrollable) => {\n      if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n        scrollingContainers.push(scrollable);\n      }\n    });\n    return scrollingContainers;\n  }\n  /** Returns true if the element is contained within the provided Scrollable. */\n  _scrollableContainsElement(scrollable, elementOrElementRef) {\n    let element = coerceElement(elementOrElementRef);\n    let scrollableElement = scrollable.getElementRef().nativeElement;\n    // Traverse through the element parents until we reach null, checking if any of the elements\n    // are the scrollable's element.\n    do {\n      if (element == scrollableElement) {\n        return true;\n      }\n    } while (element = element.parentElement);\n    return false;\n  }\n  static ɵfac = function ScrollDispatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ScrollDispatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ScrollDispatcher,\n    factory: ScrollDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n  elementRef = inject(ElementRef);\n  scrollDispatcher = inject(ScrollDispatcher);\n  ngZone = inject(NgZone);\n  dir = inject(Directionality, {\n    optional: true\n  });\n  _scrollElement = this.elementRef.nativeElement;\n  _destroyed = new Subject();\n  _renderer = inject(Renderer2);\n  _cleanupScroll;\n  _elementScrolled = new Subject();\n  constructor() {}\n  ngOnInit() {\n    this._cleanupScroll = this.ngZone.runOutsideAngular(() => this._renderer.listen(this._scrollElement, 'scroll', event => this._elementScrolled.next(event)));\n    this.scrollDispatcher.register(this);\n  }\n  ngOnDestroy() {\n    this._cleanupScroll?.();\n    this._elementScrolled.complete();\n    this.scrollDispatcher.deregister(this);\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Returns observable that emits when a scroll event is fired on the host element. */\n  elementScrolled() {\n    return this._elementScrolled;\n  }\n  /** Gets the ElementRef for the viewport. */\n  getElementRef() {\n    return this.elementRef;\n  }\n  /**\n   * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n   * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param options specified the offsets to scroll to.\n   */\n  scrollTo(options) {\n    const el = this.elementRef.nativeElement;\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    // Rewrite start & end offsets as right or left offsets.\n    if (options.left == null) {\n      options.left = isRtl ? options.end : options.start;\n    }\n    if (options.right == null) {\n      options.right = isRtl ? options.start : options.end;\n    }\n    // Rewrite the bottom offset as a top offset.\n    if (options.bottom != null) {\n      options.top = el.scrollHeight - el.clientHeight - options.bottom;\n    }\n    // Rewrite the right offset as a left offset.\n    if (isRtl && getRtlScrollAxisType() != RtlScrollAxisType.NORMAL) {\n      if (options.left != null) {\n        options.right = el.scrollWidth - el.clientWidth - options.left;\n      }\n      if (getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n        options.left = options.right;\n      } else if (getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n        options.left = options.right ? -options.right : options.right;\n      }\n    } else {\n      if (options.right != null) {\n        options.left = el.scrollWidth - el.clientWidth - options.right;\n      }\n    }\n    this._applyScrollToOptions(options);\n  }\n  _applyScrollToOptions(options) {\n    const el = this.elementRef.nativeElement;\n    if (supportsScrollBehavior()) {\n      el.scrollTo(options);\n    } else {\n      if (options.top != null) {\n        el.scrollTop = options.top;\n      }\n      if (options.left != null) {\n        el.scrollLeft = options.left;\n      }\n    }\n  }\n  /**\n   * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n   * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n   * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param from The edge to measure from.\n   */\n  measureScrollOffset(from) {\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const el = this.elementRef.nativeElement;\n    if (from == 'top') {\n      return el.scrollTop;\n    }\n    if (from == 'bottom') {\n      return el.scrollHeight - el.clientHeight - el.scrollTop;\n    }\n    // Rewrite start & end as left or right offsets.\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    if (from == 'start') {\n      from = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      from = isRtl ? LEFT : RIGHT;\n    }\n    if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n      // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      } else {\n        return el.scrollLeft;\n      }\n    } else if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n      // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft + el.scrollWidth - el.clientWidth;\n      } else {\n        return -el.scrollLeft;\n      }\n    } else {\n      // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n      // (scrollWidth - clientWidth) when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft;\n      } else {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      }\n    }\n  }\n  static ɵfac = function CdkScrollable_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkScrollable)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkScrollable,\n    selectors: [[\"\", \"cdk-scrollable\", \"\"], [\"\", \"cdkScrollable\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollable, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-scrollable], [cdkScrollable]'\n    }]\n  }], () => [], null);\n})();\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n  _platform = inject(Platform);\n  _listeners;\n  /** Cached viewport dimensions. */\n  _viewportSize;\n  /** Stream of viewport change events. */\n  _change = new Subject();\n  /** Used to reference correct document/window */\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  constructor() {\n    const ngZone = inject(NgZone);\n    const renderer = inject(RendererFactory2).createRenderer(null, null);\n    ngZone.runOutsideAngular(() => {\n      if (this._platform.isBrowser) {\n        const changeListener = event => this._change.next(event);\n        this._listeners = [renderer.listen('window', 'resize', changeListener), renderer.listen('window', 'orientationchange', changeListener)];\n      }\n      // Clear the cached position so that the viewport is re-measured next time it is required.\n      // We don't need to keep track of the subscription, because it is completed on destroy.\n      this.change().subscribe(() => this._viewportSize = null);\n    });\n  }\n  ngOnDestroy() {\n    this._listeners?.forEach(cleanup => cleanup());\n    this._change.complete();\n  }\n  /** Returns the viewport's width and height. */\n  getViewportSize() {\n    if (!this._viewportSize) {\n      this._updateViewportSize();\n    }\n    const output = {\n      width: this._viewportSize.width,\n      height: this._viewportSize.height\n    };\n    // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n    if (!this._platform.isBrowser) {\n      this._viewportSize = null;\n    }\n    return output;\n  }\n  /** Gets a DOMRect for the viewport's bounds. */\n  getViewportRect() {\n    // Use the document element's bounding rect rather than the window scroll properties\n    // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n    // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n    // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n    // can disagree when the page is pinch-zoomed (on devices that support touch).\n    // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n    // We use the documentElement instead of the body because, by default (without a css reset)\n    // browsers typically give the document body an 8px margin, which is not included in\n    // getBoundingClientRect().\n    const scrollPosition = this.getViewportScrollPosition();\n    const {\n      width,\n      height\n    } = this.getViewportSize();\n    return {\n      top: scrollPosition.top,\n      left: scrollPosition.left,\n      bottom: scrollPosition.top + height,\n      right: scrollPosition.left + width,\n      height,\n      width\n    };\n  }\n  /** Gets the (top, left) scroll position of the viewport. */\n  getViewportScrollPosition() {\n    // While we can get a reference to the fake document\n    // during SSR, it doesn't have getBoundingClientRect.\n    if (!this._platform.isBrowser) {\n      return {\n        top: 0,\n        left: 0\n      };\n    }\n    // The top-left-corner of the viewport is determined by the scroll position of the document\n    // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n    // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n    // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n    // `document.documentElement` works consistently, where the `top` and `left` values will\n    // equal negative the scroll position.\n    const document = this._document;\n    const window = this._getWindow();\n    const documentElement = document.documentElement;\n    const documentRect = documentElement.getBoundingClientRect();\n    const top = -documentRect.top || document.body.scrollTop || window.scrollY || documentElement.scrollTop || 0;\n    const left = -documentRect.left || document.body.scrollLeft || window.scrollX || documentElement.scrollLeft || 0;\n    return {\n      top,\n      left\n    };\n  }\n  /**\n   * Returns a stream that emits whenever the size of the viewport changes.\n   * This stream emits outside of the Angular zone.\n   * @param throttleTime Time in milliseconds to throttle the stream.\n   */\n  change(throttleTime = DEFAULT_RESIZE_TIME) {\n    return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n  /** Updates the cached viewport size. */\n  _updateViewportSize() {\n    const window = this._getWindow();\n    this._viewportSize = this._platform.isBrowser ? {\n      width: window.innerWidth,\n      height: window.innerHeight\n    } : {\n      width: 0,\n      height: 0\n    };\n  }\n  static ɵfac = function ViewportRuler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ViewportRuler)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ViewportRuler,\n    factory: ViewportRuler.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ViewportRuler, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nconst VIRTUAL_SCROLLABLE = new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the {@link CdkScrollable} to be used as scrolling container for virtual scrolling.\n */\nclass CdkVirtualScrollable extends CdkScrollable {\n  constructor() {\n    super();\n  }\n  /**\n   * Measure the viewport size for the provided orientation.\n   *\n   * @param orientation The orientation to measure the size from.\n   */\n  measureViewportSize(orientation) {\n    const viewportEl = this.elementRef.nativeElement;\n    return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n  }\n  static ɵfac = function CdkVirtualScrollable_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkVirtualScrollable)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkVirtualScrollable,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollable, [{\n    type: Directive\n  }], () => [], null);\n})();\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n  return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkVirtualScrollable {\n  elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _scrollStrategy = inject(VIRTUAL_SCROLL_STRATEGY, {\n    optional: true\n  });\n  scrollable = inject(VIRTUAL_SCROLLABLE, {\n    optional: true\n  });\n  _platform = inject(Platform);\n  /** Emits when the viewport is detached from a CdkVirtualForOf. */\n  _detachedSubject = new Subject();\n  /** Emits when the rendered range changes. */\n  _renderedRangeSubject = new Subject();\n  /** The direction the viewport scrolls. */\n  get orientation() {\n    return this._orientation;\n  }\n  set orientation(orientation) {\n    if (this._orientation !== orientation) {\n      this._orientation = orientation;\n      this._calculateSpacerSize();\n    }\n  }\n  _orientation = 'vertical';\n  /**\n   * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n   * will be removed.\n   */\n  appendOnly = false;\n  // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n  // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n  // depending on how the strategy calculates the scrolled index, it may come at a cost to\n  // performance.\n  /** Emits when the index of the first element visible in the viewport changes. */\n  scrolledIndexChange = new Observable(observer => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n  /** The element that wraps the rendered content. */\n  _contentWrapper;\n  /** A stream that emits whenever the rendered range changes. */\n  renderedRangeStream = this._renderedRangeSubject;\n  /**\n   * The total size of all content (in pixels), including content that is not currently rendered.\n   */\n  _totalContentSize = 0;\n  /** A string representing the `style.width` property value to be used for the spacer element. */\n  _totalContentWidth = signal('');\n  /** A string representing the `style.height` property value to be used for the spacer element. */\n  _totalContentHeight = signal('');\n  /**\n   * The CSS transform applied to the rendered subset of items so that they appear within the bounds\n   * of the visible viewport.\n   */\n  _renderedContentTransform;\n  /** The currently rendered range of indices. */\n  _renderedRange = {\n    start: 0,\n    end: 0\n  };\n  /** The length of the data bound to this viewport (in number of items). */\n  _dataLength = 0;\n  /** The size of the viewport (in pixels). */\n  _viewportSize = 0;\n  /** the currently attached CdkVirtualScrollRepeater. */\n  _forOf;\n  /** The last rendered content offset that was set. */\n  _renderedContentOffset = 0;\n  /**\n   * Whether the last rendered content offset was to the end of the content (and therefore needs to\n   * be rewritten as an offset to the start of the content).\n   */\n  _renderedContentOffsetNeedsRewrite = false;\n  /** Whether there is a pending change detection cycle. */\n  _isChangeDetectionPending = false;\n  /** A list of functions to run after the next change detection cycle. */\n  _runAfterChangeDetection = [];\n  /** Subscription to changes in the viewport size. */\n  _viewportChanges = Subscription.EMPTY;\n  _injector = inject(Injector);\n  _isDestroyed = false;\n  constructor() {\n    super();\n    const viewportRuler = inject(ViewportRuler);\n    if (!this._scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n    }\n    this._viewportChanges = viewportRuler.change().subscribe(() => {\n      this.checkViewportSize();\n    });\n    if (!this.scrollable) {\n      // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n      this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n      this.scrollable = this;\n    }\n  }\n  ngOnInit() {\n    // Scrolling depends on the element dimensions which we can't get during SSR.\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    if (this.scrollable === this) {\n      super.ngOnInit();\n    }\n    // It's still too early to measure the viewport at this point. Deferring with a promise allows\n    // the Viewport to be rendered with the correct size before we measure. We run this outside the\n    // zone to avoid causing more change detection cycles. We handle the change detection loop\n    // ourselves instead.\n    this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n      this._measureViewportSize();\n      this._scrollStrategy.attach(this);\n      this.scrollable.elementScrolled().pipe(\n      // Start off with a fake scroll event so we properly detect our initial position.\n      startWith(null),\n      // Collect multiple events into one until the next animation frame. This way if\n      // there are multiple scroll events in the same frame we only need to recheck\n      // our layout once.\n      auditTime(0, SCROLL_SCHEDULER),\n      // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n      // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n      // to unsubscribe here just in case.\n      takeUntil(this._destroyed)).subscribe(() => this._scrollStrategy.onContentScrolled());\n      this._markChangeDetectionNeeded();\n    }));\n  }\n  ngOnDestroy() {\n    this.detach();\n    this._scrollStrategy.detach();\n    // Complete all subjects\n    this._renderedRangeSubject.complete();\n    this._detachedSubject.complete();\n    this._viewportChanges.unsubscribe();\n    this._isDestroyed = true;\n    super.ngOnDestroy();\n  }\n  /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n  attach(forOf) {\n    if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CdkVirtualScrollViewport is already attached.');\n    }\n    // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n    // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n    // change detection loop ourselves.\n    this.ngZone.runOutsideAngular(() => {\n      this._forOf = forOf;\n      this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n        const newLength = data.length;\n        if (newLength !== this._dataLength) {\n          this._dataLength = newLength;\n          this._scrollStrategy.onDataLengthChanged();\n        }\n        this._doChangeDetection();\n      });\n    });\n  }\n  /** Detaches the current `CdkVirtualForOf`. */\n  detach() {\n    this._forOf = null;\n    this._detachedSubject.next();\n  }\n  /** Gets the length of the data bound to this viewport (in number of items). */\n  getDataLength() {\n    return this._dataLength;\n  }\n  /** Gets the size of the viewport (in pixels). */\n  getViewportSize() {\n    return this._viewportSize;\n  }\n  // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n  // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n  // setting it to something else, but its error prone and should probably be split into\n  // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n  /** Get the current rendered range of items. */\n  getRenderedRange() {\n    return this._renderedRange;\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n  }\n  /**\n   * Sets the total size of all content (in pixels), including content that is not currently\n   * rendered.\n   */\n  setTotalContentSize(size) {\n    if (this._totalContentSize !== size) {\n      this._totalContentSize = size;\n      this._calculateSpacerSize();\n      this._markChangeDetectionNeeded();\n    }\n  }\n  /** Sets the currently rendered range of indices. */\n  setRenderedRange(range) {\n    if (!rangesEqual(this._renderedRange, range)) {\n      if (this.appendOnly) {\n        range = {\n          start: 0,\n          end: Math.max(this._renderedRange.end, range.end)\n        };\n      }\n      this._renderedRangeSubject.next(this._renderedRange = range);\n      this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n    }\n  }\n  /**\n   * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n   */\n  getOffsetToRenderedContentStart() {\n    return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n  }\n  /**\n   * Sets the offset from the start of the viewport to either the start or end of the rendered data\n   * (in pixels).\n   */\n  setRenderedContentOffset(offset, to = 'to-start') {\n    // In appendOnly, we always start from the top\n    offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n    // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n    // in the negative direction.\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    const isHorizontal = this.orientation == 'horizontal';\n    const axis = isHorizontal ? 'X' : 'Y';\n    const axisDirection = isHorizontal && isRtl ? -1 : 1;\n    let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n    this._renderedContentOffset = offset;\n    if (to === 'to-end') {\n      transform += ` translate${axis}(-100%)`;\n      // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n      // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n      // expand upward).\n      this._renderedContentOffsetNeedsRewrite = true;\n    }\n    if (this._renderedContentTransform != transform) {\n      // We know this value is safe because we parse `offset` with `Number()` before passing it\n      // into the string.\n      this._renderedContentTransform = transform;\n      this._markChangeDetectionNeeded(() => {\n        if (this._renderedContentOffsetNeedsRewrite) {\n          this._renderedContentOffset -= this.measureRenderedContentSize();\n          this._renderedContentOffsetNeedsRewrite = false;\n          this.setRenderedContentOffset(this._renderedContentOffset);\n        } else {\n          this._scrollStrategy.onRenderedOffsetChanged();\n        }\n      });\n    }\n  }\n  /**\n   * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n   * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n   * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n   * @param offset The offset to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n  scrollToOffset(offset, behavior = 'auto') {\n    const options = {\n      behavior\n    };\n    if (this.orientation === 'horizontal') {\n      options.start = offset;\n    } else {\n      options.top = offset;\n    }\n    this.scrollable.scrollTo(options);\n  }\n  /**\n   * Scrolls to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n  scrollToIndex(index, behavior = 'auto') {\n    this._scrollStrategy.scrollToIndex(index, behavior);\n  }\n  /**\n   * Gets the current scroll offset from the start of the scrollable (in pixels).\n   * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n   *     in horizontal mode.\n   */\n  measureScrollOffset(from) {\n    // This is to break the call cycle\n    let measureScrollOffset;\n    if (this.scrollable == this) {\n      measureScrollOffset = _from => super.measureScrollOffset(_from);\n    } else {\n      measureScrollOffset = _from => this.scrollable.measureScrollOffset(_from);\n    }\n    return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) - this.measureViewportOffset());\n  }\n  /**\n   * Measures the offset of the viewport from the scrolling container\n   * @param from The edge to measure from.\n   */\n  measureViewportOffset(from) {\n    let fromRect;\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const isRtl = this.dir?.value == 'rtl';\n    if (from == 'start') {\n      fromRect = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      fromRect = isRtl ? LEFT : RIGHT;\n    } else if (from) {\n      fromRect = from;\n    } else {\n      fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n    }\n    const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n    const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n    return viewportClientRect - scrollerClientRect;\n  }\n  /** Measure the combined size of all of the rendered items. */\n  measureRenderedContentSize() {\n    const contentEl = this._contentWrapper.nativeElement;\n    return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n  }\n  /**\n   * Measure the total combined size of the given range. Throws if the range includes items that are\n   * not rendered.\n   */\n  measureRangeSize(range) {\n    if (!this._forOf) {\n      return 0;\n    }\n    return this._forOf.measureRangeSize(range, this.orientation);\n  }\n  /** Update the viewport dimensions and re-render. */\n  checkViewportSize() {\n    // TODO: Cleanup later when add logic for handling content resize\n    this._measureViewportSize();\n    this._scrollStrategy.onDataLengthChanged();\n  }\n  /** Measure the viewport size. */\n  _measureViewportSize() {\n    this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n  }\n  /** Queue up change detection to run. */\n  _markChangeDetectionNeeded(runAfter) {\n    if (runAfter) {\n      this._runAfterChangeDetection.push(runAfter);\n    }\n    // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n    // properties sequentially we only have to run `_doChangeDetection` once at the end.\n    if (!this._isChangeDetectionPending) {\n      this._isChangeDetectionPending = true;\n      this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n        this._doChangeDetection();\n      }));\n    }\n  }\n  /** Run change detection. */\n  _doChangeDetection() {\n    if (this._isDestroyed) {\n      return;\n    }\n    this.ngZone.run(() => {\n      // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n      // from the root, since the repeated items are content projected in. Calling `detectChanges`\n      // instead does not properly check the projected content.\n      this._changeDetectorRef.markForCheck();\n      // Apply the content transform. The transform can't be set via an Angular binding because\n      // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n      // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n      // the `Number` function first to coerce it to a numeric value.\n      this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n      afterNextRender(() => {\n        this._isChangeDetectionPending = false;\n        const runAfterChangeDetection = this._runAfterChangeDetection;\n        this._runAfterChangeDetection = [];\n        for (const fn of runAfterChangeDetection) {\n          fn();\n        }\n      }, {\n        injector: this._injector\n      });\n    });\n  }\n  /** Calculates the `style.width` and `style.height` for the spacer element. */\n  _calculateSpacerSize() {\n    this._totalContentHeight.set(this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`);\n    this._totalContentWidth.set(this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '');\n  }\n  static ɵfac = function CdkVirtualScrollViewport_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkVirtualScrollViewport)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkVirtualScrollViewport,\n    selectors: [[\"cdk-virtual-scroll-viewport\"]],\n    viewQuery: function CdkVirtualScrollViewport_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentWrapper = _t.first);\n      }\n    },\n    hostAttrs: [1, \"cdk-virtual-scroll-viewport\"],\n    hostVars: 4,\n    hostBindings: function CdkVirtualScrollViewport_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"cdk-virtual-scroll-orientation-horizontal\", ctx.orientation === \"horizontal\")(\"cdk-virtual-scroll-orientation-vertical\", ctx.orientation !== \"horizontal\");\n      }\n    },\n    inputs: {\n      orientation: \"orientation\",\n      appendOnly: [2, \"appendOnly\", \"appendOnly\", booleanAttribute]\n    },\n    outputs: {\n      scrolledIndexChange: \"scrolledIndexChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkScrollable,\n      useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n      deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport]\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 4,\n    consts: [[\"contentWrapper\", \"\"], [1, \"cdk-virtual-scroll-content-wrapper\"], [1, \"cdk-virtual-scroll-spacer\"]],\n    template: function CdkVirtualScrollViewport_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(3, \"div\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵstyleProp(\"width\", ctx._totalContentWidth())(\"height\", ctx._totalContentHeight());\n      }\n    },\n    styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollViewport, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport',\n      host: {\n        'class': 'cdk-virtual-scroll-viewport',\n        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: CdkScrollable,\n        useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n        deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport]\n      }],\n      template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth()\\\" [style.height]=\\\"_totalContentHeight()\\\"></div>\\n\",\n      styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\\n\"]\n    }]\n  }], () => [], {\n    orientation: [{\n      type: Input\n    }],\n    appendOnly: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    scrolledIndexChange: [{\n      type: Output\n    }],\n    _contentWrapper: [{\n      type: ViewChild,\n      args: ['contentWrapper', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n  const el = node;\n  if (!el.getBoundingClientRect) {\n    return 0;\n  }\n  const rect = el.getBoundingClientRect();\n  if (orientation === 'horizontal') {\n    return direction === 'start' ? rect.left : rect.right;\n  }\n  return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n  _viewContainerRef = inject(ViewContainerRef);\n  _template = inject(TemplateRef);\n  _differs = inject(IterableDiffers);\n  _viewRepeater = inject(_VIEW_REPEATER_STRATEGY);\n  _viewport = inject(CdkVirtualScrollViewport, {\n    skipSelf: true\n  });\n  /** Emits when the rendered view of the data changes. */\n  viewChange = new Subject();\n  /** Subject that emits when a new DataSource instance is given. */\n  _dataSourceChanges = new Subject();\n  /** The DataSource to display. */\n  get cdkVirtualForOf() {\n    return this._cdkVirtualForOf;\n  }\n  set cdkVirtualForOf(value) {\n    this._cdkVirtualForOf = value;\n    if (isDataSource(value)) {\n      this._dataSourceChanges.next(value);\n    } else {\n      // If value is an an NgIterable, convert it to an array.\n      this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n    }\n  }\n  _cdkVirtualForOf;\n  /**\n   * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n   * the item and produces a value to be used as the item's identity when tracking changes.\n   */\n  get cdkVirtualForTrackBy() {\n    return this._cdkVirtualForTrackBy;\n  }\n  set cdkVirtualForTrackBy(fn) {\n    this._needsUpdate = true;\n    this._cdkVirtualForTrackBy = fn ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item) : undefined;\n  }\n  _cdkVirtualForTrackBy;\n  /** The template used to stamp out new elements. */\n  set cdkVirtualForTemplate(value) {\n    if (value) {\n      this._needsUpdate = true;\n      this._template = value;\n    }\n  }\n  /**\n   * The size of the cache used to store templates that are not being used for re-use later.\n   * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n   */\n  get cdkVirtualForTemplateCacheSize() {\n    return this._viewRepeater.viewCacheSize;\n  }\n  set cdkVirtualForTemplateCacheSize(size) {\n    this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n  }\n  /** Emits whenever the data in the current DataSource changes. */\n  dataStream = this._dataSourceChanges.pipe(\n  // Start off with null `DataSource`.\n  startWith(null),\n  // Bundle up the previous and current data sources so we can work with both.\n  pairwise(),\n  // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n  // new one, passing back a stream of data changes which we run through `switchMap` to give\n  // us a data stream that emits the latest data from whatever the current `DataSource` is.\n  switchMap(([prev, cur]) => this._changeDataSource(prev, cur)),\n  // Replay the last emitted data when someone subscribes.\n  shareReplay(1));\n  /** The differ used to calculate changes to the data. */\n  _differ = null;\n  /** The most recent data emitted from the DataSource. */\n  _data;\n  /** The currently rendered items. */\n  _renderedItems;\n  /** The currently rendered range of indices. */\n  _renderedRange;\n  /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n  _needsUpdate = false;\n  _destroyed = new Subject();\n  constructor() {\n    const ngZone = inject(NgZone);\n    this.dataStream.subscribe(data => {\n      this._data = data;\n      this._onRenderedDataChange();\n    });\n    this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n      this._renderedRange = range;\n      if (this.viewChange.observers.length) {\n        ngZone.run(() => this.viewChange.next(this._renderedRange));\n      }\n      this._onRenderedDataChange();\n    });\n    this._viewport.attach(this);\n  }\n  /**\n   * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n   * in the specified range. Throws an error if the range includes items that are not currently\n   * rendered.\n   */\n  measureRangeSize(range, orientation) {\n    if (range.start >= range.end) {\n      return 0;\n    }\n    if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Error: attempted to measure an item that isn't rendered.`);\n    }\n    // The index into the list of rendered views for the first item in the range.\n    const renderedStartIndex = range.start - this._renderedRange.start;\n    // The length of the range we're measuring.\n    const rangeLen = range.end - range.start;\n    // Loop over all the views, find the first and land node and compute the size by subtracting\n    // the top of the first node from the bottom of the last one.\n    let firstNode;\n    let lastNode;\n    // Find the first node by starting from the beginning and going forwards.\n    for (let i = 0; i < rangeLen; i++) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n      if (view && view.rootNodes.length) {\n        firstNode = lastNode = view.rootNodes[0];\n        break;\n      }\n    }\n    // Find the last node by starting from the end and going backwards.\n    for (let i = rangeLen - 1; i > -1; i--) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n      if (view && view.rootNodes.length) {\n        lastNode = view.rootNodes[view.rootNodes.length - 1];\n        break;\n      }\n    }\n    return firstNode && lastNode ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode) : 0;\n  }\n  ngDoCheck() {\n    if (this._differ && this._needsUpdate) {\n      // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n      // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n      // changing (need to do this diff).\n      const changes = this._differ.diff(this._renderedItems);\n      if (!changes) {\n        this._updateContext();\n      } else {\n        this._applyChanges(changes);\n      }\n      this._needsUpdate = false;\n    }\n  }\n  ngOnDestroy() {\n    this._viewport.detach();\n    this._dataSourceChanges.next(undefined);\n    this._dataSourceChanges.complete();\n    this.viewChange.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._viewRepeater.detach();\n  }\n  /** React to scroll state changes in the viewport. */\n  _onRenderedDataChange() {\n    if (!this._renderedRange) {\n      return;\n    }\n    this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n    if (!this._differ) {\n      // Use a wrapper function for the `trackBy` so any new values are\n      // picked up automatically without having to recreate the differ.\n      this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n        return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n      });\n    }\n    this._needsUpdate = true;\n  }\n  /** Swap out one `DataSource` for another. */\n  _changeDataSource(oldDs, newDs) {\n    if (oldDs) {\n      oldDs.disconnect(this);\n    }\n    this._needsUpdate = true;\n    return newDs ? newDs.connect(this) : of();\n  }\n  /** Update the `CdkVirtualForOfContext` for all views. */\n  _updateContext() {\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n      this._updateComputedContextProperties(view.context);\n      view.detectChanges();\n    }\n  }\n  /** Apply changes to the DOM. */\n  _applyChanges(changes) {\n    this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n    // Update $implicit for any items that had an identity change.\n    changes.forEachIdentityChange(record => {\n      const view = this._viewContainerRef.get(record.currentIndex);\n      view.context.$implicit = record.item;\n    });\n    // Update the context variables on all items.\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n      this._updateComputedContextProperties(view.context);\n    }\n  }\n  /** Update the computed properties on the `CdkVirtualForOfContext`. */\n  _updateComputedContextProperties(context) {\n    context.first = context.index === 0;\n    context.last = context.index === context.count - 1;\n    context.even = context.index % 2 === 0;\n    context.odd = !context.even;\n  }\n  _getEmbeddedViewArgs(record, index) {\n    // Note that it's important that we insert the item directly at the proper index,\n    // rather than inserting it and the moving it in place, because if there's a directive\n    // on the same node that injects the `ViewContainerRef`, Angular will insert another\n    // comment node which can throw off the move when it's being repeated for all items.\n    return {\n      templateRef: this._template,\n      context: {\n        $implicit: record.item,\n        // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n        // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n        cdkVirtualForOf: this._cdkVirtualForOf,\n        index: -1,\n        count: -1,\n        first: false,\n        last: false,\n        odd: false,\n        even: false\n      },\n      index\n    };\n  }\n  static ngTemplateContextGuard(directive, context) {\n    return true;\n  }\n  static ɵfac = function CdkVirtualForOf_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkVirtualForOf)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkVirtualForOf,\n    selectors: [[\"\", \"cdkVirtualFor\", \"\", \"cdkVirtualForOf\", \"\"]],\n    inputs: {\n      cdkVirtualForOf: \"cdkVirtualForOf\",\n      cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\",\n      cdkVirtualForTemplate: \"cdkVirtualForTemplate\",\n      cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: _VIEW_REPEATER_STRATEGY,\n      useClass: _RecycleViewRepeaterStrategy\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualForOf, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkVirtualFor][cdkVirtualForOf]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], () => [], {\n    cdkVirtualForOf: [{\n      type: Input\n    }],\n    cdkVirtualForTrackBy: [{\n      type: Input\n    }],\n    cdkVirtualForTemplate: [{\n      type: Input\n    }],\n    cdkVirtualForTemplateCacheSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nclass CdkVirtualScrollableElement extends CdkVirtualScrollable {\n  constructor() {\n    super();\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from] - this.measureScrollOffset(from);\n  }\n  static ɵfac = function CdkVirtualScrollableElement_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkVirtualScrollableElement)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkVirtualScrollableElement,\n    selectors: [[\"\", \"cdkVirtualScrollingElement\", \"\"]],\n    hostAttrs: [1, \"cdk-virtual-scrollable\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: VIRTUAL_SCROLLABLE,\n      useExisting: CdkVirtualScrollableElement\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollableElement, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkVirtualScrollingElement]',\n      providers: [{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableElement\n      }],\n      host: {\n        'class': 'cdk-virtual-scrollable'\n      }\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nclass CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n  constructor() {\n    super();\n    const document = inject(DOCUMENT);\n    this.elementRef = new ElementRef(document.documentElement);\n    this._scrollElement = document;\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n  }\n  static ɵfac = function CdkVirtualScrollableWindow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkVirtualScrollableWindow)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkVirtualScrollableWindow,\n    selectors: [[\"cdk-virtual-scroll-viewport\", \"scrollWindow\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: VIRTUAL_SCROLLABLE,\n      useExisting: CdkVirtualScrollableWindow\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollableWindow, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n      providers: [{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableWindow\n      }]\n    }]\n  }], () => [], null);\n})();\nclass CdkScrollableModule {\n  static ɵfac = function CdkScrollableModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkScrollableModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CdkScrollableModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollableModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkScrollable],\n      imports: [CdkScrollable]\n    }]\n  }], null, null);\n})();\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {\n  static ɵfac = function ScrollingModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ScrollingModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ScrollingModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [BidiModule, CdkScrollableModule, BidiModule, CdkScrollableModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollingModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollableWindow, CdkVirtualScrollableElement],\n      exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollableWindow, CdkVirtualScrollableElement]\n    }]\n  }], null, null);\n})();\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };", "map": {"version": 3, "names": ["i0", "InjectionToken", "forwardRef", "Directive", "Input", "inject", "NgZone", "RendererFactory2", "Injectable", "ElementRef", "Renderer2", "DOCUMENT", "ChangeDetectorRef", "signal", "Injector", "afterNextRender", "booleanAttribute", "Optional", "Inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Output", "ViewChild", "ViewContainerRef", "TemplateRef", "Iterable<PERSON><PERSON><PERSON>", "NgModule", "Subject", "of", "Observable", "Subscription", "animationFrameScheduler", "asapScheduler", "isObservable", "distinctUntilChanged", "auditTime", "filter", "startWith", "takeUntil", "pairwise", "switchMap", "shareReplay", "c", "coerceNumberProperty", "a", "coerceElement", "P", "Platform", "D", "Directionality", "g", "getRtlScrollAxisType", "R", "RtlScrollAxisType", "s", "supportsScrollBehavior", "BidiModule", "_c0", "_c1", "<PERSON><PERSON>", "ɵɵDir", "b", "_VIEW_REPEATER_STRATEGY", "A", "ArrayDataSource", "_", "_RecycleViewRepeaterStrategy", "i", "isDataSource", "VIRTUAL_SCROLL_STRATEGY", "FixedSizeVirtualScrollStrategy", "_scrolledIndexChange", "scrolledIndexChange", "pipe", "_viewport", "_itemSize", "_minBufferPx", "_maxBufferPx", "constructor", "itemSize", "minBufferPx", "maxBufferPx", "attach", "viewport", "_updateTotalContentSize", "_updateRenderedRange", "detach", "complete", "updateItemAndBufferSize", "ngDevMode", "Error", "onContentScrolled", "onDataLengthChanged", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "onRenderedOffsetChanged", "scrollToIndex", "index", "behavior", "scrollToOffset", "setTotalContentSize", "getDataLength", "renderedRange", "getRenderedRange", "newRange", "start", "end", "viewportSize", "getViewportSize", "dataLength", "scrollOffset", "measureScrollOffset", "firstVisibleIndex", "maxVisibleItems", "Math", "ceil", "newVisibleIndex", "max", "min", "floor", "startBuffer", "expandStart", "end<PERSON><PERSON><PERSON>", "expandEnd", "setR<PERSON>edRange", "setRenderedContentOffset", "next", "_fixedSizeVirtualScrollStrategyFactory", "fixedSizeDir", "_scrollStrategy", "CdkFixedSizeVirtualScroll", "value", "ngOnChanges", "ɵfac", "CdkFixedSizeVirtualScroll_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "features", "ɵɵProvidersFeature", "provide", "useFactory", "deps", "ɵɵNgOnChangesFeature", "ɵsetClassMetadata", "args", "selector", "providers", "DEFAULT_SCROLL_TIME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ngZone", "_platform", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "_cleanupGlobalListener", "_scrolled", "_scrolledCount", "scrollContainers", "Map", "register", "scrollable", "has", "set", "elementScrolled", "subscribe", "deregister", "scrollableReference", "get", "unsubscribe", "delete", "scrolled", "auditTimeInMs", "<PERSON><PERSON><PERSON><PERSON>", "observer", "runOutsideAngular", "listen", "subscription", "undefined", "ngOnDestroy", "for<PERSON>ach", "container", "ancestorScrolled", "elementOrElementRef", "ancestors", "getAncestorScrollContainers", "target", "indexOf", "scrollingContainers", "_subscription", "_scrollableContainsElement", "push", "element", "scrollableElement", "getElementRef", "nativeElement", "parentElement", "ScrollDispatcher_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "CdkScrollable", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "ngZone", "dir", "optional", "_scrollElement", "_destroyed", "_cleanupScroll", "_elementScrolled", "ngOnInit", "event", "scrollTo", "options", "el", "isRtl", "left", "right", "bottom", "top", "scrollHeight", "clientHeight", "NORMAL", "scrollWidth", "clientWidth", "INVERTED", "NEGATED", "_applyScrollToOptions", "scrollTop", "scrollLeft", "from", "LEFT", "RIGHT", "CdkScrollable_Factory", "DEFAULT_RESIZE_TIME", "ViewportRuler", "_listeners", "_viewportSize", "_change", "_document", "renderer", "changeListener", "change", "cleanup", "_updateViewportSize", "output", "width", "height", "getViewportRect", "scrollPosition", "getViewportScrollPosition", "document", "window", "_getWindow", "documentElement", "documentRect", "getBoundingClientRect", "body", "scrollY", "scrollX", "throttleTime", "defaultView", "innerWidth", "innerHeight", "ViewportRuler_Factory", "VIRTUAL_SCROLLABLE", "CdkVirtualScrollable", "measureViewportSize", "orientation", "viewportEl", "CdkVirtualScrollable_Factory", "ɵɵInheritDefinitionFeature", "rangesEqual", "r1", "r2", "SCROLL_SCHEDULER", "requestAnimationFrame", "CdkVirtualScrollViewport", "_changeDetectorRef", "_detachedSubject", "_renderedRangeSubject", "_orientation", "_calculateSpacerSize", "appendOnly", "Promise", "resolve", "then", "run", "_contentWrapper", "renderedRangeStream", "_totalContentSize", "_totalContentWidth", "_totalContentHeight", "_renderedContentTransform", "_rendered<PERSON><PERSON>e", "_dataLength", "_forOf", "_renderedContentOffset", "_renderedContentOffsetNeedsRewrite", "_isChangeDetectionPending", "_runAfterChangeDetection", "_viewportChanges", "EMPTY", "_injector", "_isDestroyed", "viewportRuler", "checkViewportSize", "classList", "add", "_measureViewportSize", "_markChangeDetectionNeeded", "forOf", "dataStream", "data", "<PERSON><PERSON><PERSON><PERSON>", "length", "_doChangeDetection", "measureBoundingClientRectWithScrollOffset", "size", "range", "getOffsetToRenderedContentStart", "offset", "to", "isHorizontal", "axis", "axisDirection", "transform", "Number", "measureRenderedContentSize", "_from", "measureViewportOffset", "fromRect", "scrollerClientRect", "viewportClientRect", "contentEl", "offsetWidth", "offsetHeight", "measureRangeSize", "runAfter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "runAfterChangeDetection", "fn", "injector", "CdkVirtualScrollViewport_Factory", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "CdkVirtualScrollViewport_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "CdkVirtualScrollViewport_HostBindings", "ɵɵclassProp", "outputs", "virtualScrollable", "ngContentSelectors", "decls", "vars", "consts", "template", "CdkVirtualScrollViewport_Template", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵstyleProp", "styles", "encapsulation", "changeDetection", "host", "None", "OnPush", "static", "getOffset", "direction", "node", "rect", "CdkVirtualForOf", "_viewContainerRef", "_template", "_differs", "_view<PERSON><PERSON><PERSON>er", "skipSelf", "viewChange", "_dataSourceChanges", "cdkVirtualForOf", "_cdkVirtualForOf", "Array", "cdkVirtualForTrackBy", "_cdkVirtualForTrackBy", "_needsUpdate", "item", "cdkVirtualForTemplate", "cdkVirtualForTemplateCacheSize", "viewCacheSize", "prev", "cur", "_changeDataSource", "_differ", "_data", "_renderedItems", "_onRenderedDataChange", "observers", "renderedStartIndex", "rangeLen", "firstNode", "lastNode", "view", "rootNodes", "ngDoCheck", "changes", "diff", "_updateContext", "_applyChanges", "slice", "find", "create", "oldDs", "newDs", "disconnect", "connect", "count", "context", "_updateComputedContextProperties", "detectChanges", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "forEachIdentityChange", "$implicit", "last", "even", "odd", "templateRef", "ngTemplateContextGuard", "directive", "CdkVirtualForOf_Factory", "useClass", "CdkVirtualScrollableElement", "CdkVirtualScrollableElement_Factory", "useExisting", "CdkVirtualScrollableWindow", "CdkVirtualScrollableWindow_Factory", "CdkScrollableModule", "CdkScrollableModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "imports", "ScrollingModule", "ScrollingModule_Factory"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@angular/cdk/fesm2022/scrolling.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, inject, NgZone, RendererFactory2, Injectable, ElementRef, Renderer2, DOCUMENT, ChangeDetectorRef, signal, Injector, afterNextRender, booleanAttribute, Optional, Inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, ViewContainerRef, TemplateRef, IterableDiffers, NgModule } from '@angular/core';\nimport { Subject, of, Observable, Subscription, animationFrameScheduler, asapScheduler, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, startWith, takeUntil, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport { g as getRtlScrollAxisType, R as RtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { BidiModule } from './bidi.mjs';\nexport { Dir as ɵɵDir } from './bidi.mjs';\nimport { b as _VIEW_REPEATER_STRATEGY, A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport { i as isDataSource } from './data-source-D34wiQZj.mjs';\nimport '@angular/common';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n    _scrolledIndexChange = new Subject();\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n    /** The attached viewport. */\n    _viewport = null;\n    /** The size of the items in the virtually scrolling list. */\n    _itemSize;\n    /** The minimum amount of buffer rendered beyond the viewport (in pixels). */\n    _minBufferPx;\n    /** The number of buffer items to render beyond the edge of the viewport (in pixels). */\n    _maxBufferPx;\n    /**\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    constructor(itemSize, minBufferPx, maxBufferPx) {\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n    }\n    /**\n     * Attaches this scroll strategy to a viewport.\n     * @param viewport The viewport to attach this strategy to.\n     */\n    attach(viewport) {\n        this._viewport = viewport;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** Detaches this scroll strategy from the currently attached viewport. */\n    detach() {\n        this._scrolledIndexChange.complete();\n        this._viewport = null;\n    }\n    /**\n     * Update the item size and buffer size.\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n        if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n        }\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentScrolled() {\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onDataLengthChanged() {\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentRendered() {\n        /* no-op */\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onRenderedOffsetChanged() {\n        /* no-op */\n    }\n    /**\n     * Scroll to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling.\n     */\n    scrollToIndex(index, behavior) {\n        if (this._viewport) {\n            this._viewport.scrollToOffset(index * this._itemSize, behavior);\n        }\n    }\n    /** Update the viewport's total content size. */\n    _updateTotalContentSize() {\n        if (!this._viewport) {\n            return;\n        }\n        this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n    }\n    /** Update the viewport's rendered range. */\n    _updateRenderedRange() {\n        if (!this._viewport) {\n            return;\n        }\n        const renderedRange = this._viewport.getRenderedRange();\n        const newRange = { start: renderedRange.start, end: renderedRange.end };\n        const viewportSize = this._viewport.getViewportSize();\n        const dataLength = this._viewport.getDataLength();\n        let scrollOffset = this._viewport.measureScrollOffset();\n        // Prevent NaN as result when dividing by zero.\n        let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n        // If user scrolls to the bottom of the list and data changes to a smaller list\n        if (newRange.end > dataLength) {\n            // We have to recalculate the first visible index based on new data length and viewport size.\n            const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n            const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n            // If first visible index changed we must update scroll offset to handle start/end buffers\n            // Current range must also be adjusted to cover the new position (bottom of new list).\n            if (firstVisibleIndex != newVisibleIndex) {\n                firstVisibleIndex = newVisibleIndex;\n                scrollOffset = newVisibleIndex * this._itemSize;\n                newRange.start = Math.floor(firstVisibleIndex);\n            }\n            newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n        }\n        const startBuffer = scrollOffset - newRange.start * this._itemSize;\n        if (startBuffer < this._minBufferPx && newRange.start != 0) {\n            const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n            newRange.start = Math.max(0, newRange.start - expandStart);\n            newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n        }\n        else {\n            const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n            if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n                const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n                if (expandEnd > 0) {\n                    newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n                    newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n                }\n            }\n        }\n        this._viewport.setRenderedRange(newRange);\n        this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n        this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n    }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n    return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n    /** The size of the items in the list (in pixels). */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(value) {\n        this._itemSize = coerceNumberProperty(value);\n    }\n    _itemSize = 20;\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n    get minBufferPx() {\n        return this._minBufferPx;\n    }\n    set minBufferPx(value) {\n        this._minBufferPx = coerceNumberProperty(value);\n    }\n    _minBufferPx = 100;\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n    get maxBufferPx() {\n        return this._maxBufferPx;\n    }\n    set maxBufferPx(value) {\n        this._maxBufferPx = coerceNumberProperty(value);\n    }\n    _maxBufferPx = 200;\n    /** The scroll strategy used by this directive. */\n    _scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    ngOnChanges() {\n        this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFixedSizeVirtualScroll, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkFixedSizeVirtualScroll, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[itemSize]\", inputs: { itemSize: \"itemSize\", minBufferPx: \"minBufferPx\", maxBufferPx: \"maxBufferPx\" }, providers: [\n            {\n                provide: VIRTUAL_SCROLL_STRATEGY,\n                useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n            },\n        ], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFixedSizeVirtualScroll, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[itemSize]',\n                    providers: [\n                        {\n                            provide: VIRTUAL_SCROLL_STRATEGY,\n                            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n                        },\n                    ],\n                }]\n        }], propDecorators: { itemSize: [{\n                type: Input\n            }], minBufferPx: [{\n                type: Input\n            }], maxBufferPx: [{\n                type: Input\n            }] } });\n\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n    _ngZone = inject(NgZone);\n    _platform = inject(Platform);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _cleanupGlobalListener;\n    constructor() { }\n    /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n    _scrolled = new Subject();\n    /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n    _scrolledCount = 0;\n    /**\n     * Map of all the scrollable references that are registered with the service and their\n     * scroll event subscriptions.\n     */\n    scrollContainers = new Map();\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n    register(scrollable) {\n        if (!this.scrollContainers.has(scrollable)) {\n            this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n        }\n    }\n    /**\n     * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n    deregister(scrollable) {\n        const scrollableReference = this.scrollContainers.get(scrollable);\n        if (scrollableReference) {\n            scrollableReference.unsubscribe();\n            this.scrollContainers.delete(scrollable);\n        }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n        if (!this._platform.isBrowser) {\n            return of();\n        }\n        return new Observable((observer) => {\n            if (!this._cleanupGlobalListener) {\n                this._cleanupGlobalListener = this._ngZone.runOutsideAngular(() => this._renderer.listen('document', 'scroll', () => this._scrolled.next()));\n            }\n            // In the case of a 0ms delay, use an observable without auditTime\n            // since it does add a perceptible delay in processing overhead.\n            const subscription = auditTimeInMs > 0\n                ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer)\n                : this._scrolled.subscribe(observer);\n            this._scrolledCount++;\n            return () => {\n                subscription.unsubscribe();\n                this._scrolledCount--;\n                if (!this._scrolledCount) {\n                    this._cleanupGlobalListener?.();\n                    this._cleanupGlobalListener = undefined;\n                }\n            };\n        });\n    }\n    ngOnDestroy() {\n        this._cleanupGlobalListener?.();\n        this._cleanupGlobalListener = undefined;\n        this.scrollContainers.forEach((_, container) => this.deregister(container));\n        this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n        const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n        return this.scrolled(auditTimeInMs).pipe(filter(target => !target || ancestors.indexOf(target) > -1));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n    getAncestorScrollContainers(elementOrElementRef) {\n        const scrollingContainers = [];\n        this.scrollContainers.forEach((_subscription, scrollable) => {\n            if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n                scrollingContainers.push(scrollable);\n            }\n        });\n        return scrollingContainers;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n        let element = coerceElement(elementOrElementRef);\n        let scrollableElement = scrollable.getElementRef().nativeElement;\n        // Traverse through the element parents until we reach null, checking if any of the elements\n        // are the scrollable's element.\n        do {\n            if (element == scrollableElement) {\n                return true;\n            }\n        } while ((element = element.parentElement));\n        return false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n    elementRef = inject(ElementRef);\n    scrollDispatcher = inject(ScrollDispatcher);\n    ngZone = inject(NgZone);\n    dir = inject(Directionality, { optional: true });\n    _scrollElement = this.elementRef.nativeElement;\n    _destroyed = new Subject();\n    _renderer = inject(Renderer2);\n    _cleanupScroll;\n    _elementScrolled = new Subject();\n    constructor() { }\n    ngOnInit() {\n        this._cleanupScroll = this.ngZone.runOutsideAngular(() => this._renderer.listen(this._scrollElement, 'scroll', event => this._elementScrolled.next(event)));\n        this.scrollDispatcher.register(this);\n    }\n    ngOnDestroy() {\n        this._cleanupScroll?.();\n        this._elementScrolled.complete();\n        this.scrollDispatcher.deregister(this);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n    elementScrolled() {\n        return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n    getElementRef() {\n        return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n    scrollTo(options) {\n        const el = this.elementRef.nativeElement;\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        // Rewrite start & end offsets as right or left offsets.\n        if (options.left == null) {\n            options.left = isRtl ? options.end : options.start;\n        }\n        if (options.right == null) {\n            options.right = isRtl ? options.start : options.end;\n        }\n        // Rewrite the bottom offset as a top offset.\n        if (options.bottom != null) {\n            options.top =\n                el.scrollHeight - el.clientHeight - options.bottom;\n        }\n        // Rewrite the right offset as a left offset.\n        if (isRtl && getRtlScrollAxisType() != RtlScrollAxisType.NORMAL) {\n            if (options.left != null) {\n                options.right =\n                    el.scrollWidth - el.clientWidth - options.left;\n            }\n            if (getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n                options.left = options.right;\n            }\n            else if (getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n                options.left = options.right ? -options.right : options.right;\n            }\n        }\n        else {\n            if (options.right != null) {\n                options.left =\n                    el.scrollWidth - el.clientWidth - options.right;\n            }\n        }\n        this._applyScrollToOptions(options);\n    }\n    _applyScrollToOptions(options) {\n        const el = this.elementRef.nativeElement;\n        if (supportsScrollBehavior()) {\n            el.scrollTo(options);\n        }\n        else {\n            if (options.top != null) {\n                el.scrollTop = options.top;\n            }\n            if (options.left != null) {\n                el.scrollLeft = options.left;\n            }\n        }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n    measureScrollOffset(from) {\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const el = this.elementRef.nativeElement;\n        if (from == 'top') {\n            return el.scrollTop;\n        }\n        if (from == 'bottom') {\n            return el.scrollHeight - el.clientHeight - el.scrollTop;\n        }\n        // Rewrite start & end as left or right offsets.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        if (from == 'start') {\n            from = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            from = isRtl ? LEFT : RIGHT;\n        }\n        if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n            // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n            else {\n                return el.scrollLeft;\n            }\n        }\n        else if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n            // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft + el.scrollWidth - el.clientWidth;\n            }\n            else {\n                return -el.scrollLeft;\n            }\n        }\n        else {\n            // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n            // (scrollWidth - clientWidth) when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft;\n            }\n            else {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollable, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkScrollable, isStandalone: true, selector: \"[cdk-scrollable], [cdkScrollable]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollable, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-scrollable], [cdkScrollable]',\n                }]\n        }], ctorParameters: () => [] });\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n    _platform = inject(Platform);\n    _listeners;\n    /** Cached viewport dimensions. */\n    _viewportSize;\n    /** Stream of viewport change events. */\n    _change = new Subject();\n    /** Used to reference correct document/window */\n    _document = inject(DOCUMENT, { optional: true });\n    constructor() {\n        const ngZone = inject(NgZone);\n        const renderer = inject(RendererFactory2).createRenderer(null, null);\n        ngZone.runOutsideAngular(() => {\n            if (this._platform.isBrowser) {\n                const changeListener = (event) => this._change.next(event);\n                this._listeners = [\n                    renderer.listen('window', 'resize', changeListener),\n                    renderer.listen('window', 'orientationchange', changeListener),\n                ];\n            }\n            // Clear the cached position so that the viewport is re-measured next time it is required.\n            // We don't need to keep track of the subscription, because it is completed on destroy.\n            this.change().subscribe(() => (this._viewportSize = null));\n        });\n    }\n    ngOnDestroy() {\n        this._listeners?.forEach(cleanup => cleanup());\n        this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n    getViewportSize() {\n        if (!this._viewportSize) {\n            this._updateViewportSize();\n        }\n        const output = { width: this._viewportSize.width, height: this._viewportSize.height };\n        // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n        if (!this._platform.isBrowser) {\n            this._viewportSize = null;\n        }\n        return output;\n    }\n    /** Gets a DOMRect for the viewport's bounds. */\n    getViewportRect() {\n        // Use the document element's bounding rect rather than the window scroll properties\n        // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n        // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n        // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n        // can disagree when the page is pinch-zoomed (on devices that support touch).\n        // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n        // We use the documentElement instead of the body because, by default (without a css reset)\n        // browsers typically give the document body an 8px margin, which is not included in\n        // getBoundingClientRect().\n        const scrollPosition = this.getViewportScrollPosition();\n        const { width, height } = this.getViewportSize();\n        return {\n            top: scrollPosition.top,\n            left: scrollPosition.left,\n            bottom: scrollPosition.top + height,\n            right: scrollPosition.left + width,\n            height,\n            width,\n        };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n    getViewportScrollPosition() {\n        // While we can get a reference to the fake document\n        // during SSR, it doesn't have getBoundingClientRect.\n        if (!this._platform.isBrowser) {\n            return { top: 0, left: 0 };\n        }\n        // The top-left-corner of the viewport is determined by the scroll position of the document\n        // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n        // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n        // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n        // `document.documentElement` works consistently, where the `top` and `left` values will\n        // equal negative the scroll position.\n        const document = this._document;\n        const window = this._getWindow();\n        const documentElement = document.documentElement;\n        const documentRect = documentElement.getBoundingClientRect();\n        const top = -documentRect.top ||\n            document.body.scrollTop ||\n            window.scrollY ||\n            documentElement.scrollTop ||\n            0;\n        const left = -documentRect.left ||\n            document.body.scrollLeft ||\n            window.scrollX ||\n            documentElement.scrollLeft ||\n            0;\n        return { top, left };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n        return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n    _updateViewportSize() {\n        const window = this._getWindow();\n        this._viewportSize = this._platform.isBrowser\n            ? { width: window.innerWidth, height: window.innerHeight }\n            : { width: 0, height: 0 };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ViewportRuler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ViewportRuler, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ViewportRuler, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nconst VIRTUAL_SCROLLABLE = new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the {@link CdkScrollable} to be used as scrolling container for virtual scrolling.\n */\nclass CdkVirtualScrollable extends CdkScrollable {\n    constructor() {\n        super();\n    }\n    /**\n     * Measure the viewport size for the provided orientation.\n     *\n     * @param orientation The orientation to measure the size from.\n     */\n    measureViewportSize(orientation) {\n        const viewportEl = this.elementRef.nativeElement;\n        return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollable, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkVirtualScrollable, isStandalone: true, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollable, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [] });\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n    return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkVirtualScrollable {\n    elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _scrollStrategy = inject(VIRTUAL_SCROLL_STRATEGY, {\n        optional: true,\n    });\n    scrollable = inject(VIRTUAL_SCROLLABLE, { optional: true });\n    _platform = inject(Platform);\n    /** Emits when the viewport is detached from a CdkVirtualForOf. */\n    _detachedSubject = new Subject();\n    /** Emits when the rendered range changes. */\n    _renderedRangeSubject = new Subject();\n    /** The direction the viewport scrolls. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(orientation) {\n        if (this._orientation !== orientation) {\n            this._orientation = orientation;\n            this._calculateSpacerSize();\n        }\n    }\n    _orientation = 'vertical';\n    /**\n     * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n     * will be removed.\n     */\n    appendOnly = false;\n    // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n    // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n    // depending on how the strategy calculates the scrolled index, it may come at a cost to\n    // performance.\n    /** Emits when the index of the first element visible in the viewport changes. */\n    scrolledIndexChange = new Observable((observer) => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n    /** The element that wraps the rendered content. */\n    _contentWrapper;\n    /** A stream that emits whenever the rendered range changes. */\n    renderedRangeStream = this._renderedRangeSubject;\n    /**\n     * The total size of all content (in pixels), including content that is not currently rendered.\n     */\n    _totalContentSize = 0;\n    /** A string representing the `style.width` property value to be used for the spacer element. */\n    _totalContentWidth = signal('');\n    /** A string representing the `style.height` property value to be used for the spacer element. */\n    _totalContentHeight = signal('');\n    /**\n     * The CSS transform applied to the rendered subset of items so that they appear within the bounds\n     * of the visible viewport.\n     */\n    _renderedContentTransform;\n    /** The currently rendered range of indices. */\n    _renderedRange = { start: 0, end: 0 };\n    /** The length of the data bound to this viewport (in number of items). */\n    _dataLength = 0;\n    /** The size of the viewport (in pixels). */\n    _viewportSize = 0;\n    /** the currently attached CdkVirtualScrollRepeater. */\n    _forOf;\n    /** The last rendered content offset that was set. */\n    _renderedContentOffset = 0;\n    /**\n     * Whether the last rendered content offset was to the end of the content (and therefore needs to\n     * be rewritten as an offset to the start of the content).\n     */\n    _renderedContentOffsetNeedsRewrite = false;\n    /** Whether there is a pending change detection cycle. */\n    _isChangeDetectionPending = false;\n    /** A list of functions to run after the next change detection cycle. */\n    _runAfterChangeDetection = [];\n    /** Subscription to changes in the viewport size. */\n    _viewportChanges = Subscription.EMPTY;\n    _injector = inject(Injector);\n    _isDestroyed = false;\n    constructor() {\n        super();\n        const viewportRuler = inject(ViewportRuler);\n        if (!this._scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n        }\n        this._viewportChanges = viewportRuler.change().subscribe(() => {\n            this.checkViewportSize();\n        });\n        if (!this.scrollable) {\n            // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n            this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n            this.scrollable = this;\n        }\n    }\n    ngOnInit() {\n        // Scrolling depends on the element dimensions which we can't get during SSR.\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        if (this.scrollable === this) {\n            super.ngOnInit();\n        }\n        // It's still too early to measure the viewport at this point. Deferring with a promise allows\n        // the Viewport to be rendered with the correct size before we measure. We run this outside the\n        // zone to avoid causing more change detection cycles. We handle the change detection loop\n        // ourselves instead.\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n            this._measureViewportSize();\n            this._scrollStrategy.attach(this);\n            this.scrollable\n                .elementScrolled()\n                .pipe(\n            // Start off with a fake scroll event so we properly detect our initial position.\n            startWith(null), \n            // Collect multiple events into one until the next animation frame. This way if\n            // there are multiple scroll events in the same frame we only need to recheck\n            // our layout once.\n            auditTime(0, SCROLL_SCHEDULER), \n            // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n            // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n            // to unsubscribe here just in case.\n            takeUntil(this._destroyed))\n                .subscribe(() => this._scrollStrategy.onContentScrolled());\n            this._markChangeDetectionNeeded();\n        }));\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._scrollStrategy.detach();\n        // Complete all subjects\n        this._renderedRangeSubject.complete();\n        this._detachedSubject.complete();\n        this._viewportChanges.unsubscribe();\n        this._isDestroyed = true;\n        super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n    attach(forOf) {\n        if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CdkVirtualScrollViewport is already attached.');\n        }\n        // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n        // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n        // change detection loop ourselves.\n        this.ngZone.runOutsideAngular(() => {\n            this._forOf = forOf;\n            this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n                const newLength = data.length;\n                if (newLength !== this._dataLength) {\n                    this._dataLength = newLength;\n                    this._scrollStrategy.onDataLengthChanged();\n                }\n                this._doChangeDetection();\n            });\n        });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n    detach() {\n        this._forOf = null;\n        this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n    getDataLength() {\n        return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n    getViewportSize() {\n        return this._viewportSize;\n    }\n    // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n    /** Get the current rendered range of items. */\n    getRenderedRange() {\n        return this._renderedRange;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n    setTotalContentSize(size) {\n        if (this._totalContentSize !== size) {\n            this._totalContentSize = size;\n            this._calculateSpacerSize();\n            this._markChangeDetectionNeeded();\n        }\n    }\n    /** Sets the currently rendered range of indices. */\n    setRenderedRange(range) {\n        if (!rangesEqual(this._renderedRange, range)) {\n            if (this.appendOnly) {\n                range = { start: 0, end: Math.max(this._renderedRange.end, range.end) };\n            }\n            this._renderedRangeSubject.next((this._renderedRange = range));\n            this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n        }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n    getOffsetToRenderedContentStart() {\n        return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n    setRenderedContentOffset(offset, to = 'to-start') {\n        // In appendOnly, we always start from the top\n        offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n        // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n        // in the negative direction.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        const isHorizontal = this.orientation == 'horizontal';\n        const axis = isHorizontal ? 'X' : 'Y';\n        const axisDirection = isHorizontal && isRtl ? -1 : 1;\n        let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n        this._renderedContentOffset = offset;\n        if (to === 'to-end') {\n            transform += ` translate${axis}(-100%)`;\n            // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n            // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n            // expand upward).\n            this._renderedContentOffsetNeedsRewrite = true;\n        }\n        if (this._renderedContentTransform != transform) {\n            // We know this value is safe because we parse `offset` with `Number()` before passing it\n            // into the string.\n            this._renderedContentTransform = transform;\n            this._markChangeDetectionNeeded(() => {\n                if (this._renderedContentOffsetNeedsRewrite) {\n                    this._renderedContentOffset -= this.measureRenderedContentSize();\n                    this._renderedContentOffsetNeedsRewrite = false;\n                    this.setRenderedContentOffset(this._renderedContentOffset);\n                }\n                else {\n                    this._scrollStrategy.onRenderedOffsetChanged();\n                }\n            });\n        }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToOffset(offset, behavior = 'auto') {\n        const options = { behavior };\n        if (this.orientation === 'horizontal') {\n            options.start = offset;\n        }\n        else {\n            options.top = offset;\n        }\n        this.scrollable.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToIndex(index, behavior = 'auto') {\n        this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the scrollable (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n    measureScrollOffset(from) {\n        // This is to break the call cycle\n        let measureScrollOffset;\n        if (this.scrollable == this) {\n            measureScrollOffset = (_from) => super.measureScrollOffset(_from);\n        }\n        else {\n            measureScrollOffset = (_from) => this.scrollable.measureScrollOffset(_from);\n        }\n        return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) -\n            this.measureViewportOffset());\n    }\n    /**\n     * Measures the offset of the viewport from the scrolling container\n     * @param from The edge to measure from.\n     */\n    measureViewportOffset(from) {\n        let fromRect;\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const isRtl = this.dir?.value == 'rtl';\n        if (from == 'start') {\n            fromRect = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            fromRect = isRtl ? LEFT : RIGHT;\n        }\n        else if (from) {\n            fromRect = from;\n        }\n        else {\n            fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n        }\n        const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n        const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n        return viewportClientRect - scrollerClientRect;\n    }\n    /** Measure the combined size of all of the rendered items. */\n    measureRenderedContentSize() {\n        const contentEl = this._contentWrapper.nativeElement;\n        return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n    measureRangeSize(range) {\n        if (!this._forOf) {\n            return 0;\n        }\n        return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n    checkViewportSize() {\n        // TODO: Cleanup later when add logic for handling content resize\n        this._measureViewportSize();\n        this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n    _measureViewportSize() {\n        this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n    }\n    /** Queue up change detection to run. */\n    _markChangeDetectionNeeded(runAfter) {\n        if (runAfter) {\n            this._runAfterChangeDetection.push(runAfter);\n        }\n        // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n        // properties sequentially we only have to run `_doChangeDetection` once at the end.\n        if (!this._isChangeDetectionPending) {\n            this._isChangeDetectionPending = true;\n            this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n                this._doChangeDetection();\n            }));\n        }\n    }\n    /** Run change detection. */\n    _doChangeDetection() {\n        if (this._isDestroyed) {\n            return;\n        }\n        this.ngZone.run(() => {\n            // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n            // from the root, since the repeated items are content projected in. Calling `detectChanges`\n            // instead does not properly check the projected content.\n            this._changeDetectorRef.markForCheck();\n            // Apply the content transform. The transform can't be set via an Angular binding because\n            // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n            // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n            // the `Number` function first to coerce it to a numeric value.\n            this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n            afterNextRender(() => {\n                this._isChangeDetectionPending = false;\n                const runAfterChangeDetection = this._runAfterChangeDetection;\n                this._runAfterChangeDetection = [];\n                for (const fn of runAfterChangeDetection) {\n                    fn();\n                }\n            }, { injector: this._injector });\n        });\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n    _calculateSpacerSize() {\n        this._totalContentHeight.set(this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`);\n        this._totalContentWidth.set(this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollViewport, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: CdkVirtualScrollViewport, isStandalone: true, selector: \"cdk-virtual-scroll-viewport\", inputs: { orientation: \"orientation\", appendOnly: [\"appendOnly\", \"appendOnly\", booleanAttribute] }, outputs: { scrolledIndexChange: \"scrolledIndexChange\" }, host: { properties: { \"class.cdk-virtual-scroll-orientation-horizontal\": \"orientation === \\\"horizontal\\\"\", \"class.cdk-virtual-scroll-orientation-vertical\": \"orientation !== \\\"horizontal\\\"\" }, classAttribute: \"cdk-virtual-scroll-viewport\" }, providers: [\n            {\n                provide: CdkScrollable,\n                useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n            },\n        ], viewQueries: [{ propertyName: \"_contentWrapper\", first: true, predicate: [\"contentWrapper\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth()\\\" [style.height]=\\\"_totalContentHeight()\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollViewport, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-virtual-scroll-viewport', host: {\n                        'class': 'cdk-virtual-scroll-viewport',\n                        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n                        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        {\n                            provide: CdkScrollable,\n                            useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                            deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n                        },\n                    ], template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth()\\\" [style.height]=\\\"_totalContentHeight()\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { orientation: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], scrolledIndexChange: [{\n                type: Output\n            }], _contentWrapper: [{\n                type: ViewChild,\n                args: ['contentWrapper', { static: true }]\n            }] } });\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n    const el = node;\n    if (!el.getBoundingClientRect) {\n        return 0;\n    }\n    const rect = el.getBoundingClientRect();\n    if (orientation === 'horizontal') {\n        return direction === 'start' ? rect.left : rect.right;\n    }\n    return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n    _viewContainerRef = inject(ViewContainerRef);\n    _template = inject(TemplateRef);\n    _differs = inject(IterableDiffers);\n    _viewRepeater = inject(_VIEW_REPEATER_STRATEGY);\n    _viewport = inject(CdkVirtualScrollViewport, { skipSelf: true });\n    /** Emits when the rendered view of the data changes. */\n    viewChange = new Subject();\n    /** Subject that emits when a new DataSource instance is given. */\n    _dataSourceChanges = new Subject();\n    /** The DataSource to display. */\n    get cdkVirtualForOf() {\n        return this._cdkVirtualForOf;\n    }\n    set cdkVirtualForOf(value) {\n        this._cdkVirtualForOf = value;\n        if (isDataSource(value)) {\n            this._dataSourceChanges.next(value);\n        }\n        else {\n            // If value is an an NgIterable, convert it to an array.\n            this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n        }\n    }\n    _cdkVirtualForOf;\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n    get cdkVirtualForTrackBy() {\n        return this._cdkVirtualForTrackBy;\n    }\n    set cdkVirtualForTrackBy(fn) {\n        this._needsUpdate = true;\n        this._cdkVirtualForTrackBy = fn\n            ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item)\n            : undefined;\n    }\n    _cdkVirtualForTrackBy;\n    /** The template used to stamp out new elements. */\n    set cdkVirtualForTemplate(value) {\n        if (value) {\n            this._needsUpdate = true;\n            this._template = value;\n        }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n    get cdkVirtualForTemplateCacheSize() {\n        return this._viewRepeater.viewCacheSize;\n    }\n    set cdkVirtualForTemplateCacheSize(size) {\n        this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    /** Emits whenever the data in the current DataSource changes. */\n    dataStream = this._dataSourceChanges.pipe(\n    // Start off with null `DataSource`.\n    startWith(null), \n    // Bundle up the previous and current data sources so we can work with both.\n    pairwise(), \n    // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n    // new one, passing back a stream of data changes which we run through `switchMap` to give\n    // us a data stream that emits the latest data from whatever the current `DataSource` is.\n    switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), \n    // Replay the last emitted data when someone subscribes.\n    shareReplay(1));\n    /** The differ used to calculate changes to the data. */\n    _differ = null;\n    /** The most recent data emitted from the DataSource. */\n    _data;\n    /** The currently rendered items. */\n    _renderedItems;\n    /** The currently rendered range of indices. */\n    _renderedRange;\n    /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n    _needsUpdate = false;\n    _destroyed = new Subject();\n    constructor() {\n        const ngZone = inject(NgZone);\n        this.dataStream.subscribe(data => {\n            this._data = data;\n            this._onRenderedDataChange();\n        });\n        this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n            this._renderedRange = range;\n            if (this.viewChange.observers.length) {\n                ngZone.run(() => this.viewChange.next(this._renderedRange));\n            }\n            this._onRenderedDataChange();\n        });\n        this._viewport.attach(this);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n    measureRangeSize(range, orientation) {\n        if (range.start >= range.end) {\n            return 0;\n        }\n        if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Error: attempted to measure an item that isn't rendered.`);\n        }\n        // The index into the list of rendered views for the first item in the range.\n        const renderedStartIndex = range.start - this._renderedRange.start;\n        // The length of the range we're measuring.\n        const rangeLen = range.end - range.start;\n        // Loop over all the views, find the first and land node and compute the size by subtracting\n        // the top of the first node from the bottom of the last one.\n        let firstNode;\n        let lastNode;\n        // Find the first node by starting from the beginning and going forwards.\n        for (let i = 0; i < rangeLen; i++) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                firstNode = lastNode = view.rootNodes[0];\n                break;\n            }\n        }\n        // Find the last node by starting from the end and going backwards.\n        for (let i = rangeLen - 1; i > -1; i--) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                lastNode = view.rootNodes[view.rootNodes.length - 1];\n                break;\n            }\n        }\n        return firstNode && lastNode\n            ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode)\n            : 0;\n    }\n    ngDoCheck() {\n        if (this._differ && this._needsUpdate) {\n            // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n            // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n            // changing (need to do this diff).\n            const changes = this._differ.diff(this._renderedItems);\n            if (!changes) {\n                this._updateContext();\n            }\n            else {\n                this._applyChanges(changes);\n            }\n            this._needsUpdate = false;\n        }\n    }\n    ngOnDestroy() {\n        this._viewport.detach();\n        this._dataSourceChanges.next(undefined);\n        this._dataSourceChanges.complete();\n        this.viewChange.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n    _onRenderedDataChange() {\n        if (!this._renderedRange) {\n            return;\n        }\n        this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n        if (!this._differ) {\n            // Use a wrapper function for the `trackBy` so any new values are\n            // picked up automatically without having to recreate the differ.\n            this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n                return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n            });\n        }\n        this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n    _changeDataSource(oldDs, newDs) {\n        if (oldDs) {\n            oldDs.disconnect(this);\n        }\n        this._needsUpdate = true;\n        return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n    _updateContext() {\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n            view.detectChanges();\n        }\n    }\n    /** Apply changes to the DOM. */\n    _applyChanges(changes) {\n        this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n        // Update $implicit for any items that had an identity change.\n        changes.forEachIdentityChange((record) => {\n            const view = this._viewContainerRef.get(record.currentIndex);\n            view.context.$implicit = record.item;\n        });\n        // Update the context variables on all items.\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n        }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n    _updateComputedContextProperties(context) {\n        context.first = context.index === 0;\n        context.last = context.index === context.count - 1;\n        context.even = context.index % 2 === 0;\n        context.odd = !context.even;\n    }\n    _getEmbeddedViewArgs(record, index) {\n        // Note that it's important that we insert the item directly at the proper index,\n        // rather than inserting it and the moving it in place, because if there's a directive\n        // on the same node that injects the `ViewContainerRef`, Angular will insert another\n        // comment node which can throw off the move when it's being repeated for all items.\n        return {\n            templateRef: this._template,\n            context: {\n                $implicit: record.item,\n                // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n                // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n                cdkVirtualForOf: this._cdkVirtualForOf,\n                index: -1,\n                count: -1,\n                first: false,\n                last: false,\n                odd: false,\n                even: false,\n            },\n            index,\n        };\n    }\n    static ngTemplateContextGuard(directive, context) {\n        return true;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualForOf, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkVirtualForOf, isStandalone: true, selector: \"[cdkVirtualFor][cdkVirtualForOf]\", inputs: { cdkVirtualForOf: \"cdkVirtualForOf\", cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\", cdkVirtualForTemplate: \"cdkVirtualForTemplate\", cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\" }, providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualForOf, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualFor][cdkVirtualForOf]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { cdkVirtualForOf: [{\n                type: Input\n            }], cdkVirtualForTrackBy: [{\n                type: Input\n            }], cdkVirtualForTemplate: [{\n                type: Input\n            }], cdkVirtualForTemplateCacheSize: [{\n                type: Input\n            }] } });\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nclass CdkVirtualScrollableElement extends CdkVirtualScrollable {\n    constructor() {\n        super();\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return (this.getElementRef().nativeElement.getBoundingClientRect()[from] -\n            this.measureScrollOffset(from));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollableElement, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkVirtualScrollableElement, isStandalone: true, selector: \"[cdkVirtualScrollingElement]\", host: { classAttribute: \"cdk-virtual-scrollable\" }, providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollableElement, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualScrollingElement]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }],\n                    host: {\n                        'class': 'cdk-virtual-scrollable',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nclass CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n    constructor() {\n        super();\n        const document = inject(DOCUMENT);\n        this.elementRef = new ElementRef(document.documentElement);\n        this._scrollElement = document;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollableWindow, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkVirtualScrollableWindow, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[scrollWindow]\", providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollableWindow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }],\n                }]\n        }], ctorParameters: () => [] });\n\nclass CdkScrollableModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollableModule, imports: [CdkScrollable], exports: [CdkScrollable] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollableModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkScrollable],\n                    imports: [CdkScrollable],\n                }]\n        }] });\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollingModule, imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport,\n            CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement], exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollViewport,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollingModule, imports: [BidiModule,\n            CdkScrollableModule, BidiModule, CdkScrollableModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkVirtualScrollViewport,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                    exports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollViewport,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                }]\n        }] });\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACzX,SAASC,OAAO,EAAEC,EAAE,EAAEC,UAAU,EAAEC,YAAY,EAAEC,uBAAuB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,MAAM;AAClH,SAASC,oBAAoB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AAChI,SAASC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,aAAa,QAAQ,wBAAwB;AACtF,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,SAASC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,sBAAsB,QAAQ,0BAA0B;AACzH,SAASC,UAAU,QAAQ,YAAY;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACxC,SAASC,GAAG,IAAIC,KAAK,QAAQ,YAAY;AACzC,SAASC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,4BAA4B,QAAQ,+CAA+C;AACrJ,SAASC,CAAC,IAAIC,YAAY,QAAQ,4BAA4B;AAC9D,OAAO,iBAAiB;;AAExB;AACA,MAAMC,uBAAuB,GAAG,IAAIrE,cAAc,CAAC,yBAAyB,CAAC;;AAE7E;AACA,MAAMsE,8BAA8B,CAAC;EACjCC,oBAAoB,GAAG,IAAI5C,OAAO,CAAC,CAAC;EACpC;EACA6C,mBAAmB,GAAG,IAAI,CAACD,oBAAoB,CAACE,IAAI,CAACvC,oBAAoB,CAAC,CAAC,CAAC;EAC5E;EACAwC,SAAS,GAAG,IAAI;EAChB;EACAC,SAAS;EACT;EACAC,YAAY;EACZ;EACAC,YAAY;EACZ;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IAC5C,IAAI,CAACN,SAAS,GAAGI,QAAQ;IACzB,IAAI,CAACH,YAAY,GAAGI,WAAW;IAC/B,IAAI,CAACH,YAAY,GAAGI,WAAW;EACnC;EACA;AACJ;AACA;AACA;EACIC,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,CAACT,SAAS,GAAGS,QAAQ;IACzB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACf,oBAAoB,CAACgB,QAAQ,CAAC,CAAC;IACpC,IAAI,CAACb,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIc,uBAAuBA,CAACT,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IACxD,IAAIA,WAAW,GAAGD,WAAW,KAAK,OAAOS,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC9E,MAAMC,KAAK,CAAC,8EAA8E,CAAC;IAC/F;IACA,IAAI,CAACf,SAAS,GAAGI,QAAQ;IACzB,IAAI,CAACH,YAAY,GAAGI,WAAW;IAC/B,IAAI,CAACH,YAAY,GAAGI,WAAW;IAC/B,IAAI,CAACG,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAM,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACN,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAO,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACR,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAQ,iBAAiBA,CAAA,EAAG;IAChB;EAAA;EAEJ;EACAC,uBAAuBA,CAAA,EAAG;IACtB;EAAA;EAEJ;AACJ;AACA;AACA;AACA;EACIC,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC3B,IAAI,IAAI,CAACvB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACwB,cAAc,CAACF,KAAK,GAAG,IAAI,CAACrB,SAAS,EAAEsB,QAAQ,CAAC;IACnE;EACJ;EACA;EACAb,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACV,SAAS,EAAE;MACjB;IACJ;IACA,IAAI,CAACA,SAAS,CAACyB,mBAAmB,CAAC,IAAI,CAACzB,SAAS,CAAC0B,aAAa,CAAC,CAAC,GAAG,IAAI,CAACzB,SAAS,CAAC;EACvF;EACA;EACAU,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACX,SAAS,EAAE;MACjB;IACJ;IACA,MAAM2B,aAAa,GAAG,IAAI,CAAC3B,SAAS,CAAC4B,gBAAgB,CAAC,CAAC;IACvD,MAAMC,QAAQ,GAAG;MAAEC,KAAK,EAAEH,aAAa,CAACG,KAAK;MAAEC,GAAG,EAAEJ,aAAa,CAACI;IAAI,CAAC;IACvE,MAAMC,YAAY,GAAG,IAAI,CAAChC,SAAS,CAACiC,eAAe,CAAC,CAAC;IACrD,MAAMC,UAAU,GAAG,IAAI,CAAClC,SAAS,CAAC0B,aAAa,CAAC,CAAC;IACjD,IAAIS,YAAY,GAAG,IAAI,CAACnC,SAAS,CAACoC,mBAAmB,CAAC,CAAC;IACvD;IACA,IAAIC,iBAAiB,GAAG,IAAI,CAACpC,SAAS,GAAG,CAAC,GAAGkC,YAAY,GAAG,IAAI,CAAClC,SAAS,GAAG,CAAC;IAC9E;IACA,IAAI4B,QAAQ,CAACE,GAAG,GAAGG,UAAU,EAAE;MAC3B;MACA,MAAMI,eAAe,GAAGC,IAAI,CAACC,IAAI,CAACR,YAAY,GAAG,IAAI,CAAC/B,SAAS,CAAC;MAChE,MAAMwC,eAAe,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACN,iBAAiB,EAAEH,UAAU,GAAGI,eAAe,CAAC,CAAC;MAC9F;MACA;MACA,IAAID,iBAAiB,IAAII,eAAe,EAAE;QACtCJ,iBAAiB,GAAGI,eAAe;QACnCN,YAAY,GAAGM,eAAe,GAAG,IAAI,CAACxC,SAAS;QAC/C4B,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC;MAClD;MACAR,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACC,KAAK,GAAGQ,eAAe,CAAC,CAAC;IACtF;IACA,MAAMO,WAAW,GAAGV,YAAY,GAAGN,QAAQ,CAACC,KAAK,GAAG,IAAI,CAAC7B,SAAS;IAClE,IAAI4C,WAAW,GAAG,IAAI,CAAC3C,YAAY,IAAI2B,QAAQ,CAACC,KAAK,IAAI,CAAC,EAAE;MACxD,MAAMgB,WAAW,GAAGP,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACrC,YAAY,GAAG0C,WAAW,IAAI,IAAI,CAAC5C,SAAS,CAAC;MACjF4B,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEb,QAAQ,CAACC,KAAK,GAAGgB,WAAW,CAAC;MAC1DjB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEK,IAAI,CAACC,IAAI,CAACH,iBAAiB,GAAG,CAACL,YAAY,GAAG,IAAI,CAAC9B,YAAY,IAAI,IAAI,CAACD,SAAS,CAAC,CAAC;IAC3H,CAAC,MACI;MACD,MAAM8C,SAAS,GAAGlB,QAAQ,CAACE,GAAG,GAAG,IAAI,CAAC9B,SAAS,IAAIkC,YAAY,GAAGH,YAAY,CAAC;MAC/E,IAAIe,SAAS,GAAG,IAAI,CAAC7C,YAAY,IAAI2B,QAAQ,CAACE,GAAG,IAAIG,UAAU,EAAE;QAC7D,MAAMc,SAAS,GAAGT,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACrC,YAAY,GAAG4C,SAAS,IAAI,IAAI,CAAC9C,SAAS,CAAC;QAC7E,IAAI+C,SAAS,GAAG,CAAC,EAAE;UACfnB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACE,GAAG,GAAGiB,SAAS,CAAC;UAC7DnB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACK,KAAK,CAACP,iBAAiB,GAAG,IAAI,CAACnC,YAAY,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC;QACpG;MACJ;IACJ;IACA,IAAI,CAACD,SAAS,CAACiD,gBAAgB,CAACpB,QAAQ,CAAC;IACzC,IAAI,CAAC7B,SAAS,CAACkD,wBAAwB,CAAC,IAAI,CAACjD,SAAS,GAAG4B,QAAQ,CAACC,KAAK,CAAC;IACxE,IAAI,CAACjC,oBAAoB,CAACsD,IAAI,CAACZ,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC,CAAC;EACjE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,sCAAsCA,CAACC,YAAY,EAAE;EAC1D,OAAOA,YAAY,CAACC,eAAe;AACvC;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5B;EACA,IAAIlD,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACJ,SAAS;EACzB;EACA,IAAII,QAAQA,CAACmD,KAAK,EAAE;IAChB,IAAI,CAACvD,SAAS,GAAGhC,oBAAoB,CAACuF,KAAK,CAAC;EAChD;EACAvD,SAAS,GAAG,EAAE;EACd;AACJ;AACA;AACA;EACI,IAAIK,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACJ,YAAY;EAC5B;EACA,IAAII,WAAWA,CAACkD,KAAK,EAAE;IACnB,IAAI,CAACtD,YAAY,GAAGjC,oBAAoB,CAACuF,KAAK,CAAC;EACnD;EACAtD,YAAY,GAAG,GAAG;EAClB;AACJ;AACA;EACI,IAAIK,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACJ,YAAY;EAC5B;EACA,IAAII,WAAWA,CAACiD,KAAK,EAAE;IACnB,IAAI,CAACrD,YAAY,GAAGlC,oBAAoB,CAACuF,KAAK,CAAC;EACnD;EACArD,YAAY,GAAG,GAAG;EAClB;EACAmD,eAAe,GAAG,IAAI1D,8BAA8B,CAAC,IAAI,CAACS,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;EACvGkD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,eAAe,CAACxC,uBAAuB,CAAC,IAAI,CAACT,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;EACnG;EACA,OAAOmD,IAAI,YAAAC,kCAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFL,yBAAyB;EAAA;EAC5H,OAAOM,IAAI,kBAD8ExI,EAAE,CAAAyI,iBAAA;IAAAC,IAAA,EACJR,yBAAyB;IAAAS,SAAA;IAAAC,MAAA;MAAA5D,QAAA;MAAAC,WAAA;MAAAC,WAAA;IAAA;IAAA2D,QAAA,GADvB7I,EAAE,CAAA8I,kBAAA,CACmM,CACtR;MACIC,OAAO,EAAEzE,uBAAuB;MAChC0E,UAAU,EAAEjB,sCAAsC;MAClDkB,IAAI,EAAE,CAAC/I,UAAU,CAAC,MAAMgI,yBAAyB,CAAC;IACtD,CAAC,CACJ,GAPoFlI,EAAE,CAAAkJ,oBAAA;EAAA;AAQ/F;AACA;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KAT6F1F,EAAE,CAAAmJ,iBAAA,CASJjB,yBAAyB,EAAc,CAAC;IACvHQ,IAAI,EAAEvI,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uCAAuC;MACjDC,SAAS,EAAE,CACP;QACIP,OAAO,EAAEzE,uBAAuB;QAChC0E,UAAU,EAAEjB,sCAAsC;QAClDkB,IAAI,EAAE,CAAC/I,UAAU,CAAC,MAAMgI,yBAAyB,CAAC;MACtD,CAAC;IAET,CAAC;EACT,CAAC,CAAC,QAAkB;IAAElD,QAAQ,EAAE,CAAC;MACzB0D,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAE6E,WAAW,EAAE,CAAC;MACdyD,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAE8E,WAAW,EAAE,CAAC;MACdwD,IAAI,EAAEtI;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMmJ,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBC,OAAO,GAAGpJ,MAAM,CAACC,MAAM,CAAC;EACxBoJ,SAAS,GAAGrJ,MAAM,CAAC2C,QAAQ,CAAC;EAC5B2G,SAAS,GAAGtJ,MAAM,CAACE,gBAAgB,CAAC,CAACqJ,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC/DC,sBAAsB;EACtB9E,WAAWA,CAAA,EAAG,CAAE;EAChB;EACA+E,SAAS,GAAG,IAAIlI,OAAO,CAAC,CAAC;EACzB;EACAmI,cAAc,GAAG,CAAC;EAClB;AACJ;AACA;AACA;EACIC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;EACIC,QAAQA,CAACC,UAAU,EAAE;IACjB,IAAI,CAAC,IAAI,CAACH,gBAAgB,CAACI,GAAG,CAACD,UAAU,CAAC,EAAE;MACxC,IAAI,CAACH,gBAAgB,CAACK,GAAG,CAACF,UAAU,EAAEA,UAAU,CAACG,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM,IAAI,CAACT,SAAS,CAAChC,IAAI,CAACqC,UAAU,CAAC,CAAC,CAAC;IACxH;EACJ;EACA;AACJ;AACA;AACA;EACIK,UAAUA,CAACL,UAAU,EAAE;IACnB,MAAMM,mBAAmB,GAAG,IAAI,CAACT,gBAAgB,CAACU,GAAG,CAACP,UAAU,CAAC;IACjE,IAAIM,mBAAmB,EAAE;MACrBA,mBAAmB,CAACE,WAAW,CAAC,CAAC;MACjC,IAAI,CAACX,gBAAgB,CAACY,MAAM,CAACT,UAAU,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIU,QAAQA,CAACC,aAAa,GAAGvB,mBAAmB,EAAE;IAC1C,IAAI,CAAC,IAAI,CAACG,SAAS,CAACqB,SAAS,EAAE;MAC3B,OAAOlJ,EAAE,CAAC,CAAC;IACf;IACA,OAAO,IAAIC,UAAU,CAAEkJ,QAAQ,IAAK;MAChC,IAAI,CAAC,IAAI,CAACnB,sBAAsB,EAAE;QAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAACJ,OAAO,CAACwB,iBAAiB,CAAC,MAAM,IAAI,CAACtB,SAAS,CAACuB,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,IAAI,CAACpB,SAAS,CAAChC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChJ;MACA;MACA;MACA,MAAMqD,YAAY,GAAGL,aAAa,GAAG,CAAC,GAChC,IAAI,CAAChB,SAAS,CAACpF,IAAI,CAACtC,SAAS,CAAC0I,aAAa,CAAC,CAAC,CAACP,SAAS,CAACS,QAAQ,CAAC,GACjE,IAAI,CAAClB,SAAS,CAACS,SAAS,CAACS,QAAQ,CAAC;MACxC,IAAI,CAACjB,cAAc,EAAE;MACrB,OAAO,MAAM;QACToB,YAAY,CAACR,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACZ,cAAc,EAAE;QACrB,IAAI,CAAC,IAAI,CAACA,cAAc,EAAE;UACtB,IAAI,CAACF,sBAAsB,GAAG,CAAC;UAC/B,IAAI,CAACA,sBAAsB,GAAGuB,SAAS;QAC3C;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACxB,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACA,sBAAsB,GAAGuB,SAAS;IACvC,IAAI,CAACpB,gBAAgB,CAACsB,OAAO,CAAC,CAACpH,CAAC,EAAEqH,SAAS,KAAK,IAAI,CAACf,UAAU,CAACe,SAAS,CAAC,CAAC;IAC3E,IAAI,CAACzB,SAAS,CAACtE,QAAQ,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgG,gBAAgBA,CAACC,mBAAmB,EAAEX,aAAa,EAAE;IACjD,MAAMY,SAAS,GAAG,IAAI,CAACC,2BAA2B,CAACF,mBAAmB,CAAC;IACvE,OAAO,IAAI,CAACZ,QAAQ,CAACC,aAAa,CAAC,CAACpG,IAAI,CAACrC,MAAM,CAACuJ,MAAM,IAAI,CAACA,MAAM,IAAIF,SAAS,CAACG,OAAO,CAACD,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzG;EACA;EACAD,2BAA2BA,CAACF,mBAAmB,EAAE;IAC7C,MAAMK,mBAAmB,GAAG,EAAE;IAC9B,IAAI,CAAC9B,gBAAgB,CAACsB,OAAO,CAAC,CAACS,aAAa,EAAE5B,UAAU,KAAK;MACzD,IAAI,IAAI,CAAC6B,0BAA0B,CAAC7B,UAAU,EAAEsB,mBAAmB,CAAC,EAAE;QAClEK,mBAAmB,CAACG,IAAI,CAAC9B,UAAU,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,OAAO2B,mBAAmB;EAC9B;EACA;EACAE,0BAA0BA,CAAC7B,UAAU,EAAEsB,mBAAmB,EAAE;IACxD,IAAIS,OAAO,GAAGpJ,aAAa,CAAC2I,mBAAmB,CAAC;IAChD,IAAIU,iBAAiB,GAAGhC,UAAU,CAACiC,aAAa,CAAC,CAAC,CAACC,aAAa;IAChE;IACA;IACA,GAAG;MACC,IAAIH,OAAO,IAAIC,iBAAiB,EAAE;QAC9B,OAAO,IAAI;MACf;IACJ,CAAC,QAASD,OAAO,GAAGA,OAAO,CAACI,aAAa;IACzC,OAAO,KAAK;EAChB;EACA,OAAOjE,IAAI,YAAAkE,yBAAAhE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFiB,gBAAgB;EAAA;EACnH,OAAOgD,KAAK,kBAjJ6ExM,EAAE,CAAAyM,kBAAA;IAAAC,KAAA,EAiJYlD,gBAAgB;IAAAmD,OAAA,EAAhBnD,gBAAgB,CAAAnB,IAAA;IAAAuE,UAAA,EAAc;EAAM;AAC/I;AACA;EAAA,QAAAlH,SAAA,oBAAAA,SAAA,KAnJ6F1F,EAAE,CAAAmJ,iBAAA,CAmJJK,gBAAgB,EAAc,CAAC;IAC9Gd,IAAI,EAAElI,UAAU;IAChB4I,IAAI,EAAE,CAAC;MAAEwD,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,UAAU,GAAGzM,MAAM,CAACI,UAAU,CAAC;EAC/BsM,gBAAgB,GAAG1M,MAAM,CAACmJ,gBAAgB,CAAC;EAC3CwD,MAAM,GAAG3M,MAAM,CAACC,MAAM,CAAC;EACvB2M,GAAG,GAAG5M,MAAM,CAAC6C,cAAc,EAAE;IAAEgK,QAAQ,EAAE;EAAK,CAAC,CAAC;EAChDC,cAAc,GAAG,IAAI,CAACL,UAAU,CAACT,aAAa;EAC9Ce,UAAU,GAAG,IAAIxL,OAAO,CAAC,CAAC;EAC1B+H,SAAS,GAAGtJ,MAAM,CAACK,SAAS,CAAC;EAC7B2M,cAAc;EACdC,gBAAgB,GAAG,IAAI1L,OAAO,CAAC,CAAC;EAChCmD,WAAWA,CAAA,EAAG,CAAE;EAChBwI,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,cAAc,GAAG,IAAI,CAACL,MAAM,CAAC/B,iBAAiB,CAAC,MAAM,IAAI,CAACtB,SAAS,CAACuB,MAAM,CAAC,IAAI,CAACiC,cAAc,EAAE,QAAQ,EAAEK,KAAK,IAAI,IAAI,CAACF,gBAAgB,CAACxF,IAAI,CAAC0F,KAAK,CAAC,CAAC,CAAC;IAC3J,IAAI,CAACT,gBAAgB,CAAC7C,QAAQ,CAAC,IAAI,CAAC;EACxC;EACAmB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,gBAAgB,CAAC9H,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACuH,gBAAgB,CAACvC,UAAU,CAAC,IAAI,CAAC;IACtC,IAAI,CAAC4C,UAAU,CAACtF,IAAI,CAAC,CAAC;IACtB,IAAI,CAACsF,UAAU,CAAC5H,QAAQ,CAAC,CAAC;EAC9B;EACA;EACA8E,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACgD,gBAAgB;EAChC;EACA;EACAlB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACU,UAAU;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIW,QAAQA,CAACC,OAAO,EAAE;IACd,MAAMC,EAAE,GAAG,IAAI,CAACb,UAAU,CAACT,aAAa;IACxC,MAAMuB,KAAK,GAAG,IAAI,CAACX,GAAG,IAAI,IAAI,CAACA,GAAG,CAAC9E,KAAK,IAAI,KAAK;IACjD;IACA,IAAIuF,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;MACtBH,OAAO,CAACG,IAAI,GAAGD,KAAK,GAAGF,OAAO,CAAChH,GAAG,GAAGgH,OAAO,CAACjH,KAAK;IACtD;IACA,IAAIiH,OAAO,CAACI,KAAK,IAAI,IAAI,EAAE;MACvBJ,OAAO,CAACI,KAAK,GAAGF,KAAK,GAAGF,OAAO,CAACjH,KAAK,GAAGiH,OAAO,CAAChH,GAAG;IACvD;IACA;IACA,IAAIgH,OAAO,CAACK,MAAM,IAAI,IAAI,EAAE;MACxBL,OAAO,CAACM,GAAG,GACPL,EAAE,CAACM,YAAY,GAAGN,EAAE,CAACO,YAAY,GAAGR,OAAO,CAACK,MAAM;IAC1D;IACA;IACA,IAAIH,KAAK,IAAIxK,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAAC6K,MAAM,EAAE;MAC7D,IAAIT,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;QACtBH,OAAO,CAACI,KAAK,GACTH,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGX,OAAO,CAACG,IAAI;MACtD;MACA,IAAIzK,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAACgL,QAAQ,EAAE;QACtDZ,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACI,KAAK;MAChC,CAAC,MACI,IAAI1K,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAACiL,OAAO,EAAE;QAC1Db,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACI,KAAK,GAAG,CAACJ,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACI,KAAK;MACjE;IACJ,CAAC,MACI;MACD,IAAIJ,OAAO,CAACI,KAAK,IAAI,IAAI,EAAE;QACvBJ,OAAO,CAACG,IAAI,GACRF,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGX,OAAO,CAACI,KAAK;MACvD;IACJ;IACA,IAAI,CAACU,qBAAqB,CAACd,OAAO,CAAC;EACvC;EACAc,qBAAqBA,CAACd,OAAO,EAAE;IAC3B,MAAMC,EAAE,GAAG,IAAI,CAACb,UAAU,CAACT,aAAa;IACxC,IAAI7I,sBAAsB,CAAC,CAAC,EAAE;MAC1BmK,EAAE,CAACF,QAAQ,CAACC,OAAO,CAAC;IACxB,CAAC,MACI;MACD,IAAIA,OAAO,CAACM,GAAG,IAAI,IAAI,EAAE;QACrBL,EAAE,CAACc,SAAS,GAAGf,OAAO,CAACM,GAAG;MAC9B;MACA,IAAIN,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;QACtBF,EAAE,CAACe,UAAU,GAAGhB,OAAO,CAACG,IAAI;MAChC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI9G,mBAAmBA,CAAC4H,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,MAAM;IACnB,MAAMC,KAAK,GAAG,OAAO;IACrB,MAAMlB,EAAE,GAAG,IAAI,CAACb,UAAU,CAACT,aAAa;IACxC,IAAIsC,IAAI,IAAI,KAAK,EAAE;MACf,OAAOhB,EAAE,CAACc,SAAS;IACvB;IACA,IAAIE,IAAI,IAAI,QAAQ,EAAE;MAClB,OAAOhB,EAAE,CAACM,YAAY,GAAGN,EAAE,CAACO,YAAY,GAAGP,EAAE,CAACc,SAAS;IAC3D;IACA;IACA,MAAMb,KAAK,GAAG,IAAI,CAACX,GAAG,IAAI,IAAI,CAACA,GAAG,CAAC9E,KAAK,IAAI,KAAK;IACjD,IAAIwG,IAAI,IAAI,OAAO,EAAE;MACjBA,IAAI,GAAGf,KAAK,GAAGiB,KAAK,GAAGD,IAAI;IAC/B,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;MACpBA,IAAI,GAAGf,KAAK,GAAGgB,IAAI,GAAGC,KAAK;IAC/B;IACA,IAAIjB,KAAK,IAAIxK,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAACgL,QAAQ,EAAE;MAC/D;MACA;MACA,IAAIK,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOjB,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGV,EAAE,CAACe,UAAU;MAC1D,CAAC,MACI;QACD,OAAOf,EAAE,CAACe,UAAU;MACxB;IACJ,CAAC,MACI,IAAId,KAAK,IAAIxK,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAACiL,OAAO,EAAE;MACnE;MACA;MACA,IAAII,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOjB,EAAE,CAACe,UAAU,GAAGf,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW;MAC1D,CAAC,MACI;QACD,OAAO,CAACV,EAAE,CAACe,UAAU;MACzB;IACJ,CAAC,MACI;MACD;MACA;MACA,IAAIC,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOjB,EAAE,CAACe,UAAU;MACxB,CAAC,MACI;QACD,OAAOf,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGV,EAAE,CAACe,UAAU;MAC1D;IACJ;EACJ;EACA,OAAOrG,IAAI,YAAAyG,sBAAAvG,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsE,aAAa;EAAA;EAChH,OAAOrE,IAAI,kBAhT8ExI,EAAE,CAAAyI,iBAAA;IAAAC,IAAA,EAgTJmE,aAAa;IAAAlE,SAAA;EAAA;AACxG;AACA;EAAA,QAAAjD,SAAA,oBAAAA,SAAA,KAlT6F1F,EAAE,CAAAmJ,iBAAA,CAkTJ0D,aAAa,EAAc,CAAC;IAC3GnE,IAAI,EAAEvI,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAM0F,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBtF,SAAS,GAAGrJ,MAAM,CAAC2C,QAAQ,CAAC;EAC5BiM,UAAU;EACV;EACAC,aAAa;EACb;EACAC,OAAO,GAAG,IAAIvN,OAAO,CAAC,CAAC;EACvB;EACAwN,SAAS,GAAG/O,MAAM,CAACM,QAAQ,EAAE;IAAEuM,QAAQ,EAAE;EAAK,CAAC,CAAC;EAChDnI,WAAWA,CAAA,EAAG;IACV,MAAMiI,MAAM,GAAG3M,MAAM,CAACC,MAAM,CAAC;IAC7B,MAAM+O,QAAQ,GAAGhP,MAAM,CAACE,gBAAgB,CAAC,CAACqJ,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IACpEoD,MAAM,CAAC/B,iBAAiB,CAAC,MAAM;MAC3B,IAAI,IAAI,CAACvB,SAAS,CAACqB,SAAS,EAAE;QAC1B,MAAMuE,cAAc,GAAI9B,KAAK,IAAK,IAAI,CAAC2B,OAAO,CAACrH,IAAI,CAAC0F,KAAK,CAAC;QAC1D,IAAI,CAACyB,UAAU,GAAG,CACdI,QAAQ,CAACnE,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAEoE,cAAc,CAAC,EACnDD,QAAQ,CAACnE,MAAM,CAAC,QAAQ,EAAE,mBAAmB,EAAEoE,cAAc,CAAC,CACjE;MACL;MACA;MACA;MACA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAChF,SAAS,CAAC,MAAO,IAAI,CAAC2E,aAAa,GAAG,IAAK,CAAC;IAC9D,CAAC,CAAC;EACN;EACA7D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4D,UAAU,EAAE3D,OAAO,CAACkE,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACL,OAAO,CAAC3J,QAAQ,CAAC,CAAC;EAC3B;EACA;EACAoB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACsI,aAAa,EAAE;MACrB,IAAI,CAACO,mBAAmB,CAAC,CAAC;IAC9B;IACA,MAAMC,MAAM,GAAG;MAAEC,KAAK,EAAE,IAAI,CAACT,aAAa,CAACS,KAAK;MAAEC,MAAM,EAAE,IAAI,CAACV,aAAa,CAACU;IAAO,CAAC;IACrF;IACA,IAAI,CAAC,IAAI,CAAClG,SAAS,CAACqB,SAAS,EAAE;MAC3B,IAAI,CAACmE,aAAa,GAAG,IAAI;IAC7B;IACA,OAAOQ,MAAM;EACjB;EACA;EACAG,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,cAAc,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACvD,MAAM;MAAEJ,KAAK;MAAEC;IAAO,CAAC,GAAG,IAAI,CAAChJ,eAAe,CAAC,CAAC;IAChD,OAAO;MACHoH,GAAG,EAAE8B,cAAc,CAAC9B,GAAG;MACvBH,IAAI,EAAEiC,cAAc,CAACjC,IAAI;MACzBE,MAAM,EAAE+B,cAAc,CAAC9B,GAAG,GAAG4B,MAAM;MACnC9B,KAAK,EAAEgC,cAAc,CAACjC,IAAI,GAAG8B,KAAK;MAClCC,MAAM;MACND;IACJ,CAAC;EACL;EACA;EACAI,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA,IAAI,CAAC,IAAI,CAACrG,SAAS,CAACqB,SAAS,EAAE;MAC3B,OAAO;QAAEiD,GAAG,EAAE,CAAC;QAAEH,IAAI,EAAE;MAAE,CAAC;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMmC,QAAQ,GAAG,IAAI,CAACZ,SAAS;IAC/B,MAAMa,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAChC,MAAMC,eAAe,GAAGH,QAAQ,CAACG,eAAe;IAChD,MAAMC,YAAY,GAAGD,eAAe,CAACE,qBAAqB,CAAC,CAAC;IAC5D,MAAMrC,GAAG,GAAG,CAACoC,YAAY,CAACpC,GAAG,IACzBgC,QAAQ,CAACM,IAAI,CAAC7B,SAAS,IACvBwB,MAAM,CAACM,OAAO,IACdJ,eAAe,CAAC1B,SAAS,IACzB,CAAC;IACL,MAAMZ,IAAI,GAAG,CAACuC,YAAY,CAACvC,IAAI,IAC3BmC,QAAQ,CAACM,IAAI,CAAC5B,UAAU,IACxBuB,MAAM,CAACO,OAAO,IACdL,eAAe,CAACzB,UAAU,IAC1B,CAAC;IACL,OAAO;MAAEV,GAAG;MAAEH;IAAK,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;EACI0B,MAAMA,CAACkB,YAAY,GAAG1B,mBAAmB,EAAE;IACvC,OAAO0B,YAAY,GAAG,CAAC,GAAG,IAAI,CAACtB,OAAO,CAACzK,IAAI,CAACtC,SAAS,CAACqO,YAAY,CAAC,CAAC,GAAG,IAAI,CAACtB,OAAO;EACvF;EACA;EACAe,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACd,SAAS,CAACsB,WAAW,IAAIT,MAAM;EAC/C;EACA;EACAR,mBAAmBA,CAAA,EAAG;IAClB,MAAMQ,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAChC,IAAI,CAAChB,aAAa,GAAG,IAAI,CAACxF,SAAS,CAACqB,SAAS,GACvC;MAAE4E,KAAK,EAAEM,MAAM,CAACU,UAAU;MAAEf,MAAM,EAAEK,MAAM,CAACW;IAAY,CAAC,GACxD;MAAEjB,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;EACjC;EACA,OAAOvH,IAAI,YAAAwI,sBAAAtI,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyG,aAAa;EAAA;EAChH,OAAOxC,KAAK,kBA/a6ExM,EAAE,CAAAyM,kBAAA;IAAAC,KAAA,EA+aYsC,aAAa;IAAArC,OAAA,EAAbqC,aAAa,CAAA3G,IAAA;IAAAuE,UAAA,EAAc;EAAM;AAC5I;AACA;EAAA,QAAAlH,SAAA,oBAAAA,SAAA,KAjb6F1F,EAAE,CAAAmJ,iBAAA,CAibJ6F,aAAa,EAAc,CAAC;IAC3GtG,IAAI,EAAElI,UAAU;IAChB4I,IAAI,EAAE,CAAC;MAAEwD,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMkE,kBAAkB,GAAG,IAAI7Q,cAAc,CAAC,oBAAoB,CAAC;AACnE;AACA;AACA;AACA,MAAM8Q,oBAAoB,SAASlE,aAAa,CAAC;EAC7C9H,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;EACX;EACA;AACJ;AACA;AACA;AACA;EACIiM,mBAAmBA,CAACC,WAAW,EAAE;IAC7B,MAAMC,UAAU,GAAG,IAAI,CAACpE,UAAU,CAACT,aAAa;IAChD,OAAO4E,WAAW,KAAK,YAAY,GAAGC,UAAU,CAAC7C,WAAW,GAAG6C,UAAU,CAAChD,YAAY;EAC1F;EACA,OAAO7F,IAAI,YAAA8I,6BAAA5I,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwI,oBAAoB;EAAA;EACvH,OAAOvI,IAAI,kBAxc8ExI,EAAE,CAAAyI,iBAAA;IAAAC,IAAA,EAwcJqI,oBAAoB;IAAAlI,QAAA,GAxclB7I,EAAE,CAAAoR,0BAAA;EAAA;AAyc/F;AACA;EAAA,QAAA1L,SAAA,oBAAAA,SAAA,KA1c6F1F,EAAE,CAAAmJ,iBAAA,CA0cJ4H,oBAAoB,EAAc,CAAC;IAClHrI,IAAI,EAAEvI;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,SAASkR,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACzB,OAAOD,EAAE,CAAC7K,KAAK,IAAI8K,EAAE,CAAC9K,KAAK,IAAI6K,EAAE,CAAC5K,GAAG,IAAI6K,EAAE,CAAC7K,GAAG;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8K,gBAAgB,GAAG,OAAOC,qBAAqB,KAAK,WAAW,GAAGzP,uBAAuB,GAAGC,aAAa;AAC/G;AACA,MAAMyP,wBAAwB,SAASX,oBAAoB,CAAC;EACxDjE,UAAU,GAAGzM,MAAM,CAACI,UAAU,CAAC;EAC/BkR,kBAAkB,GAAGtR,MAAM,CAACO,iBAAiB,CAAC;EAC9CqH,eAAe,GAAG5H,MAAM,CAACiE,uBAAuB,EAAE;IAC9C4I,QAAQ,EAAE;EACd,CAAC,CAAC;EACF/C,UAAU,GAAG9J,MAAM,CAACyQ,kBAAkB,EAAE;IAAE5D,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC3DxD,SAAS,GAAGrJ,MAAM,CAAC2C,QAAQ,CAAC;EAC5B;EACA4O,gBAAgB,GAAG,IAAIhQ,OAAO,CAAC,CAAC;EAChC;EACAiQ,qBAAqB,GAAG,IAAIjQ,OAAO,CAAC,CAAC;EACrC;EACA,IAAIqP,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACa,YAAY;EAC5B;EACA,IAAIb,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACa,YAAY,KAAKb,WAAW,EAAE;MACnC,IAAI,CAACa,YAAY,GAAGb,WAAW;MAC/B,IAAI,CAACc,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACAD,YAAY,GAAG,UAAU;EACzB;AACJ;AACA;AACA;EACIE,UAAU,GAAG,KAAK;EAClB;EACA;EACA;EACA;EACA;EACAvN,mBAAmB,GAAG,IAAI3C,UAAU,CAAEkJ,QAAQ,IAAK,IAAI,CAAC/C,eAAe,CAACxD,mBAAmB,CAAC8F,SAAS,CAACtE,KAAK,IAAIgM,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACnF,MAAM,CAACoF,GAAG,CAAC,MAAMpH,QAAQ,CAAClD,IAAI,CAAC7B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1L;EACAoM,eAAe;EACf;EACAC,mBAAmB,GAAG,IAAI,CAACT,qBAAqB;EAChD;AACJ;AACA;EACIU,iBAAiB,GAAG,CAAC;EACrB;EACAC,kBAAkB,GAAG3R,MAAM,CAAC,EAAE,CAAC;EAC/B;EACA4R,mBAAmB,GAAG5R,MAAM,CAAC,EAAE,CAAC;EAChC;AACJ;AACA;AACA;EACI6R,yBAAyB;EACzB;EACAC,cAAc,GAAG;IAAElM,KAAK,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAE,CAAC;EACrC;EACAkM,WAAW,GAAG,CAAC;EACf;EACA1D,aAAa,GAAG,CAAC;EACjB;EACA2D,MAAM;EACN;EACAC,sBAAsB,GAAG,CAAC;EAC1B;AACJ;AACA;AACA;EACIC,kCAAkC,GAAG,KAAK;EAC1C;EACAC,yBAAyB,GAAG,KAAK;EACjC;EACAC,wBAAwB,GAAG,EAAE;EAC7B;EACAC,gBAAgB,GAAGnR,YAAY,CAACoR,KAAK;EACrCC,SAAS,GAAG/S,MAAM,CAACS,QAAQ,CAAC;EAC5BuS,YAAY,GAAG,KAAK;EACpBtO,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,MAAMuO,aAAa,GAAGjT,MAAM,CAAC2O,aAAa,CAAC;IAC3C,IAAI,CAAC,IAAI,CAAC/G,eAAe,KAAK,OAAOvC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1E,MAAMC,KAAK,CAAC,gFAAgF,CAAC;IACjG;IACA,IAAI,CAACuN,gBAAgB,GAAGI,aAAa,CAAC/D,MAAM,CAAC,CAAC,CAAChF,SAAS,CAAC,MAAM;MAC3D,IAAI,CAACgJ,iBAAiB,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAAC,IAAI,CAACpJ,UAAU,EAAE;MAClB;MACA,IAAI,CAAC2C,UAAU,CAACT,aAAa,CAACmH,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrE,IAAI,CAACtJ,UAAU,GAAG,IAAI;IAC1B;EACJ;EACAoD,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAAC,IAAI,CAAC7D,SAAS,CAACqB,SAAS,EAAE;MAC3B;IACJ;IACA,IAAI,IAAI,CAACZ,UAAU,KAAK,IAAI,EAAE;MAC1B,KAAK,CAACoD,QAAQ,CAAC,CAAC;IACpB;IACA;IACA;IACA;IACA;IACA,IAAI,CAACP,MAAM,CAAC/B,iBAAiB,CAAC,MAAMgH,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MAC7D,IAAI,CAACuB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACzL,eAAe,CAAC9C,MAAM,CAAC,IAAI,CAAC;MACjC,IAAI,CAACgF,UAAU,CACVG,eAAe,CAAC,CAAC,CACjB5F,IAAI;MACT;MACApC,SAAS,CAAC,IAAI,CAAC;MACf;MACA;MACA;MACAF,SAAS,CAAC,CAAC,EAAEoP,gBAAgB,CAAC;MAC9B;MACA;MACA;MACAjP,SAAS,CAAC,IAAI,CAAC6K,UAAU,CAAC,CAAC,CACtB7C,SAAS,CAAC,MAAM,IAAI,CAACtC,eAAe,CAACrC,iBAAiB,CAAC,CAAC,CAAC;MAC9D,IAAI,CAAC+N,0BAA0B,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;EACP;EACAtI,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9F,MAAM,CAAC,CAAC;IACb,IAAI,CAAC0C,eAAe,CAAC1C,MAAM,CAAC,CAAC;IAC7B;IACA,IAAI,CAACsM,qBAAqB,CAACrM,QAAQ,CAAC,CAAC;IACrC,IAAI,CAACoM,gBAAgB,CAACpM,QAAQ,CAAC,CAAC;IAChC,IAAI,CAAC0N,gBAAgB,CAACvI,WAAW,CAAC,CAAC;IACnC,IAAI,CAAC0I,YAAY,GAAG,IAAI;IACxB,KAAK,CAAChI,WAAW,CAAC,CAAC;EACvB;EACA;EACAlG,MAAMA,CAACyO,KAAK,EAAE;IACV,IAAI,IAAI,CAACf,MAAM,KAAK,OAAOnN,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAChE,MAAMC,KAAK,CAAC,+CAA+C,CAAC;IAChE;IACA;IACA;IACA;IACA,IAAI,CAACqH,MAAM,CAAC/B,iBAAiB,CAAC,MAAM;MAChC,IAAI,CAAC4H,MAAM,GAAGe,KAAK;MACnB,IAAI,CAACf,MAAM,CAACgB,UAAU,CAACnP,IAAI,CAACnC,SAAS,CAAC,IAAI,CAACqP,gBAAgB,CAAC,CAAC,CAACrH,SAAS,CAACuJ,IAAI,IAAI;QAC5E,MAAMC,SAAS,GAAGD,IAAI,CAACE,MAAM;QAC7B,IAAID,SAAS,KAAK,IAAI,CAACnB,WAAW,EAAE;UAChC,IAAI,CAACA,WAAW,GAAGmB,SAAS;UAC5B,IAAI,CAAC9L,eAAe,CAACpC,mBAAmB,CAAC,CAAC;QAC9C;QACA,IAAI,CAACoO,kBAAkB,CAAC,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACA1O,MAAMA,CAAA,EAAG;IACL,IAAI,CAACsN,MAAM,GAAG,IAAI;IAClB,IAAI,CAACjB,gBAAgB,CAAC9J,IAAI,CAAC,CAAC;EAChC;EACA;EACAzB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACuM,WAAW;EAC3B;EACA;EACAhM,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACsI,aAAa;EAC7B;EACA;EACA;EACA;EACA;EACA;EACA3I,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACoM,cAAc;EAC9B;EACAuB,yCAAyCA,CAACvF,IAAI,EAAE;IAC5C,OAAO,IAAI,CAACvC,aAAa,CAAC,CAAC,CAACC,aAAa,CAACgE,qBAAqB,CAAC,CAAC,CAAC1B,IAAI,CAAC;EAC3E;EACA;AACJ;AACA;AACA;EACIvI,mBAAmBA,CAAC+N,IAAI,EAAE;IACtB,IAAI,IAAI,CAAC5B,iBAAiB,KAAK4B,IAAI,EAAE;MACjC,IAAI,CAAC5B,iBAAiB,GAAG4B,IAAI;MAC7B,IAAI,CAACpC,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAC4B,0BAA0B,CAAC,CAAC;IACrC;EACJ;EACA;EACA/L,gBAAgBA,CAACwM,KAAK,EAAE;IACpB,IAAI,CAAC/C,WAAW,CAAC,IAAI,CAACsB,cAAc,EAAEyB,KAAK,CAAC,EAAE;MAC1C,IAAI,IAAI,CAACpC,UAAU,EAAE;QACjBoC,KAAK,GAAG;UAAE3N,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAEQ,IAAI,CAACG,GAAG,CAAC,IAAI,CAACsL,cAAc,CAACjM,GAAG,EAAE0N,KAAK,CAAC1N,GAAG;QAAE,CAAC;MAC3E;MACA,IAAI,CAACmL,qBAAqB,CAAC/J,IAAI,CAAE,IAAI,CAAC6K,cAAc,GAAGyB,KAAM,CAAC;MAC9D,IAAI,CAACT,0BAA0B,CAAC,MAAM,IAAI,CAAC1L,eAAe,CAACnC,iBAAiB,CAAC,CAAC,CAAC;IACnF;EACJ;EACA;AACJ;AACA;EACIuO,+BAA+BA,CAAA,EAAG;IAC9B,OAAO,IAAI,CAACtB,kCAAkC,GAAG,IAAI,GAAG,IAAI,CAACD,sBAAsB;EACvF;EACA;AACJ;AACA;AACA;EACIjL,wBAAwBA,CAACyM,MAAM,EAAEC,EAAE,GAAG,UAAU,EAAE;IAC9C;IACAD,MAAM,GAAG,IAAI,CAACtC,UAAU,IAAIuC,EAAE,KAAK,UAAU,GAAG,CAAC,GAAGD,MAAM;IAC1D;IACA;IACA,MAAM1G,KAAK,GAAG,IAAI,CAACX,GAAG,IAAI,IAAI,CAACA,GAAG,CAAC9E,KAAK,IAAI,KAAK;IACjD,MAAMqM,YAAY,GAAG,IAAI,CAACvD,WAAW,IAAI,YAAY;IACrD,MAAMwD,IAAI,GAAGD,YAAY,GAAG,GAAG,GAAG,GAAG;IACrC,MAAME,aAAa,GAAGF,YAAY,IAAI5G,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IACpD,IAAI+G,SAAS,GAAG,YAAYF,IAAI,IAAIG,MAAM,CAACF,aAAa,GAAGJ,MAAM,CAAC,KAAK;IACvE,IAAI,CAACxB,sBAAsB,GAAGwB,MAAM;IACpC,IAAIC,EAAE,KAAK,QAAQ,EAAE;MACjBI,SAAS,IAAI,aAAaF,IAAI,SAAS;MACvC;MACA;MACA;MACA,IAAI,CAAC1B,kCAAkC,GAAG,IAAI;IAClD;IACA,IAAI,IAAI,CAACL,yBAAyB,IAAIiC,SAAS,EAAE;MAC7C;MACA;MACA,IAAI,CAACjC,yBAAyB,GAAGiC,SAAS;MAC1C,IAAI,CAAChB,0BAA0B,CAAC,MAAM;QAClC,IAAI,IAAI,CAACZ,kCAAkC,EAAE;UACzC,IAAI,CAACD,sBAAsB,IAAI,IAAI,CAAC+B,0BAA0B,CAAC,CAAC;UAChE,IAAI,CAAC9B,kCAAkC,GAAG,KAAK;UAC/C,IAAI,CAAClL,wBAAwB,CAAC,IAAI,CAACiL,sBAAsB,CAAC;QAC9D,CAAC,MACI;UACD,IAAI,CAAC7K,eAAe,CAAClC,uBAAuB,CAAC,CAAC;QAClD;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,cAAcA,CAACmO,MAAM,EAAEpO,QAAQ,GAAG,MAAM,EAAE;IACtC,MAAMwH,OAAO,GAAG;MAAExH;IAAS,CAAC;IAC5B,IAAI,IAAI,CAAC+K,WAAW,KAAK,YAAY,EAAE;MACnCvD,OAAO,CAACjH,KAAK,GAAG6N,MAAM;IAC1B,CAAC,MACI;MACD5G,OAAO,CAACM,GAAG,GAAGsG,MAAM;IACxB;IACA,IAAI,CAACnK,UAAU,CAACsD,QAAQ,CAACC,OAAO,CAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;EACI1H,aAAaA,CAACC,KAAK,EAAEC,QAAQ,GAAG,MAAM,EAAE;IACpC,IAAI,CAAC+B,eAAe,CAACjC,aAAa,CAACC,KAAK,EAAEC,QAAQ,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;EACIa,mBAAmBA,CAAC4H,IAAI,EAAE;IACtB;IACA,IAAI5H,mBAAmB;IACvB,IAAI,IAAI,CAACoD,UAAU,IAAI,IAAI,EAAE;MACzBpD,mBAAmB,GAAI+N,KAAK,IAAK,KAAK,CAAC/N,mBAAmB,CAAC+N,KAAK,CAAC;IACrE,CAAC,MACI;MACD/N,mBAAmB,GAAI+N,KAAK,IAAK,IAAI,CAAC3K,UAAU,CAACpD,mBAAmB,CAAC+N,KAAK,CAAC;IAC/E;IACA,OAAO5N,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEN,mBAAmB,CAAC4H,IAAI,KAAK,IAAI,CAACsC,WAAW,KAAK,YAAY,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,GACjG,IAAI,CAAC8D,qBAAqB,CAAC,CAAC,CAAC;EACrC;EACA;AACJ;AACA;AACA;EACIA,qBAAqBA,CAACpG,IAAI,EAAE;IACxB,IAAIqG,QAAQ;IACZ,MAAMpG,IAAI,GAAG,MAAM;IACnB,MAAMC,KAAK,GAAG,OAAO;IACrB,MAAMjB,KAAK,GAAG,IAAI,CAACX,GAAG,EAAE9E,KAAK,IAAI,KAAK;IACtC,IAAIwG,IAAI,IAAI,OAAO,EAAE;MACjBqG,QAAQ,GAAGpH,KAAK,GAAGiB,KAAK,GAAGD,IAAI;IACnC,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;MACpBqG,QAAQ,GAAGpH,KAAK,GAAGgB,IAAI,GAAGC,KAAK;IACnC,CAAC,MACI,IAAIF,IAAI,EAAE;MACXqG,QAAQ,GAAGrG,IAAI;IACnB,CAAC,MACI;MACDqG,QAAQ,GAAG,IAAI,CAAC/D,WAAW,KAAK,YAAY,GAAG,MAAM,GAAG,KAAK;IACjE;IACA,MAAMgE,kBAAkB,GAAG,IAAI,CAAC9K,UAAU,CAAC+J,yCAAyC,CAACc,QAAQ,CAAC;IAC9F,MAAME,kBAAkB,GAAG,IAAI,CAACpI,UAAU,CAACT,aAAa,CAACgE,qBAAqB,CAAC,CAAC,CAAC2E,QAAQ,CAAC;IAC1F,OAAOE,kBAAkB,GAAGD,kBAAkB;EAClD;EACA;EACAJ,0BAA0BA,CAAA,EAAG;IACzB,MAAMM,SAAS,GAAG,IAAI,CAAC9C,eAAe,CAAChG,aAAa;IACpD,OAAO,IAAI,CAAC4E,WAAW,KAAK,YAAY,GAAGkE,SAAS,CAACC,WAAW,GAAGD,SAAS,CAACE,YAAY;EAC7F;EACA;AACJ;AACA;AACA;EACIC,gBAAgBA,CAAClB,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACvB,MAAM,EAAE;MACd,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACA,MAAM,CAACyC,gBAAgB,CAAClB,KAAK,EAAE,IAAI,CAACnD,WAAW,CAAC;EAChE;EACA;EACAsC,iBAAiBA,CAAA,EAAG;IAChB;IACA,IAAI,CAACG,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACzL,eAAe,CAACpC,mBAAmB,CAAC,CAAC;EAC9C;EACA;EACA6N,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACxE,aAAa,GAAG,IAAI,CAAC/E,UAAU,CAAC6G,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAAC;EAC9E;EACA;EACA0C,0BAA0BA,CAAC4B,QAAQ,EAAE;IACjC,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACtC,wBAAwB,CAAChH,IAAI,CAACsJ,QAAQ,CAAC;IAChD;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACvC,yBAAyB,EAAE;MACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;MACrC,IAAI,CAAChG,MAAM,CAAC/B,iBAAiB,CAAC,MAAMgH,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC7D,IAAI,CAAC8B,kBAAkB,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC;IACP;EACJ;EACA;EACAA,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACZ,YAAY,EAAE;MACnB;IACJ;IACA,IAAI,CAACrG,MAAM,CAACoF,GAAG,CAAC,MAAM;MAClB;MACA;MACA;MACA,IAAI,CAACT,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;MACtC;MACA;MACA;MACA;MACA,IAAI,CAACnD,eAAe,CAAChG,aAAa,CAACoJ,KAAK,CAACd,SAAS,GAAG,IAAI,CAACjC,yBAAyB;MACnF3R,eAAe,CAAC,MAAM;QAClB,IAAI,CAACiS,yBAAyB,GAAG,KAAK;QACtC,MAAM0C,uBAAuB,GAAG,IAAI,CAACzC,wBAAwB;QAC7D,IAAI,CAACA,wBAAwB,GAAG,EAAE;QAClC,KAAK,MAAM0C,EAAE,IAAID,uBAAuB,EAAE;UACtCC,EAAE,CAAC,CAAC;QACR;MACJ,CAAC,EAAE;QAAEC,QAAQ,EAAE,IAAI,CAACxC;MAAU,CAAC,CAAC;IACpC,CAAC,CAAC;EACN;EACA;EACArB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACU,mBAAmB,CAACpI,GAAG,CAAC,IAAI,CAAC4G,WAAW,KAAK,YAAY,GAAG,EAAE,GAAG,GAAG,IAAI,CAACsB,iBAAiB,IAAI,CAAC;IACpG,IAAI,CAACC,kBAAkB,CAACnI,GAAG,CAAC,IAAI,CAAC4G,WAAW,KAAK,YAAY,GAAG,GAAG,IAAI,CAACsB,iBAAiB,IAAI,GAAG,EAAE,CAAC;EACvG;EACA,OAAOlK,IAAI,YAAAwN,iCAAAtN,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmJ,wBAAwB;EAAA;EAC3H,OAAOoE,IAAI,kBAl1B8E9V,EAAE,CAAA+V,iBAAA;IAAArN,IAAA,EAk1BJgJ,wBAAwB;IAAA/I,SAAA;IAAAqN,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAl1BtBlW,EAAE,CAAAoW,WAAA,CAAA1S,GAAA;MAAA;MAAA,IAAAwS,EAAA;QAAA,IAAAG,EAAA;QAAFrW,EAAE,CAAAsW,cAAA,CAAAD,EAAA,GAAFrW,EAAE,CAAAuW,WAAA,QAAAJ,GAAA,CAAA9D,eAAA,GAAAgE,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,sCAAAV,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFlW,EAAE,CAAA6W,WAAA,8CAAAV,GAAA,CAAAlF,WAAA,KAk1BY,YAAO,CAAC,4CAAAkF,GAAA,CAAAlF,WAAA,KAAR,YAAO,CAAC;MAAA;IAAA;IAAArI,MAAA;MAAAqI,WAAA;MAAAe,UAAA,kCAA8IhR,gBAAgB;IAAA;IAAA8V,OAAA;MAAArS,mBAAA;IAAA;IAAAoE,QAAA,GAl1BpL7I,EAAE,CAAA8I,kBAAA,CAk1B4e,CAC/jB;MACIC,OAAO,EAAE8D,aAAa;MACtB7D,UAAU,EAAEA,CAAC+N,iBAAiB,EAAE3R,QAAQ,KAAK2R,iBAAiB,IAAI3R,QAAQ;MAC1E6D,IAAI,EAAE,CAAC,CAAC,IAAIhI,QAAQ,CAAC,CAAC,EAAE,IAAIC,MAAM,CAAC4P,kBAAkB,CAAC,CAAC,EAAEY,wBAAwB;IACrF,CAAC,CACJ,GAx1BoF1R,EAAE,CAAAoR,0BAAA;IAAA4F,kBAAA,EAAArT,GAAA;IAAAsT,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAnB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFlW,EAAE,CAAAsX,eAAA;QAAFtX,EAAE,CAAAuX,cAAA,eAw1BoR,CAAC;QAx1BvRvX,EAAE,CAAAwX,YAAA,EAw1BiT,CAAC;QAx1BpTxX,EAAE,CAAAyX,YAAA,CAw1ByT,CAAC;QAx1B5TzX,EAAE,CAAA0X,SAAA,YAw1BknB,CAAC;MAAA;MAAA,IAAAxB,EAAA;QAx1BrnBlW,EAAE,CAAA2X,SAAA,EAw1BkkB,CAAC;QAx1BrkB3X,EAAE,CAAA4X,WAAA,UAAAzB,GAAA,CAAA3D,kBAAA,EAw1BkkB,CAAC,WAAA2D,GAAA,CAAA1D,mBAAA,EAAwC,CAAC;MAAA;IAAA;IAAAoF,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC3sB;AACA;EAAA,QAAArS,SAAA,oBAAAA,SAAA,KA11B6F1F,EAAE,CAAAmJ,iBAAA,CA01BJuI,wBAAwB,EAAc,CAAC;IACtHhJ,IAAI,EAAEvH,SAAS;IACfiI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAE2O,IAAI,EAAE;QAC5C,OAAO,EAAE,6BAA6B;QACtC,mDAAmD,EAAE,8BAA8B;QACnF,iDAAiD,EAAE;MACvD,CAAC;MAAEF,aAAa,EAAE1W,iBAAiB,CAAC6W,IAAI;MAAEF,eAAe,EAAE1W,uBAAuB,CAAC6W,MAAM;MAAE5O,SAAS,EAAE,CAClG;QACIP,OAAO,EAAE8D,aAAa;QACtB7D,UAAU,EAAEA,CAAC+N,iBAAiB,EAAE3R,QAAQ,KAAK2R,iBAAiB,IAAI3R,QAAQ;QAC1E6D,IAAI,EAAE,CAAC,CAAC,IAAIhI,QAAQ,CAAC,CAAC,EAAE,IAAIC,MAAM,CAAC4P,kBAAkB,CAAC,CAAC,EAAEY,wBAAwB;MACrF,CAAC,CACJ;MAAE0F,QAAQ,EAAE,0hBAA0hB;MAAES,MAAM,EAAE,CAAC,upDAAupD;IAAE,CAAC;EACxtE,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE5G,WAAW,EAAE,CAAC;MACtDvI,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAE4R,UAAU,EAAE,CAAC;MACbtJ,IAAI,EAAEtI,KAAK;MACXgJ,IAAI,EAAE,CAAC;QAAEuL,SAAS,EAAE3T;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyD,mBAAmB,EAAE,CAAC;MACtBiE,IAAI,EAAEpH;IACV,CAAC,CAAC;IAAE+Q,eAAe,EAAE,CAAC;MAClB3J,IAAI,EAAEnH,SAAS;MACf6H,IAAI,EAAE,CAAC,gBAAgB,EAAE;QAAE+O,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,SAASC,SAASA,CAACnH,WAAW,EAAEoH,SAAS,EAAEC,IAAI,EAAE;EAC7C,MAAM3K,EAAE,GAAG2K,IAAI;EACf,IAAI,CAAC3K,EAAE,CAAC0C,qBAAqB,EAAE;IAC3B,OAAO,CAAC;EACZ;EACA,MAAMkI,IAAI,GAAG5K,EAAE,CAAC0C,qBAAqB,CAAC,CAAC;EACvC,IAAIY,WAAW,KAAK,YAAY,EAAE;IAC9B,OAAOoH,SAAS,KAAK,OAAO,GAAGE,IAAI,CAAC1K,IAAI,GAAG0K,IAAI,CAACzK,KAAK;EACzD;EACA,OAAOuK,SAAS,KAAK,OAAO,GAAGE,IAAI,CAACvK,GAAG,GAAGuK,IAAI,CAACxK,MAAM;AACzD;AACA;AACA;AACA;AACA;AACA,MAAMyK,eAAe,CAAC;EAClBC,iBAAiB,GAAGpY,MAAM,CAACmB,gBAAgB,CAAC;EAC5CkX,SAAS,GAAGrY,MAAM,CAACoB,WAAW,CAAC;EAC/BkX,QAAQ,GAAGtY,MAAM,CAACqB,eAAe,CAAC;EAClCkX,aAAa,GAAGvY,MAAM,CAAC0D,uBAAuB,CAAC;EAC/CY,SAAS,GAAGtE,MAAM,CAACqR,wBAAwB,EAAE;IAAEmH,QAAQ,EAAE;EAAK,CAAC,CAAC;EAChE;EACAC,UAAU,GAAG,IAAIlX,OAAO,CAAC,CAAC;EAC1B;EACAmX,kBAAkB,GAAG,IAAInX,OAAO,CAAC,CAAC;EAClC;EACA,IAAIoX,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAAC7Q,KAAK,EAAE;IACvB,IAAI,CAAC8Q,gBAAgB,GAAG9Q,KAAK;IAC7B,IAAI9D,YAAY,CAAC8D,KAAK,CAAC,EAAE;MACrB,IAAI,CAAC4Q,kBAAkB,CAACjR,IAAI,CAACK,KAAK,CAAC;IACvC,CAAC,MACI;MACD;MACA,IAAI,CAAC4Q,kBAAkB,CAACjR,IAAI,CAAC,IAAI7D,eAAe,CAAC/B,YAAY,CAACiG,KAAK,CAAC,GAAGA,KAAK,GAAG+Q,KAAK,CAACvK,IAAI,CAACxG,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5G;EACJ;EACA8Q,gBAAgB;EAChB;AACJ;AACA;AACA;EACI,IAAIE,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACC,qBAAqB;EACrC;EACA,IAAID,oBAAoBA,CAACxD,EAAE,EAAE;IACzB,IAAI,CAAC0D,YAAY,GAAG,IAAI;IACxB,IAAI,CAACD,qBAAqB,GAAGzD,EAAE,GACzB,CAAC1P,KAAK,EAAEqT,IAAI,KAAK3D,EAAE,CAAC1P,KAAK,IAAI,IAAI,CAAC0M,cAAc,GAAG,IAAI,CAACA,cAAc,CAAClM,KAAK,GAAG,CAAC,CAAC,EAAE6S,IAAI,CAAC,GACxFlO,SAAS;EACnB;EACAgO,qBAAqB;EACrB;EACA,IAAIG,qBAAqBA,CAACpR,KAAK,EAAE;IAC7B,IAAIA,KAAK,EAAE;MACP,IAAI,CAACkR,YAAY,GAAG,IAAI;MACxB,IAAI,CAACX,SAAS,GAAGvQ,KAAK;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIqR,8BAA8BA,CAAA,EAAG;IACjC,OAAO,IAAI,CAACZ,aAAa,CAACa,aAAa;EAC3C;EACA,IAAID,8BAA8BA,CAACrF,IAAI,EAAE;IACrC,IAAI,CAACyE,aAAa,CAACa,aAAa,GAAG7W,oBAAoB,CAACuR,IAAI,CAAC;EACjE;EACA;EACAN,UAAU,GAAG,IAAI,CAACkF,kBAAkB,CAACrU,IAAI;EACzC;EACApC,SAAS,CAAC,IAAI,CAAC;EACf;EACAE,QAAQ,CAAC,CAAC;EACV;EACA;EACA;EACAC,SAAS,CAAC,CAAC,CAACiX,IAAI,EAAEC,GAAG,CAAC,KAAK,IAAI,CAACC,iBAAiB,CAACF,IAAI,EAAEC,GAAG,CAAC,CAAC;EAC7D;EACAjX,WAAW,CAAC,CAAC,CAAC,CAAC;EACf;EACAmX,OAAO,GAAG,IAAI;EACd;EACAC,KAAK;EACL;EACAC,cAAc;EACd;EACApH,cAAc;EACd;EACA0G,YAAY,GAAG,KAAK;EACpBjM,UAAU,GAAG,IAAIxL,OAAO,CAAC,CAAC;EAC1BmD,WAAWA,CAAA,EAAG;IACV,MAAMiI,MAAM,GAAG3M,MAAM,CAACC,MAAM,CAAC;IAC7B,IAAI,CAACuT,UAAU,CAACtJ,SAAS,CAACuJ,IAAI,IAAI;MAC9B,IAAI,CAACgG,KAAK,GAAGhG,IAAI;MACjB,IAAI,CAACkG,qBAAqB,CAAC,CAAC;IAChC,CAAC,CAAC;IACF,IAAI,CAACrV,SAAS,CAAC2N,mBAAmB,CAAC5N,IAAI,CAACnC,SAAS,CAAC,IAAI,CAAC6K,UAAU,CAAC,CAAC,CAAC7C,SAAS,CAAC6J,KAAK,IAAI;MACnF,IAAI,CAACzB,cAAc,GAAGyB,KAAK;MAC3B,IAAI,IAAI,CAAC0E,UAAU,CAACmB,SAAS,CAACjG,MAAM,EAAE;QAClChH,MAAM,CAACoF,GAAG,CAAC,MAAM,IAAI,CAAC0G,UAAU,CAAChR,IAAI,CAAC,IAAI,CAAC6K,cAAc,CAAC,CAAC;MAC/D;MACA,IAAI,CAACqH,qBAAqB,CAAC,CAAC;IAChC,CAAC,CAAC;IACF,IAAI,CAACrV,SAAS,CAACQ,MAAM,CAAC,IAAI,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACImQ,gBAAgBA,CAAClB,KAAK,EAAEnD,WAAW,EAAE;IACjC,IAAImD,KAAK,CAAC3N,KAAK,IAAI2N,KAAK,CAAC1N,GAAG,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,IAAI,CAAC0N,KAAK,CAAC3N,KAAK,GAAG,IAAI,CAACkM,cAAc,CAAClM,KAAK,IAAI2N,KAAK,CAAC1N,GAAG,GAAG,IAAI,CAACiM,cAAc,CAACjM,GAAG,MAC9E,OAAOhB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMC,KAAK,CAAC,0DAA0D,CAAC;IAC3E;IACA;IACA,MAAMuU,kBAAkB,GAAG9F,KAAK,CAAC3N,KAAK,GAAG,IAAI,CAACkM,cAAc,CAAClM,KAAK;IAClE;IACA,MAAM0T,QAAQ,GAAG/F,KAAK,CAAC1N,GAAG,GAAG0N,KAAK,CAAC3N,KAAK;IACxC;IACA;IACA,IAAI2T,SAAS;IACb,IAAIC,QAAQ;IACZ;IACA,KAAK,IAAIjW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+V,QAAQ,EAAE/V,CAAC,EAAE,EAAE;MAC/B,MAAMkW,IAAI,GAAG,IAAI,CAAC7B,iBAAiB,CAAC/N,GAAG,CAACtG,CAAC,GAAG8V,kBAAkB,CAAC;MAC/D,IAAII,IAAI,IAAIA,IAAI,CAACC,SAAS,CAACvG,MAAM,EAAE;QAC/BoG,SAAS,GAAGC,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;QACxC;MACJ;IACJ;IACA;IACA,KAAK,IAAInW,CAAC,GAAG+V,QAAQ,GAAG,CAAC,EAAE/V,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpC,MAAMkW,IAAI,GAAG,IAAI,CAAC7B,iBAAiB,CAAC/N,GAAG,CAACtG,CAAC,GAAG8V,kBAAkB,CAAC;MAC/D,IAAII,IAAI,IAAIA,IAAI,CAACC,SAAS,CAACvG,MAAM,EAAE;QAC/BqG,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACD,IAAI,CAACC,SAAS,CAACvG,MAAM,GAAG,CAAC,CAAC;QACpD;MACJ;IACJ;IACA,OAAOoG,SAAS,IAAIC,QAAQ,GACtBjC,SAAS,CAACnH,WAAW,EAAE,KAAK,EAAEoJ,QAAQ,CAAC,GAAGjC,SAAS,CAACnH,WAAW,EAAE,OAAO,EAAEmJ,SAAS,CAAC,GACpF,CAAC;EACX;EACAI,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACX,OAAO,IAAI,IAAI,CAACR,YAAY,EAAE;MACnC;MACA;MACA;MACA,MAAMoB,OAAO,GAAG,IAAI,CAACZ,OAAO,CAACa,IAAI,CAAC,IAAI,CAACX,cAAc,CAAC;MACtD,IAAI,CAACU,OAAO,EAAE;QACV,IAAI,CAACE,cAAc,CAAC,CAAC;MACzB,CAAC,MACI;QACD,IAAI,CAACC,aAAa,CAACH,OAAO,CAAC;MAC/B;MACA,IAAI,CAACpB,YAAY,GAAG,KAAK;IAC7B;EACJ;EACAhO,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC1G,SAAS,CAACY,MAAM,CAAC,CAAC;IACvB,IAAI,CAACwT,kBAAkB,CAACjR,IAAI,CAACsD,SAAS,CAAC;IACvC,IAAI,CAAC2N,kBAAkB,CAACvT,QAAQ,CAAC,CAAC;IAClC,IAAI,CAACsT,UAAU,CAACtT,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAAC4H,UAAU,CAACtF,IAAI,CAAC,CAAC;IACtB,IAAI,CAACsF,UAAU,CAAC5H,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACoT,aAAa,CAACrT,MAAM,CAAC,CAAC;EAC/B;EACA;EACAyU,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACrH,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,CAACoH,cAAc,GAAG,IAAI,CAACD,KAAK,CAACe,KAAK,CAAC,IAAI,CAAClI,cAAc,CAAClM,KAAK,EAAE,IAAI,CAACkM,cAAc,CAACjM,GAAG,CAAC;IAC1F,IAAI,CAAC,IAAI,CAACmT,OAAO,EAAE;MACf;MACA;MACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAAClB,QAAQ,CAACmC,IAAI,CAAC,IAAI,CAACf,cAAc,CAAC,CAACgB,MAAM,CAAC,CAAC9U,KAAK,EAAEqT,IAAI,KAAK;QAC3E,OAAO,IAAI,CAACH,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAAClT,KAAK,EAAEqT,IAAI,CAAC,GAAGA,IAAI;MACpF,CAAC,CAAC;IACN;IACA,IAAI,CAACD,YAAY,GAAG,IAAI;EAC5B;EACA;EACAO,iBAAiBA,CAACoB,KAAK,EAAEC,KAAK,EAAE;IAC5B,IAAID,KAAK,EAAE;MACPA,KAAK,CAACE,UAAU,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,CAAC7B,YAAY,GAAG,IAAI;IACxB,OAAO4B,KAAK,GAAGA,KAAK,CAACE,OAAO,CAAC,IAAI,CAAC,GAAGtZ,EAAE,CAAC,CAAC;EAC7C;EACA;EACA8Y,cAAcA,CAAA,EAAG;IACb,MAAMS,KAAK,GAAG,IAAI,CAACtB,KAAK,CAAC9F,MAAM;IAC/B,IAAI5P,CAAC,GAAG,IAAI,CAACqU,iBAAiB,CAACzE,MAAM;IACrC,OAAO5P,CAAC,EAAE,EAAE;MACR,MAAMkW,IAAI,GAAG,IAAI,CAAC7B,iBAAiB,CAAC/N,GAAG,CAACtG,CAAC,CAAC;MAC1CkW,IAAI,CAACe,OAAO,CAACpV,KAAK,GAAG,IAAI,CAAC0M,cAAc,CAAClM,KAAK,GAAGrC,CAAC;MAClDkW,IAAI,CAACe,OAAO,CAACD,KAAK,GAAGA,KAAK;MAC1B,IAAI,CAACE,gCAAgC,CAAChB,IAAI,CAACe,OAAO,CAAC;MACnDf,IAAI,CAACiB,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;EACAX,aAAaA,CAACH,OAAO,EAAE;IACnB,IAAI,CAAC7B,aAAa,CAAC4C,YAAY,CAACf,OAAO,EAAE,IAAI,CAAChC,iBAAiB,EAAE,CAACgD,MAAM,EAAEC,sBAAsB,EAAEC,YAAY,KAAK,IAAI,CAACC,oBAAoB,CAACH,MAAM,EAAEE,YAAY,CAAC,EAAEF,MAAM,IAAIA,MAAM,CAACnC,IAAI,CAAC;IAC1L;IACAmB,OAAO,CAACoB,qBAAqB,CAAEJ,MAAM,IAAK;MACtC,MAAMnB,IAAI,GAAG,IAAI,CAAC7B,iBAAiB,CAAC/N,GAAG,CAAC+Q,MAAM,CAACE,YAAY,CAAC;MAC5DrB,IAAI,CAACe,OAAO,CAACS,SAAS,GAAGL,MAAM,CAACnC,IAAI;IACxC,CAAC,CAAC;IACF;IACA,MAAM8B,KAAK,GAAG,IAAI,CAACtB,KAAK,CAAC9F,MAAM;IAC/B,IAAI5P,CAAC,GAAG,IAAI,CAACqU,iBAAiB,CAACzE,MAAM;IACrC,OAAO5P,CAAC,EAAE,EAAE;MACR,MAAMkW,IAAI,GAAG,IAAI,CAAC7B,iBAAiB,CAAC/N,GAAG,CAACtG,CAAC,CAAC;MAC1CkW,IAAI,CAACe,OAAO,CAACpV,KAAK,GAAG,IAAI,CAAC0M,cAAc,CAAClM,KAAK,GAAGrC,CAAC;MAClDkW,IAAI,CAACe,OAAO,CAACD,KAAK,GAAGA,KAAK;MAC1B,IAAI,CAACE,gCAAgC,CAAChB,IAAI,CAACe,OAAO,CAAC;IACvD;EACJ;EACA;EACAC,gCAAgCA,CAACD,OAAO,EAAE;IACtCA,OAAO,CAAC7E,KAAK,GAAG6E,OAAO,CAACpV,KAAK,KAAK,CAAC;IACnCoV,OAAO,CAACU,IAAI,GAAGV,OAAO,CAACpV,KAAK,KAAKoV,OAAO,CAACD,KAAK,GAAG,CAAC;IAClDC,OAAO,CAACW,IAAI,GAAGX,OAAO,CAACpV,KAAK,GAAG,CAAC,KAAK,CAAC;IACtCoV,OAAO,CAACY,GAAG,GAAG,CAACZ,OAAO,CAACW,IAAI;EAC/B;EACAJ,oBAAoBA,CAACH,MAAM,EAAExV,KAAK,EAAE;IAChC;IACA;IACA;IACA;IACA,OAAO;MACHiW,WAAW,EAAE,IAAI,CAACxD,SAAS;MAC3B2C,OAAO,EAAE;QACLS,SAAS,EAAEL,MAAM,CAACnC,IAAI;QACtB;QACA;QACAN,eAAe,EAAE,IAAI,CAACC,gBAAgB;QACtChT,KAAK,EAAE,CAAC,CAAC;QACTmV,KAAK,EAAE,CAAC,CAAC;QACT5E,KAAK,EAAE,KAAK;QACZuF,IAAI,EAAE,KAAK;QACXE,GAAG,EAAE,KAAK;QACVD,IAAI,EAAE;MACV,CAAC;MACD/V;IACJ,CAAC;EACL;EACA,OAAOkW,sBAAsBA,CAACC,SAAS,EAAEf,OAAO,EAAE;IAC9C,OAAO,IAAI;EACf;EACA,OAAOhT,IAAI,YAAAgU,wBAAA9T,iBAAA;IAAA,YAAAA,iBAAA,IAAwFiQ,eAAe;EAAA;EAClH,OAAOhQ,IAAI,kBAxnC8ExI,EAAE,CAAAyI,iBAAA;IAAAC,IAAA,EAwnCJ8P,eAAe;IAAA7P,SAAA;IAAAC,MAAA;MAAAoQ,eAAA;MAAAG,oBAAA;MAAAI,qBAAA;MAAAC,8BAAA;IAAA;IAAA3Q,QAAA,GAxnCb7I,EAAE,CAAA8I,kBAAA,CAwnC0S,CAAC;MAAEC,OAAO,EAAEhF,uBAAuB;MAAEuY,QAAQ,EAAEnY;IAA6B,CAAC,CAAC;EAAA;AACvd;AACA;EAAA,QAAAuB,SAAA,oBAAAA,SAAA,KA1nC6F1F,EAAE,CAAAmJ,iBAAA,CA0nCJqP,eAAe,EAAc,CAAC;IAC7G9P,IAAI,EAAEvI,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCAAkC;MAC5CC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAEhF,uBAAuB;QAAEuY,QAAQ,EAAEnY;MAA6B,CAAC;IAC5F,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE6U,eAAe,EAAE,CAAC;MAC1DtQ,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAE+Y,oBAAoB,EAAE,CAAC;MACvBzQ,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEmZ,qBAAqB,EAAE,CAAC;MACxB7Q,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEoZ,8BAA8B,EAAE,CAAC;MACjC9Q,IAAI,EAAEtI;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMmc,2BAA2B,SAASxL,oBAAoB,CAAC;EAC3DhM,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;EACX;EACAmP,yCAAyCA,CAACvF,IAAI,EAAE;IAC5C,OAAQ,IAAI,CAACvC,aAAa,CAAC,CAAC,CAACC,aAAa,CAACgE,qBAAqB,CAAC,CAAC,CAAC1B,IAAI,CAAC,GACpE,IAAI,CAAC5H,mBAAmB,CAAC4H,IAAI,CAAC;EACtC;EACA,OAAOtG,IAAI,YAAAmU,oCAAAjU,iBAAA;IAAA,YAAAA,iBAAA,IAAwFgU,2BAA2B;EAAA;EAC9H,OAAO/T,IAAI,kBAtpC8ExI,EAAE,CAAAyI,iBAAA;IAAAC,IAAA,EAspCJ6T,2BAA2B;IAAA5T,SAAA;IAAA8N,SAAA;IAAA5N,QAAA,GAtpCzB7I,EAAE,CAAA8I,kBAAA,CAspCsJ,CAAC;MAAEC,OAAO,EAAE+H,kBAAkB;MAAE2L,WAAW,EAAEF;IAA4B,CAAC,CAAC,GAtpCnOvc,EAAE,CAAAoR,0BAAA;EAAA;AAupC/F;AACA;EAAA,QAAA1L,SAAA,oBAAAA,SAAA,KAxpC6F1F,EAAE,CAAAmJ,iBAAA,CAwpCJoT,2BAA2B,EAAc,CAAC;IACzH7T,IAAI,EAAEvI,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,8BAA8B;MACxCC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAE+H,kBAAkB;QAAE2L,WAAW,EAAEF;MAA4B,CAAC,CAAC;MACtFvE,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA,MAAM0E,0BAA0B,SAAS3L,oBAAoB,CAAC;EAC1DhM,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,MAAMiL,QAAQ,GAAG3P,MAAM,CAACM,QAAQ,CAAC;IACjC,IAAI,CAACmM,UAAU,GAAG,IAAIrM,UAAU,CAACuP,QAAQ,CAACG,eAAe,CAAC;IAC1D,IAAI,CAAChD,cAAc,GAAG6C,QAAQ;EAClC;EACAkE,yCAAyCA,CAACvF,IAAI,EAAE;IAC5C,OAAO,IAAI,CAACvC,aAAa,CAAC,CAAC,CAACC,aAAa,CAACgE,qBAAqB,CAAC,CAAC,CAAC1B,IAAI,CAAC;EAC3E;EACA,OAAOtG,IAAI,YAAAsU,mCAAApU,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmU,0BAA0B;EAAA;EAC7H,OAAOlU,IAAI,kBAjrC8ExI,EAAE,CAAAyI,iBAAA;IAAAC,IAAA,EAirCJgU,0BAA0B;IAAA/T,SAAA;IAAAE,QAAA,GAjrCxB7I,EAAE,CAAA8I,kBAAA,CAirC8G,CAAC;MAAEC,OAAO,EAAE+H,kBAAkB;MAAE2L,WAAW,EAAEC;IAA2B,CAAC,CAAC,GAjrC1L1c,EAAE,CAAAoR,0BAAA;EAAA;AAkrC/F;AACA;EAAA,QAAA1L,SAAA,oBAAAA,SAAA,KAnrC6F1F,EAAE,CAAAmJ,iBAAA,CAmrCJuT,0BAA0B,EAAc,CAAC;IACxHhU,IAAI,EAAEvI,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAE+H,kBAAkB;QAAE2L,WAAW,EAAEC;MAA2B,CAAC;IACxF,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAME,mBAAmB,CAAC;EACtB,OAAOvU,IAAI,YAAAwU,4BAAAtU,iBAAA;IAAA,YAAAA,iBAAA,IAAwFqU,mBAAmB;EAAA;EACtH,OAAOE,IAAI,kBA7rC8E9c,EAAE,CAAA+c,gBAAA;IAAArU,IAAA,EA6rCSkU;EAAmB;EACvH,OAAOI,IAAI,kBA9rC8Ehd,EAAE,CAAAid,gBAAA;AA+rC/F;AACA;EAAA,QAAAvX,SAAA,oBAAAA,SAAA,KAhsC6F1F,EAAE,CAAAmJ,iBAAA,CAgsCJyT,mBAAmB,EAAc,CAAC;IACjHlU,IAAI,EAAE/G,QAAQ;IACdyH,IAAI,EAAE,CAAC;MACC8T,OAAO,EAAE,CAACrQ,aAAa,CAAC;MACxBsQ,OAAO,EAAE,CAACtQ,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAMuQ,eAAe,CAAC;EAClB,OAAO/U,IAAI,YAAAgV,wBAAA9U,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6U,eAAe;EAAA;EAClH,OAAON,IAAI,kBA5sC8E9c,EAAE,CAAA+c,gBAAA;IAAArU,IAAA,EA4sCS0U;EAAe;EASnH,OAAOJ,IAAI,kBArtC8Ehd,EAAE,CAAAid,gBAAA;IAAAE,OAAA,GAqtCoC1Z,UAAU,EACjImZ,mBAAmB,EAAEnZ,UAAU,EAAEmZ,mBAAmB;EAAA;AAChE;AACA;EAAA,QAAAlX,SAAA,oBAAAA,SAAA,KAxtC6F1F,EAAE,CAAAmJ,iBAAA,CAwtCJiU,eAAe,EAAc,CAAC;IAC7G1U,IAAI,EAAE/G,QAAQ;IACdyH,IAAI,EAAE,CAAC;MACC+T,OAAO,EAAE,CACL1Z,UAAU,EACVmZ,mBAAmB,EACnBlL,wBAAwB,EACxBxJ,yBAAyB,EACzBsQ,eAAe,EACfkE,0BAA0B,EAC1BH,2BAA2B,CAC9B;MACDW,OAAO,EAAE,CACLzZ,UAAU,EACVmZ,mBAAmB,EACnB1U,yBAAyB,EACzBsQ,eAAe,EACf9G,wBAAwB,EACxBgL,0BAA0B,EAC1BH,2BAA2B;IAEnC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASrU,yBAAyB,EAAE2E,aAAa,EAAE+P,mBAAmB,EAAEpE,eAAe,EAAE9G,wBAAwB,EAAEX,oBAAoB,EAAEwL,2BAA2B,EAAEG,0BAA0B,EAAE3N,mBAAmB,EAAExF,mBAAmB,EAAEhF,8BAA8B,EAAEiF,gBAAgB,EAAE4T,eAAe,EAAEtM,kBAAkB,EAAExM,uBAAuB,EAAE0K,aAAa,EAAEjH,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}