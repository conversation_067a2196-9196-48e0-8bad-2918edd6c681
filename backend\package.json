{"name": "secure-backend", "version": "1.0.0", "description": "Secure modular backend with authentication and payments", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": "18 || 20"}, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "pretest": "npm run rebuild", "test": "echo \"Error: no test specified\" && exit 1", "premigrate": "npm run build", "migrate": "node ./dist/migrate", "prestart": "npm run rebuild", "start": "node -r source-map-support/register .", "dev": "nodemon --exec \"npm run start\"", "clean": "rm -rf dist *.tsbuildinfo", "rebuild": "npm run clean && npm run build"}, "repository": {"type": "git"}, "author": "", "license": "MIT", "files": ["README.md", "dist", "src", "!*/__tests__"], "keywords": ["loopback-application", "loopback", "authentication", "payments", "security"], "dependencies": {"@loopback/authentication": "^11.0.14", "@loopback/authentication-jwt": "^0.15.14", "@loopback/authorization": "^0.15.14", "@loopback/boot": "^7.0.14", "@loopback/build": "^11.0.12", "@loopback/context": "^7.0.14", "@loopback/core": "^6.1.11", "@loopback/eslint-config": "^15.0.5", "@loopback/filter": "^5.0.13", "@loopback/metadata": "^7.0.14", "@loopback/openapi-v3": "^10.0.14", "@loopback/repository": "^7.0.14", "@loopback/repository-json-schema": "^8.0.14", "@loopback/rest": "^14.0.14", "@loopback/rest-explorer": "^7.0.14", "@loopback/security": "^0.11.14", "@loopback/service-proxy": "^7.0.14", "@loopback/testlab": "^7.0.13", "@types/node": "^22.15.29", "@types/nodemailer": "^6.4.17", "ajv": "^8.12.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.3", "nodemon": "^3.1.10", "qrcode": "^1.5.4", "razorpay": "^2.9.6", "source-map-support": "^0.5.21", "speakeasy": "^2.0.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.18", "@types/express-rate-limit": "^5.1.3", "@types/jsonwebtoken": "^9.0.9", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10"}}