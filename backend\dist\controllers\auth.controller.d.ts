import { TokenService } from '@loopback/authentication';
import { UserProfile } from '@loopback/security';
import { User as UserModel } from '../models';
import { UserRepository as UserRepo } from '../repositories';
import { SecurityService, EmailService, SmsService } from '../services';
export declare class AuthController {
    jwtService: TokenService;
    user: UserProfile;
    protected userRepository: UserRepo;
    securityService?: SecurityService | undefined;
    emailService?: EmailService | undefined;
    smsService?: SmsService | undefined;
    constructor(jwtService: TokenService, user: UserProfile, userRepository: UserRepo, securityService?: SecurityService | undefined, emailService?: EmailService | undefined, smsService?: SmsService | undefined);
    signUp(newUserRequest: Omit<UserModel, 'id'> & {
        password: string;
    }): Promise<{
        message: string;
        userId: string;
    }>;
    login(credentials: {
        email: string;
        password: string;
        twoFactorToken?: string;
    }): Promise<{
        token: string;
        user: UserModel;
        requiresTwoFactor?: boolean;
    }>;
    verifyEmail(request: {
        token: string;
    }): Promise<{
        message: string;
    }>;
    forgotPassword(request: {
        email: string;
    }): Promise<{
        message: string;
    }>;
    resetPassword(request: {
        token: string;
        password: string;
    }): Promise<{
        message: string;
    }>;
    private verifyCredentials;
    private convertToUserProfile;
    private handleFailedLogin;
    private handleSuccessfulLogin;
}
