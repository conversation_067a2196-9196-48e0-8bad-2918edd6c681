#!/usr/bin/env node

/**
 * Payment Service Binding Test
 */

require('dotenv').config();

async function testPaymentServiceBinding() {
  console.log('🔍 TESTING PAYMENT SERVICE BINDING');
  console.log('===================================\n');

  try {
    // Force PostgreSQL usage
    process.env.USE_POSTGRESQL = 'true';
    
    const { SecureBackendApplication } = require('../dist/application');
    const app = new SecureBackendApplication({
      rest: {
        port: 0, // Use random port for testing
        host: 'localhost',
      },
    });

    console.log('🚀 Booting application...');
    await app.boot();
    console.log('✅ Application booted successfully');

    // Test PaymentService binding
    console.log('\n🔍 Testing PaymentService binding...');

    try {
      const paymentService = await app.get('services.PaymentService');
      console.log('✅ PaymentService bound:', !!paymentService);
      
      // Test Razorpay configuration
      console.log('\n🔍 Testing Razorpay configuration...');
      console.log('   RAZORPAY_KEY_ID:', process.env.RAZORPAY_KEY_ID || 'Not set');
      console.log('   RAZORPAY_KEY_SECRET:', process.env.RAZORPAY_KEY_SECRET ? 'Set' : 'Not set');
      
      // Test payment repository
      const { PaymentRepository } = require('../dist/repositories');
      const datasource = await app.get('datasources.db');
      const paymentRepository = new PaymentRepository(datasource);
      console.log('✅ PaymentRepository working:', !!paymentRepository);
      
      // Test payment count
      const paymentCount = await paymentRepository.count();
      console.log('✅ Database connection working, payment count:', paymentCount.count);
      
      // Test creating a payment order (this will test Razorpay API)
      console.log('\n🔍 Testing payment order creation...');
      try {
        const testOrder = await paymentService.createOrder(
          100, // 100 INR
          'INR',
          'test-user-id',
          'Test payment order'
        );
        console.log('✅ Payment order created successfully!');
        console.log('   Order ID:', testOrder.orderId);
        console.log('   Amount:', testOrder.amount);
        console.log('   Currency:', testOrder.currency);
        
      } catch (orderError) {
        console.log('❌ Payment order creation failed');
        console.log('   Error:', orderError.message);
        console.log('   Stack:', orderError.stack);
      }
      
    } catch (error) {
      console.log('❌ PaymentService error:', error.message);
      console.log('   Stack:', error.stack);
    }

    await app.stop();
    console.log('\n🎉 Payment service binding test completed!');

  } catch (error) {
    console.error('❌ Payment service binding test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

if (require.main === module) {
  testPaymentServiceBinding().catch(console.error);
}

module.exports = { testPaymentServiceBinding };
