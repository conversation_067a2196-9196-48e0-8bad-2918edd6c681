#!/usr/bin/env node

/**
 * Create Payment Table Script
 */

require('dotenv').config();

async function createPaymentTable() {
  console.log('🔍 CREATING PAYMENT TABLE');
  console.log('=========================\n');

  try {
    // Force PostgreSQL usage
    process.env.USE_POSTGRESQL = 'true';
    
    const { SecureBackendApplication } = require('../dist/application');
    const app = new SecureBackendApplication({
      rest: {
        port: 0, // Use random port for testing
        host: 'localhost',
      },
    });

    console.log('🚀 Booting application...');
    await app.boot();
    console.log('✅ Application booted successfully');

    // Get datasource
    const datasource = await app.get('datasources.db');
    console.log('✅ Database connection established');

    // Create payment table SQL
    const createPaymentTableSQL = `
      CREATE TABLE IF NOT EXISTS payment (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        razorpay_order_id VARCHAR(255) NOT NULL,
        razorpay_payment_id VARCHAR(255),
        razorpay_signature VARCHAR(255),
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(10) NOT NULL DEFAULT 'INR',
        status VARCHAR(50) NOT NULL DEFAULT 'pending',
        description TEXT,
        metadata JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        paid_at TIMESTAMP WITH TIME ZONE,
        user_id UUID NOT NULL REFERENCES "user"(id) ON DELETE CASCADE
      );
    `;

    console.log('🔍 Creating payment table...');
    await datasource.execute(createPaymentTableSQL);
    console.log('✅ Payment table created successfully!');

    // Create indexes
    const createIndexesSQL = [
      'CREATE INDEX IF NOT EXISTS idx_payment_user_id ON payment(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_payment_razorpay_order_id ON payment(razorpay_order_id);',
      'CREATE INDEX IF NOT EXISTS idx_payment_status ON payment(status);',
      'CREATE INDEX IF NOT EXISTS idx_payment_created_at ON payment(created_at);'
    ];

    console.log('🔍 Creating indexes...');
    for (const indexSQL of createIndexesSQL) {
      await datasource.execute(indexSQL);
    }
    console.log('✅ Indexes created successfully!');

    // Test table creation by counting rows
    const result = await datasource.execute('SELECT COUNT(*) FROM payment;');
    console.log('✅ Payment table is working, row count:', result[0].count);

    await app.stop();
    console.log('\n🎉 Payment table creation completed!');

  } catch (error) {
    console.error('❌ Payment table creation failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

if (require.main === module) {
  createPaymentTable().catch(console.error);
}

module.exports = { createPaymentTable };
