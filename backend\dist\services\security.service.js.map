{"version": 3, "file": "security.service.js", "sourceRoot": "", "sources": ["../../src/services/security.service.ts"], "names": [], "mappings": ";;;;AAAA,yCAAgE;AAChE,qDAAgD;AAChD,yCAA0C;AAC1C,6DAAuC;AACvC,uDAAiC;AACjC,uDAAiC;AACjC,uCAA8B;AAC9B,kDAA8D;AAIvD,IAAM,eAAe,GAArB,MAAM,eAAe;IAC1B,YACqC,cAA8B,EAC/B,aAA4B;QAD3B,mBAAc,GAAd,cAAc,CAAgB;QAC/B,kBAAa,GAAb,aAAa,CAAe;IAC7D,CAAC;IAEJ,KAAK,CAAC,uBAAuB,CAAC,MAAc;QAC1C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,iBAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,MAAM,GAAG,SAAS,CAAC,cAAc,CAAC;YACtC,IAAI,EAAE,cAAc,IAAI,CAAC,KAAK,GAAG;YACjC,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,EAAE;SACX,CAAC,CAAC;QAEH,0BAA0B;QAC1B,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE;YAC3C,eAAe,EAAE,MAAM,CAAC,MAAM;YAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,mBAAmB;QACnB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,WAAY,CAAC,CAAC;QAE3D,OAAO;YACL,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,MAAM;SACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,KAAa;QACtD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACnC,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;YACrC,MAAM,EAAE,IAAI,CAAC,eAAe;YAC5B,QAAQ,EAAE,QAAQ;YAClB,KAAK;YACL,MAAM,EAAE,CAAC,EAAE,sCAAsC;SAClD,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,KAAa;QACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE;YAC3C,gBAAgB,EAAE,IAAI;YACtB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,KAAa;QAClD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE;YAC3C,gBAAgB,EAAE,KAAK;YACvB,eAAe,EAAE,SAAS;YAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,UAAkB,EAAE,IAAY;QAChD,uBAAuB;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QACpE,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,aAAa;QAEtE,wDAAwD;QACxD,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;YACjC,UAAU;YACV,IAAI;YACJ,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;QAEH,iBAAiB;QACjB,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAC9B,UAAU;YACV,IAAI;YACJ,IAAI;YACJ,SAAS;SACV,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,IAAY,EAAE,IAAY;QAC5D,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,EAAE;gBACL,UAAU;gBACV,IAAI;gBACJ,IAAI;gBACJ,IAAI,EAAE,KAAK;gBACX,SAAS,EAAE,EAAC,EAAE,EAAE,IAAI,IAAI,EAAE,EAAC;aAC5B;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,uCAAuC;YACvC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;gBACnD,KAAK,EAAE;oBACL,UAAU;oBACV,IAAI;oBACJ,IAAI,EAAE,KAAK;iBACZ;aACF,CAAC,CAAC;YAEH,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,EAAE;oBAClD,QAAQ,EAAE,CAAC,WAAW,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC;iBAC1C,CAAC,CAAC;gBAEH,+BAA+B;gBAC/B,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrC,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,EAAE;wBAClD,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,IAAI,IAAI,EAAE;qBACnB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,mBAAmB;QACnB,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE;YAC1C,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,IAAI,IAAI,EAAE;SACnB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,0BAA0B;QAC9B,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,8BAA8B;QAClC,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,OAAO,IAAA,eAAI,EAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACnC,0CAA0C;QAC1C,MAAM,iBAAiB,GAAG;YACxB,kBAAkB;YAClB,mBAAmB;YACnB,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,eAAe;YACf,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,wBAAwB;SACzB,CAAC;QAEF,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC;QAClD,OAAO,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,QAAgB;QAC7C,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,uCAAuC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;CACF,CAAA;AA3MY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,iBAAU,EAAC,EAAC,KAAK,EAAE,mBAAY,CAAC,SAAS,EAAC,CAAC;IAGvC,mBAAA,IAAA,uBAAU,EAAC,6BAAc,CAAC,CAAA;IAC1B,mBAAA,IAAA,uBAAU,EAAC,4BAAa,CAAC,CAAA;6CADyB,6BAAc;QAChB,4BAAa;GAHrD,eAAe,CA2M3B"}